(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/constants/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "API_ENDPOINTS": ()=>API_ENDPOINTS,
    "APP_DESCRIPTION": ()=>APP_DESCRIPTION,
    "APP_NAME": ()=>APP_NAME,
    "CONTRACT_ADDRESSES": ()=>CONTRACT_ADDRESSES,
    "CURRENCIES": ()=>CURRENCIES,
    "DASHBOARD_NAV_ITEMS": ()=>DASHBOARD_NAV_ITEMS,
    "EVENT_TYPES": ()=>EVENT_TYPES,
    "FILE_UPLOAD": ()=>FILE_UPLOAD,
    "MAINTENANCE_CATEGORIES": ()=>MAINTENANCE_CATEGORIES,
    "MAINTENANCE_PRIORITIES": ()=>MAINTENANCE_PRIORITIES,
    "PAGINATION": ()=>PAGINATION,
    "PAYMENT_STATUSES": ()=>PAYMENT_STATUSES,
    "PROPERTY_STATUSES": ()=>PROPERTY_STATUSES,
    "PROPERTY_TYPES": ()=>PROPERTY_TYPES,
    "REWARD_SOURCES": ()=>REWARD_SOURCES,
    "ROLE_PERMISSIONS": ()=>ROLE_PERMISSIONS,
    "SOCIAL_LINKS": ()=>SOCIAL_LINKS,
    "STORAGE_KEYS": ()=>STORAGE_KEYS,
    "SUPPORTED_CHAINS": ()=>SUPPORTED_CHAINS,
    "THEME_COLORS": ()=>THEME_COLORS,
    "USER_ROLES": ()=>USER_ROLES,
    "VALIDATION": ()=>VALIDATION,
    "getRoleColor": ()=>getRoleColor,
    "getRoleDisplayName": ()=>getRoleDisplayName
});
const APP_NAME = 'ManageLife';
const APP_DESCRIPTION = 'Real Estate Tokenization Platform';
const SUPPORTED_CHAINS = {
    ETHEREUM: 1,
    POLYGON: 137,
    SEPOLIA: 11155111
};
const CONTRACT_ADDRESSES = {
    NFTi: '******************************************',
    NFTr: '******************************************',
    MLIFE_TOKEN: '******************************************',
    MARKETPLACE: '******************************************',
    REWARDS: '******************************************'
};
const USER_ROLES = [
    'homeowner',
    'renter',
    'buyer',
    'portfolio-manager',
    'community-member'
];
const ROLE_PERMISSIONS = {
    homeowner: [
        'create_property',
        'tokenize_property',
        'list_property',
        'manage_leases',
        'view_analytics',
        'request_maintenance'
    ],
    renter: [
        'browse_properties',
        'create_lease',
        'pay_rent',
        'request_maintenance',
        'view_lease_history'
    ],
    buyer: [
        'browse_marketplace',
        'purchase_property',
        'make_offers',
        'view_property_details',
        'save_favorites'
    ],
    'portfolio-manager': [
        'manage_multiple_properties',
        'view_portfolio_analytics',
        'manage_tenants',
        'handle_maintenance',
        'generate_reports'
    ],
    'community-member': [
        'view_events',
        'participate_discussions',
        'earn_rewards',
        'refer_users',
        'access_resources'
    ]
};
const PROPERTY_TYPES = [
    {
        value: 'house',
        label: 'House'
    },
    {
        value: 'apartment',
        label: 'Apartment'
    },
    {
        value: 'condo',
        label: 'Condominium'
    },
    {
        value: 'commercial',
        label: 'Commercial'
    }
];
const PROPERTY_STATUSES = [
    {
        value: 'available',
        label: 'Available',
        color: 'green'
    },
    {
        value: 'rented',
        label: 'Rented',
        color: 'blue'
    },
    {
        value: 'sold',
        label: 'Sold',
        color: 'gray'
    },
    {
        value: 'maintenance',
        label: 'Under Maintenance',
        color: 'yellow'
    }
];
const CURRENCIES = [
    {
        value: 'USD',
        label: 'US Dollar',
        symbol: '$'
    },
    {
        value: 'ETH',
        label: 'Ethereum',
        symbol: 'Ξ'
    },
    {
        value: 'MLIFE',
        label: 'ManageLife Token',
        symbol: '$MLIFE'
    }
];
const MAINTENANCE_CATEGORIES = [
    {
        value: 'plumbing',
        label: 'Plumbing'
    },
    {
        value: 'electrical',
        label: 'Electrical'
    },
    {
        value: 'hvac',
        label: 'HVAC'
    },
    {
        value: 'appliance',
        label: 'Appliance'
    },
    {
        value: 'structural',
        label: 'Structural'
    },
    {
        value: 'other',
        label: 'Other'
    }
];
const MAINTENANCE_PRIORITIES = [
    {
        value: 'low',
        label: 'Low',
        color: 'green'
    },
    {
        value: 'medium',
        label: 'Medium',
        color: 'yellow'
    },
    {
        value: 'high',
        label: 'High',
        color: 'orange'
    },
    {
        value: 'urgent',
        label: 'Urgent',
        color: 'red'
    }
];
const PAYMENT_STATUSES = [
    {
        value: 'pending',
        label: 'Pending',
        color: 'yellow'
    },
    {
        value: 'paid',
        label: 'Paid',
        color: 'green'
    },
    {
        value: 'overdue',
        label: 'Overdue',
        color: 'red'
    }
];
const REWARD_SOURCES = [
    {
        value: 'rent_payment',
        label: 'Rent Payment'
    },
    {
        value: 'referral',
        label: 'Referral'
    },
    {
        value: 'community_activity',
        label: 'Community Activity'
    },
    {
        value: 'staking',
        label: 'Staking'
    },
    {
        value: 'marketplace_activity',
        label: 'Marketplace Activity'
    }
];
const EVENT_TYPES = [
    {
        value: 'webinar',
        label: 'Webinar'
    },
    {
        value: 'meetup',
        label: 'Meetup'
    },
    {
        value: 'workshop',
        label: 'Workshop'
    },
    {
        value: 'announcement',
        label: 'Announcement'
    }
];
const SOCIAL_LINKS = {
    TELEGRAM: 'https://t.me/managelife',
    DISCORD: 'https://discord.gg/managelife',
    TWITTER: 'https://twitter.com/managelife',
    LINKEDIN: 'https://linkedin.com/company/managelife',
    MEDIUM: 'https://medium.com/@managelife'
};
const API_ENDPOINTS = {
    PROPERTIES: '/api/properties',
    USERS: '/api/users',
    LEASES: '/api/leases',
    MARKETPLACE: '/api/marketplace',
    REWARDS: '/api/rewards',
    MAINTENANCE: '/api/maintenance',
    EVENTS: '/api/events',
    AUTH: '/api/auth'
};
const STORAGE_KEYS = {
    USER_PREFERENCES: 'managelife_user_preferences',
    WALLET_CONNECTION: 'managelife_wallet_connection',
    THEME: 'managelife_theme',
    LANGUAGE: 'managelife_language'
};
const PAGINATION = {
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100
};
const FILE_UPLOAD = {
    MAX_SIZE: 10 * 1024 * 1024,
    ALLOWED_TYPES: [
        'image/jpeg',
        'image/png',
        'image/webp',
        'application/pdf'
    ],
    MAX_FILES: 10
};
const VALIDATION = {
    PASSWORD_MIN_LENGTH: 8,
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 50,
    DESCRIPTION_MAX_LENGTH: 1000,
    TITLE_MAX_LENGTH: 100
};
const THEME_COLORS = {
    PRIMARY: {
        50: '#eff6ff',
        100: '#dbeafe',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        900: '#1e3a8a'
    },
    SECONDARY: {
        50: '#faf5ff',
        100: '#f3e8ff',
        500: '#8b5cf6',
        600: '#7c3aed',
        700: '#6d28d9',
        900: '#4c1d95'
    }
};
const DASHBOARD_NAV_ITEMS = {
    homeowner: [
        {
            href: '/dashboard',
            label: 'Overview',
            icon: 'Home',
            tab: 'overview'
        },
        {
            href: '/dashboard?tab=properties',
            label: 'My Properties',
            icon: 'Building',
            tab: 'properties'
        },
        {
            href: '/dashboard?tab=rentals',
            label: 'Rental Management',
            icon: 'FileText',
            tab: 'rentals'
        },
        {
            href: '/dashboard?tab=nfts',
            label: 'My NFTs',
            icon: 'Coins',
            tab: 'nfts'
        },
        {
            href: '/dashboard?tab=notifications',
            label: 'Notifications',
            icon: 'Bell',
            tab: 'notifications'
        },
        {
            href: '/dashboard?tab=rewards',
            label: 'Rewards',
            icon: 'Gift',
            tab: 'rewards'
        },
        {
            href: '/dashboard?tab=analytics',
            label: 'Analytics',
            icon: 'BarChart3',
            tab: 'analytics'
        }
    ],
    renter: [
        {
            href: '/dashboard',
            label: 'Overview',
            icon: 'Home',
            tab: 'overview'
        },
        {
            href: '/dashboard?tab=rentals',
            label: 'My Rental',
            icon: 'FileText',
            tab: 'rentals'
        },
        {
            href: '/dashboard?tab=payments',
            label: 'Payments',
            icon: 'CreditCard',
            tab: 'payments'
        },
        {
            href: '/dashboard?tab=maintenance',
            label: 'Maintenance',
            icon: 'Wrench',
            tab: 'maintenance'
        },
        {
            href: '/dashboard?tab=notifications',
            label: 'Notifications',
            icon: 'Bell',
            tab: 'notifications'
        },
        {
            href: '/dashboard?tab=rewards',
            label: 'Rewards',
            icon: 'Gift',
            tab: 'rewards'
        }
    ],
    buyer: [
        {
            href: '/dashboard',
            label: 'Overview',
            icon: 'Home',
            tab: 'overview'
        },
        {
            href: '/dashboard?tab=marketplace',
            label: 'Browse Properties',
            icon: 'Search',
            tab: 'marketplace'
        },
        {
            href: '/dashboard?tab=favorites',
            label: 'Favorites',
            icon: 'Heart',
            tab: 'favorites'
        },
        {
            href: '/dashboard?tab=offers',
            label: 'My Offers',
            icon: 'HandCoins',
            tab: 'offers'
        },
        {
            href: '/dashboard?tab=notifications',
            label: 'Notifications',
            icon: 'Bell',
            tab: 'notifications'
        },
        {
            href: '/dashboard?tab=purchases',
            label: 'Purchases',
            icon: 'ShoppingCart',
            tab: 'purchases'
        }
    ],
    'portfolio-manager': [
        {
            href: '/dashboard',
            label: 'Overview',
            icon: 'Home',
            tab: 'overview'
        },
        {
            href: '/dashboard?tab=portfolio',
            label: 'Portfolio',
            icon: 'Building2',
            tab: 'portfolio'
        },
        {
            href: '/dashboard?tab=tenants',
            label: 'Tenants',
            icon: 'Users',
            tab: 'tenants'
        },
        {
            href: '/dashboard?tab=maintenance',
            label: 'Maintenance',
            icon: 'Wrench',
            tab: 'maintenance'
        },
        {
            href: '/dashboard?tab=notifications',
            label: 'Notifications',
            icon: 'Bell',
            tab: 'notifications'
        },
        {
            href: '/dashboard?tab=reports',
            label: 'Reports',
            icon: 'FileBarChart',
            tab: 'reports'
        },
        {
            href: '/dashboard?tab=analytics',
            label: 'Analytics',
            icon: 'BarChart3',
            tab: 'analytics'
        }
    ],
    'community-member': [
        {
            href: '/dashboard',
            label: 'Overview',
            icon: 'Home',
            tab: 'overview'
        },
        {
            href: '/dashboard?tab=community',
            label: 'Community',
            icon: 'Users',
            tab: 'community'
        },
        {
            href: '/dashboard?tab=events',
            label: 'Events',
            icon: 'Calendar',
            tab: 'events'
        },
        {
            href: '/dashboard?tab=notifications',
            label: 'Notifications',
            icon: 'Bell',
            tab: 'notifications'
        },
        {
            href: '/dashboard?tab=rewards',
            label: 'Rewards',
            icon: 'Gift',
            tab: 'rewards'
        },
        {
            href: '/dashboard?tab=referrals',
            label: 'Referrals',
            icon: 'UserPlus',
            tab: 'referrals'
        }
    ]
};
function getRoleDisplayName(role) {
    switch(role){
        case 'homeowner':
            return 'Homeowner';
        case 'renter':
            return 'Renter';
        case 'buyer':
            return 'Buyer';
        case 'portfolio-manager':
            return 'Portfolio Manager';
        case 'community-member':
            return 'Community Member';
        default:
            return role;
    }
}
function getRoleColor(role) {
    switch(role){
        case 'homeowner':
            return 'text-blue-600 bg-blue-100';
        case 'renter':
            return 'text-green-600 bg-green-100';
        case 'buyer':
            return 'text-purple-600 bg-purple-100';
        case 'portfolio-manager':
            return 'text-orange-600 bg-orange-100';
        case 'community-member':
            return 'text-pink-600 bg-pink-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useRewards.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLeaderboard": ()=>useLeaderboard,
    "useRewards": ()=>useRewards
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
function useRewards() {
    _s();
    const [rewards, setRewards] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [pendingRewards, setPendingRewards] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [stats, setStats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [rules, setRules] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Fetch user rewards
    const fetchRewards = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[fetchRewards]": async ()=>{
            try {
                setLoading(true);
                const response = await fetch('/api/rewards');
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to fetch rewards');
                }
                setRewards(data.rewards);
            } catch (err) {
                setError(err.message);
            } finally{
                setLoading(false);
            }
        }
    }["useRewards.useCallback[fetchRewards]"], []);
    // Fetch pending rewards
    const fetchPendingRewards = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[fetchPendingRewards]": async ()=>{
            try {
                const response = await fetch('/api/rewards?type=pending');
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to fetch pending rewards');
                }
                setPendingRewards(data.rewards);
            } catch (err) {
                setError(err.message);
            }
        }
    }["useRewards.useCallback[fetchPendingRewards]"], []);
    // Fetch user stats
    const fetchStats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[fetchStats]": async ()=>{
            try {
                const response = await fetch('/api/rewards?type=stats');
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to fetch stats');
                }
                setStats(data.stats);
            } catch (err) {
                setError(err.message);
            }
        }
    }["useRewards.useCallback[fetchStats]"], []);
    // Fetch reward rules
    const fetchRules = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[fetchRules]": async ()=>{
            try {
                const response = await fetch('/api/rewards?type=rules');
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to fetch rules');
                }
                setRules(data.rules);
            } catch (err) {
                setError(err.message);
            }
        }
    }["useRewards.useCallback[fetchRules]"], []);
    // Claim a specific reward
    const claimReward = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[claimReward]": async (rewardId)=>{
            try {
                setLoading(true);
                const response = await fetch('/api/rewards', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'claim',
                        rewardId
                    })
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to claim reward');
                }
                // Refresh data
                await Promise.all([
                    fetchRewards(),
                    fetchPendingRewards(),
                    fetchStats()
                ]);
                return data.reward;
            } catch (err) {
                setError(err.message);
                throw err;
            } finally{
                setLoading(false);
            }
        }
    }["useRewards.useCallback[claimReward]"], [
        fetchRewards,
        fetchPendingRewards,
        fetchStats
    ]);
    // Claim all pending rewards
    const claimAllRewards = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[claimAllRewards]": async ()=>{
            try {
                setLoading(true);
                const response = await fetch('/api/rewards', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'claim',
                        rewardId: 'all'
                    })
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to claim rewards');
                }
                // Refresh data
                await Promise.all([
                    fetchRewards(),
                    fetchPendingRewards(),
                    fetchStats()
                ]);
                return {
                    claimed: data.claimed,
                    total: data.total
                };
            } catch (err) {
                setError(err.message);
                throw err;
            } finally{
                setLoading(false);
            }
        }
    }["useRewards.useCallback[claimAllRewards]"], [
        fetchRewards,
        fetchPendingRewards,
        fetchStats
    ]);
    // Award a reward (for testing or admin purposes)
    const awardReward = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[awardReward]": async (type, metadata)=>{
            try {
                setLoading(true);
                const response = await fetch('/api/rewards', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'award',
                        type,
                        metadata
                    })
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to award reward');
                }
                // Refresh data
                await Promise.all([
                    fetchRewards(),
                    fetchPendingRewards(),
                    fetchStats()
                ]);
                return data.reward;
            } catch (err) {
                setError(err.message);
                throw err;
            } finally{
                setLoading(false);
            }
        }
    }["useRewards.useCallback[awardReward]"], [
        fetchRewards,
        fetchPendingRewards,
        fetchStats
    ]);
    // Clear error
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRewards.useCallback[clearError]": ()=>{
            setError(null);
        }
    }["useRewards.useCallback[clearError]"], []);
    // Initialize data on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRewards.useEffect": ()=>{
            Promise.all([
                fetchRewards(),
                fetchPendingRewards(),
                fetchStats(),
                fetchRules()
            ]);
        }
    }["useRewards.useEffect"], [
        fetchRewards,
        fetchPendingRewards,
        fetchStats,
        fetchRules
    ]);
    return {
        rewards,
        pendingRewards,
        stats,
        rules,
        loading,
        error,
        claimReward,
        claimAllRewards,
        awardReward,
        fetchRewards,
        fetchPendingRewards,
        fetchStats,
        clearError
    };
}
_s(useRewards, "I5JuhcGG0ZPzFEszsxI4dXCoFrA=");
function useLeaderboard() {
    _s1();
    const [leaderboard, setLeaderboard] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const fetchLeaderboard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLeaderboard.useCallback[fetchLeaderboard]": async function() {
            let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;
            try {
                setLoading(true);
                const response = await fetch("/api/rewards/leaderboard?limit=".concat(limit));
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to fetch leaderboard');
                }
                setLeaderboard(data.leaderboard);
            } catch (err) {
                setError(err.message);
            } finally{
                setLoading(false);
            }
        }
    }["useLeaderboard.useCallback[fetchLeaderboard]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useLeaderboard.useEffect": ()=>{
            fetchLeaderboard();
        }
    }["useLeaderboard.useEffect"], [
        fetchLeaderboard
    ]);
    return {
        leaderboard,
        loading,
        error,
        fetchLeaderboard
    };
}
_s1(useLeaderboard, "K29qPVE8n9aPWTChnJttBI3w428=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useWeb3.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useContractRead": ()=>useContractRead,
    "useMLifeToken": ()=>useMLifeToken,
    "useMarketplace": ()=>useMarketplace,
    "useNFTOperations": ()=>useNFTOperations,
    "usePropertyRegistry": ()=>usePropertyRegistry,
    "useWeb3": ()=>useWeb3
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useAccount$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/wagmi/dist/esm/hooks/useAccount.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/wagmi/dist/esm/hooks/useChainId.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useWriteContract$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/wagmi/dist/esm/hooks/useWriteContract.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useReadContract$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/wagmi/dist/esm/hooks/useReadContract.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$parseEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/unit/parseEther.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$formatEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/viem/_esm/utils/unit/formatEther.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/web3.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
;
;
;
;
function useWeb3() {
    _s();
    const { address, isConnected } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useAccount$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccount"])();
    const chainId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"])();
    const { writeContract } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useWriteContract$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWriteContract"])();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Helper function to handle contract writes
    const executeTransaction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWeb3.useCallback[executeTransaction]": async function(contractName, functionName) {
            let args = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [], value = arguments.length > 3 ? arguments[3] : void 0;
            if (!isConnected || !address) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Web3Error"]('Wallet not connected');
            }
            setIsLoading(true);
            setError(null);
            try {
                const contractAddress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getContractAddress"])(contractName, chainId);
                let abi;
                switch(contractName){
                    case 'NFTi':
                    case 'NFTr':
                        abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].NFT;
                        break;
                    case 'MLIFE_TOKEN':
                        abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].ERC20;
                        break;
                    case 'MARKETPLACE':
                        abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].MARKETPLACE;
                        break;
                    case 'PROPERTY_REGISTRY':
                        abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].PROPERTY_REGISTRY;
                        break;
                    default:
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Web3Error"]('Unknown contract');
                }
                const result = await writeContract({
                    address: contractAddress,
                    abi,
                    functionName,
                    args,
                    value
                });
                return result;
            } catch (err) {
                const errorMessage = err.message || 'Transaction failed';
                setError(errorMessage);
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Web3Error"](errorMessage, err.code, err.data);
            } finally{
                setIsLoading(false);
            }
        }
    }["useWeb3.useCallback[executeTransaction]"], [
        isConnected,
        address,
        chainId,
        writeContract
    ]);
    return {
        address,
        isConnected,
        chainId,
        isLoading,
        error,
        executeTransaction,
        clearError: ()=>setError(null)
    };
}
_s(useWeb3, "fx1MeCcPc09BNfRAdyNLxRW7UaE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useAccount$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccount"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useWriteContract$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWriteContract"]
    ];
});
function useContractRead(contractName, functionName) {
    let args = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];
    _s1();
    const chainId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"])();
    const contractAddress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getContractAddress"])(contractName, chainId);
    let abi;
    switch(contractName){
        case 'NFTi':
        case 'NFTr':
            abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].NFT;
            break;
        case 'MLIFE_TOKEN':
            abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].ERC20;
            break;
        case 'MARKETPLACE':
            abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].MARKETPLACE;
            break;
        case 'PROPERTY_REGISTRY':
            abi = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTRACT_ABIS"].PROPERTY_REGISTRY;
            break;
        default:
            throw new Error('Unknown contract');
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useReadContract$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReadContract"])({
        address: contractAddress,
        abi,
        functionName,
        args
    });
}
_s1(useContractRead, "5xcbLkraOQczvNNlFwTO/wiXeSE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useReadContract$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReadContract"]
    ];
});
function useMLifeToken() {
    _s2();
    const { executeTransaction } = useWeb3();
    const { address } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useAccount$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccount"])();
    const chainId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"])();
    // Get token balance
    const { data: balance, refetch: refetchBalance } = useContractRead('MLIFE_TOKEN', 'balanceOf', address ? [
        address
    ] : []);
    // Transfer tokens
    const transfer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMLifeToken.useCallback[transfer]": async (to, amount)=>{
            const amountWei = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$parseEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseEther"])(amount);
            return executeTransaction('MLIFE_TOKEN', 'transfer', [
                to,
                amountWei
            ]);
        }
    }["useMLifeToken.useCallback[transfer]"], [
        executeTransaction
    ]);
    // Approve tokens
    const approve = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMLifeToken.useCallback[approve]": async (spender, amount)=>{
            const amountWei = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$parseEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseEther"])(amount);
            return executeTransaction('MLIFE_TOKEN', 'approve', [
                spender,
                amountWei
            ]);
        }
    }["useMLifeToken.useCallback[approve]"], [
        executeTransaction
    ]);
    return {
        balance: balance ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$formatEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatEther"])(balance) : '0',
        transfer,
        approve,
        refetchBalance
    };
}
_s2(useMLifeToken, "ZnRq/XprPfajTRssiXSHyzRy558=", false, function() {
    return [
        useWeb3,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useAccount$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccount"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"],
        useContractRead
    ];
});
function useNFTOperations() {
    _s3();
    var _s = __turbopack_context__.k.signature();
    const { executeTransaction } = useWeb3();
    const { address } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useAccount$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccount"])();
    // Mint NFTi (Property NFT)
    const mintPropertyNFT = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNFTOperations.useCallback[mintPropertyNFT]": async (tokenId, tokenURI)=>{
            if (!address) throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Web3Error"]('Wallet not connected');
            return executeTransaction('NFTi', 'mint', [
                address,
                tokenId,
                tokenURI
            ]);
        }
    }["useNFTOperations.useCallback[mintPropertyNFT]"], [
        executeTransaction,
        address
    ]);
    // Mint NFTr (Rental NFT)
    const mintRentalNFT = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNFTOperations.useCallback[mintRentalNFT]": async (tokenId, tokenURI)=>{
            if (!address) throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$web3$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Web3Error"]('Wallet not connected');
            return executeTransaction('NFTr', 'mint', [
                address,
                tokenId,
                tokenURI
            ]);
        }
    }["useNFTOperations.useCallback[mintRentalNFT]"], [
        executeTransaction,
        address
    ]);
    // Transfer NFT
    const transferNFT = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNFTOperations.useCallback[transferNFT]": async (contractType, from, to, tokenId)=>{
            return executeTransaction(contractType, 'transferFrom', [
                from,
                to,
                tokenId
            ]);
        }
    }["useNFTOperations.useCallback[transferNFT]"], [
        executeTransaction
    ]);
    // Get NFT owner
    const getNFTOwner = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(_s({
        "useNFTOperations.useCallback[getNFTOwner]": (contractType, tokenId)=>{
            _s();
            return useContractRead(contractType, 'ownerOf', [
                tokenId
            ]);
        }
    }["useNFTOperations.useCallback[getNFTOwner]"], "/++im8S10zUYMTaBKH6SIfLPa2s=", false, {
        "useNFTOperations.useCallback[getNFTOwner]": function() {
            return [
                useContractRead
            ];
        }
    }["useNFTOperations.useCallback[getNFTOwner]"]), []);
    return {
        mintPropertyNFT,
        mintRentalNFT,
        transferNFT,
        getNFTOwner
    };
}
_s3(useNFTOperations, "kJ0bKk+my1Tj8UAIEy6zZFY77BE=", false, function() {
    return [
        useWeb3,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useAccount$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAccount"]
    ];
});
function useMarketplace() {
    _s4();
    var _s = __turbopack_context__.k.signature();
    const { executeTransaction } = useWeb3();
    const chainId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"])();
    const { data: rawListings, isLoading } = useContractRead('MARKETPLACE', 'getActiveListings', []);
    // List property for sale/rent
    const listProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMarketplace.useCallback[listProperty]": async (tokenId, price, isForRent)=>{
            const priceWei = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$parseEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseEther"])(price);
            return executeTransaction('MARKETPLACE', 'listProperty', [
                tokenId,
                priceWei,
                isForRent
            ]);
        }
    }["useMarketplace.useCallback[listProperty]"], [
        executeTransaction
    ]);
    // Buy property
    const buyProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMarketplace.useCallback[buyProperty]": async (listingId, price)=>{
            const priceWei = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$parseEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseEther"])(price);
            return executeTransaction('MARKETPLACE', 'buyProperty', [
                listingId
            ], priceWei);
        }
    }["useMarketplace.useCallback[buyProperty]"], [
        executeTransaction
    ]);
    // Rent property
    const rentProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMarketplace.useCallback[rentProperty]": async (listingId, duration, price)=>{
            const priceWei = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$parseEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseEther"])(price);
            return executeTransaction('MARKETPLACE', 'rentProperty', [
                listingId,
                duration
            ], priceWei);
        }
    }["useMarketplace.useCallback[rentProperty]"], [
        executeTransaction
    ]);
    // Cancel listing
    const cancelListing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMarketplace.useCallback[cancelListing]": async (listingId)=>{
            return executeTransaction('MARKETPLACE', 'cancelListing', [
                listingId
            ]);
        }
    }["useMarketplace.useCallback[cancelListing]"], [
        executeTransaction
    ]);
    // Get listing details
    const getListing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(_s({
        "useMarketplace.useCallback[getListing]": (listingId)=>{
            _s();
            return useContractRead('MARKETPLACE', 'getListing', [
                listingId
            ]);
        }
    }["useMarketplace.useCallback[getListing]"], "/++im8S10zUYMTaBKH6SIfLPa2s=", false, {
        "useMarketplace.useCallback[getListing]": function() {
            return [
                useContractRead
            ];
        }
    }["useMarketplace.useCallback[getListing]"]), []);
    // Make offer
    const makeOffer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMarketplace.useCallback[makeOffer]": async (listingId, offerPrice)=>{
            const offerWei = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$viem$2f$_esm$2f$utils$2f$unit$2f$parseEther$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseEther"])(offerPrice);
            return executeTransaction('MARKETPLACE', 'makeOffer', [
                listingId
            ], offerWei);
        }
    }["useMarketplace.useCallback[makeOffer]"], [
        executeTransaction
    ]);
    return {
        properties: rawListings || [],
        isLoading,
        listProperty,
        buyProperty,
        rentProperty,
        cancelListing,
        getListing,
        makeOffer
    };
}
_s4(useMarketplace, "2+GQ3IaCMPsZOhlzaZzvGcx3WF8=", false, function() {
    return [
        useWeb3,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$wagmi$2f$dist$2f$esm$2f$hooks$2f$useChainId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChainId"],
        useContractRead
    ];
});
function usePropertyRegistry() {
    _s5();
    var _s = __turbopack_context__.k.signature();
    const { executeTransaction } = useWeb3();
    // Register new property
    const registerProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyRegistry.useCallback[registerProperty]": async (propertyData, location)=>{
            return executeTransaction('PROPERTY_REGISTRY', 'registerProperty', [
                propertyData,
                location
            ]);
        }
    }["usePropertyRegistry.useCallback[registerProperty]"], [
        executeTransaction
    ]);
    // Tokenize property
    const tokenizeProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyRegistry.useCallback[tokenizeProperty]": async (propertyId, tokenURI)=>{
            return executeTransaction('PROPERTY_REGISTRY', 'tokenizeProperty', [
                propertyId,
                tokenURI
            ]);
        }
    }["usePropertyRegistry.useCallback[tokenizeProperty]"], [
        executeTransaction
    ]);
    // Get property details
    const getProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(_s({
        "usePropertyRegistry.useCallback[getProperty]": (propertyId)=>{
            _s();
            return useContractRead('PROPERTY_REGISTRY', 'getProperty', [
                propertyId
            ]);
        }
    }["usePropertyRegistry.useCallback[getProperty]"], "/++im8S10zUYMTaBKH6SIfLPa2s=", false, {
        "usePropertyRegistry.useCallback[getProperty]": function() {
            return [
                useContractRead
            ];
        }
    }["usePropertyRegistry.useCallback[getProperty]"]), []);
    // Update property
    const updateProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePropertyRegistry.useCallback[updateProperty]": async (propertyId, propertyData)=>{
            return executeTransaction('PROPERTY_REGISTRY', 'updateProperty', [
                propertyId,
                propertyData
            ]);
        }
    }["usePropertyRegistry.useCallback[updateProperty]"], [
        executeTransaction
    ]);
    return {
        registerProperty,
        tokenizeProperty,
        getProperty,
        updateProperty
    };
}
_s5(usePropertyRegistry, "uTp5CUR0sR8FatfeGCrrMcPI/qw=", false, function() {
    return [
        useWeb3
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateAnnualYield": ()=>calculateAnnualYield,
    "calculatePercentage": ()=>calculatePercentage,
    "cn": ()=>cn,
    "daysBetween": ()=>daysBetween,
    "debounce": ()=>debounce,
    "fileToBase64": ()=>fileToBase64,
    "formatCurrency": ()=>formatCurrency,
    "formatDate": ()=>formatDate,
    "formatDateTime": ()=>formatDateTime,
    "formatPropertyType": ()=>formatPropertyType,
    "formatTime": ()=>formatTime,
    "formatTimeAgo": ()=>formatTimeAgo,
    "formatWalletAddress": ()=>formatWalletAddress,
    "generateId": ()=>generateId,
    "getMaintenancePriorityColor": ()=>getMaintenancePriorityColor,
    "getPaymentStatusColor": ()=>getPaymentStatusColor,
    "getPropertyStatusColor": ()=>getPropertyStatusColor,
    "isOverdue": ()=>isOverdue,
    "isValidEmail": ()=>isValidEmail,
    "isValidEthereumAddress": ()=>isValidEthereumAddress,
    "sleep": ()=>sleep
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn() {
    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){
        inputs[_key] = arguments[_key];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount) {
    let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'USD';
    switch(currency){
        case 'USD':
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        case 'ETH':
            return "".concat(amount.toFixed(4), " ETH");
        case 'MLIFE':
            return "".concat(amount.toLocaleString(), " $MLIFE");
        default:
            return amount.toString();
    }
}
function formatWalletAddress(address) {
    let chars = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 4;
    if (!address) return '';
    return "".concat(address.slice(0, chars + 2), "...").concat(address.slice(-chars));
}
function formatDate(date) {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}
function formatTime(date) {
    const d = new Date(date);
    return d.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
}
function formatDateTime(date) {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
function formatTimeAgo(date) {
    const d = new Date(date);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);
    if (diffInSeconds < 60) {
        return 'just now';
    }
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
        return "".concat(diffInMinutes, " minute").concat(diffInMinutes > 1 ? 's' : '', " ago");
    }
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
        return "".concat(diffInHours, " hour").concat(diffInHours > 1 ? 's' : '', " ago");
    }
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
        return "".concat(diffInDays, " day").concat(diffInDays > 1 ? 's' : '', " ago");
    }
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
        return "".concat(diffInWeeks, " week").concat(diffInWeeks > 1 ? 's' : '', " ago");
    }
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
        return "".concat(diffInMonths, " month").concat(diffInMonths > 1 ? 's' : '', " ago");
    }
    const diffInYears = Math.floor(diffInDays / 365);
    return "".concat(diffInYears, " year").concat(diffInYears > 1 ? 's' : '', " ago");
}
function daysBetween(date1, date2) {
    const oneDay = 24 * 60 * 60 * 1000;
    return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
}
function isOverdue(dueDate) {
    return new Date() > new Date(dueDate);
}
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidEthereumAddress(address) {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
}
function calculatePercentage(value, total) {
    if (total === 0) return 0;
    return Math.round(value / total * 100);
}
function debounce(func, wait) {
    let timeout;
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function fileToBase64(file) {
    return new Promise((resolve, reject)=>{
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = ()=>resolve(reader.result);
        reader.onerror = (error)=>reject(error);
    });
}
function getPropertyStatusColor(status) {
    switch(status){
        case 'available':
            return 'text-green-600 bg-green-100';
        case 'rented':
            return 'text-blue-600 bg-blue-100';
        case 'sold':
            return 'text-gray-600 bg-gray-100';
        case 'maintenance':
            return 'text-yellow-600 bg-yellow-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
}
function getMaintenancePriorityColor(priority) {
    switch(priority){
        case 'urgent':
            return 'text-red-600 bg-red-100';
        case 'high':
            return 'text-orange-600 bg-orange-100';
        case 'medium':
            return 'text-yellow-600 bg-yellow-100';
        case 'low':
            return 'text-green-600 bg-green-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
}
function getPaymentStatusColor(status) {
    switch(status){
        case 'paid':
            return 'text-green-600 bg-green-100';
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'overdue':
            return 'text-red-600 bg-red-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
}
function calculateAnnualYield(monthlyRent, propertyValue) {
    if (propertyValue === 0) return 0;
    return monthlyRent * 12 / propertyValue * 100;
}
function formatPropertyType(type) {
    return type.charAt(0).toUpperCase() + type.slice(1);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_49944d03._.js.map