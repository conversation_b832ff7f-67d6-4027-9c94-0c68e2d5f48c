'use client';

import { useState } from 'react';
import { 
  <PERSON>,
  <PERSON>O<PERSON>,
  Check,
  X,
  Trash2,
  Filter,
  DollarSign,
  Home,
  Users,
  AlertTriangle,
  Info,
  CheckCircle,
  Calendar,
  Settings,
  Gift
} from 'lucide-react';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'payment' | 'maintenance' | 'community' | 'system' | 'reward' | 'alert';
  priority: 'low' | 'medium' | 'high';
  read: boolean;
  timestamp: string;
  actionUrl?: string;
  actionText?: string;
}

export default function NotificationsPanel() {
  const [filter, setFilter] = useState<'all' | 'unread' | 'payment' | 'maintenance' | 'community' | 'system' | 'reward' | 'alert'>('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  // Mock notifications data
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'Rent Payment Due',
      message: 'Your rent payment of $2,800 is due on February 1st, 2024.',
      type: 'payment',
      priority: 'high',
      read: false,
      timestamp: '2024-01-25T10:30:00Z',
      actionUrl: '/dashboard?tab=payments',
      actionText: 'Pay Now',
    },
    {
      id: '2',
      title: '$MLIFE Rewards Earned',
      message: 'You earned 150 $MLIFE tokens for timely rent payment!',
      type: 'reward',
      priority: 'medium',
      read: false,
      timestamp: '2024-01-24T14:15:00Z',
      actionUrl: '/dashboard?tab=rewards',
      actionText: 'View Rewards',
    },
    {
      id: '3',
      title: 'Maintenance Request Update',
      message: 'Your kitchen faucet repair has been scheduled for January 30th.',
      type: 'maintenance',
      priority: 'medium',
      read: true,
      timestamp: '2024-01-23T09:45:00Z',
      actionUrl: '/dashboard?tab=maintenance',
      actionText: 'View Details',
    },
    {
      id: '4',
      title: 'Community Event',
      message: 'Join our virtual real estate investment webinar this Friday at 2 PM.',
      type: 'community',
      priority: 'low',
      read: true,
      timestamp: '2024-01-22T16:20:00Z',
      actionUrl: '/dashboard?tab=community',
      actionText: 'Learn More',
    },
    {
      id: '5',
      title: 'System Maintenance',
      message: 'Scheduled maintenance will occur on January 28th from 2-4 AM EST.',
      type: 'system',
      priority: 'medium',
      read: false,
      timestamp: '2024-01-21T11:00:00Z',
    },
    {
      id: '6',
      title: 'Security Alert',
      message: 'New login detected from a different device. If this wasn\'t you, please secure your account.',
      type: 'alert',
      priority: 'high',
      read: true,
      timestamp: '2024-01-20T08:30:00Z',
      actionUrl: '/dashboard?tab=settings',
      actionText: 'Review Security',
    },
  ]);

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.read;
    return notification.type === filter;
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <DollarSign className="w-5 h-5 text-green-600" />;
      case 'maintenance':
        return <Home className="w-5 h-5 text-orange-600" />;
      case 'community':
        return <Users className="w-5 h-5 text-blue-600" />;
      case 'system':
        return <Settings className="w-5 h-5 text-gray-600" />;
      case 'reward':
        return <Gift className="w-5 h-5 text-purple-600" />;
      case 'alert':
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
      default:
        return <Info className="w-5 h-5 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500';
      case 'medium':
        return 'border-l-yellow-500';
      case 'low':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-300';
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const deleteSelected = () => {
    setNotifications(prev => 
      prev.filter(notification => !selectedNotifications.includes(notification.id))
    );
    setSelectedNotifications([]);
  };

  const toggleSelection = (id: string) => {
    setSelectedNotifications(prev =>
      prev.includes(id)
        ? prev.filter(notificationId => notificationId !== id)
        : [...prev, id]
    );
  };

  const selectAll = () => {
    setSelectedNotifications(filteredNotifications.map(n => n.id));
  };

  const clearSelection = () => {
    setSelectedNotifications([]);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-gray-900">Notifications</h2>
          {unreadCount > 0 && (
            <span className="bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
              {unreadCount} unread
            </span>
          )}
        </div>
        {unreadCount > 0 && (
          <button
            onClick={markAllAsRead}
            className="text-blue-600 hover:text-blue-700 font-medium text-sm"
          >
            Mark all as read
          </button>
        )}
      </div>

      {/* Filters and Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {[
              { id: 'all', label: 'All', count: notifications.length },
              { id: 'unread', label: 'Unread', count: unreadCount },
              { id: 'payment', label: 'Payments', count: notifications.filter(n => n.type === 'payment').length },
              { id: 'maintenance', label: 'Maintenance', count: notifications.filter(n => n.type === 'maintenance').length },
              { id: 'community', label: 'Community', count: notifications.filter(n => n.type === 'community').length },
              { id: 'reward', label: 'Rewards', count: notifications.filter(n => n.type === 'reward').length },
            ].map((filterOption) => (
              <button
                key={filterOption.id}
                onClick={() => setFilter(filterOption.id as any)}
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors ${
                  filter === filterOption.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                {filterOption.label} ({filterOption.count})
              </button>
            ))}
          </div>

          {selectedNotifications.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {selectedNotifications.length} selected
              </span>
              <button
                onClick={deleteSelected}
                className="text-red-600 hover:text-red-700 p-1"
              >
                <Trash2 className="w-4 h-4" />
              </button>
              <button
                onClick={clearSelection}
                className="text-gray-600 hover:text-gray-700 p-1"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        {filteredNotifications.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              <button
                onClick={selectedNotifications.length === filteredNotifications.length ? clearSelection : selectAll}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                {selectedNotifications.length === filteredNotifications.length ? 'Deselect All' : 'Select All'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {filteredNotifications.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
            <p className="text-gray-600">
              {filter === 'unread' 
                ? "You're all caught up! No unread notifications."
                : `No ${filter === 'all' ? '' : filter} notifications found.`
              }
            </p>
          </div>
        ) : (
          filteredNotifications.map((notification) => (
            <div
              key={notification.id}
              className={`bg-white rounded-xl shadow-sm border border-gray-200 border-l-4 ${getPriorityColor(notification.priority)} p-6 hover:shadow-md transition-shadow ${
                !notification.read ? 'bg-blue-50' : ''
              }`}
            >
              <div className="flex items-start space-x-4">
                <input
                  type="checkbox"
                  checked={selectedNotifications.includes(notification.id)}
                  onChange={() => toggleSelection(notification.id)}
                  className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                
                <div className="flex-shrink-0 mt-1">
                  {getNotificationIcon(notification.type)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                        {notification.title}
                        {!notification.read && (
                          <span className="ml-2 w-2 h-2 bg-blue-600 rounded-full inline-block"></span>
                        )}
                      </h3>
                      <p className="mt-1 text-sm text-gray-600">{notification.message}</p>
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                        <span>{new Date(notification.timestamp).toLocaleString()}</span>
                        <span className="capitalize">{notification.type}</span>
                        <span className="capitalize">{notification.priority} priority</span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {notification.actionUrl && (
                        <button
                          onClick={() => {
                            // Handle navigation to specific tab
                            const url = new URL(notification.actionUrl!, window.location.origin);
                            const tab = url.searchParams.get('tab');
                            if (tab) {
                              // This would trigger tab change in parent component
                              window.dispatchEvent(new CustomEvent('changeTab', { detail: tab }));
                            }
                          }}
                          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                          {notification.actionText || 'View'}
                        </button>
                      )}
                      
                      {!notification.read && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="text-gray-400 hover:text-blue-600 p-1"
                          title="Mark as read"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={() => deleteNotification(notification.id)}
                        className="text-gray-400 hover:text-red-600 p-1"
                        title="Delete notification"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
