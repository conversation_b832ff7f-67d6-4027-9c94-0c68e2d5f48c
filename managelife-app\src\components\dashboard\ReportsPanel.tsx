'use client';

import { useState } from 'react';
import { 
  FileBarChart,
  Download,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Building2,
  Users,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON>,
  RefreshCw,
  Eye,
  Share2,
  FileText,
  Calculator,
  Target,
  AlertCircle
} from 'lucide-react';

interface ReportData {
  id: string;
  title: string;
  type: 'financial' | 'occupancy' | 'maintenance' | 'performance';
  period: string;
  generatedDate: string;
  status: 'ready' | 'generating' | 'error';
  summary: {
    totalRevenue?: number;
    totalExpenses?: number;
    netIncome?: number;
    occupancyRate?: number;
    averageRent?: number;
    maintenanceCosts?: number;
    roi?: number;
  };
  charts?: {
    type: 'line' | 'bar' | 'pie';
    data: any[];
  }[];
}

export default function ReportsPanel() {
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month');
  const [selectedType, setSelectedType] = useState<'all' | 'financial' | 'occupancy' | 'maintenance' | 'performance'>('all');
  const [activeTab, setActiveTab] = useState<'overview' | 'financial' | 'occupancy' | 'maintenance'>('overview');

  // Mock reports data
  const [reports] = useState<ReportData[]>([
    {
      id: '1',
      title: 'Monthly Financial Report',
      type: 'financial',
      period: 'January 2024',
      generatedDate: '2024-01-31T23:59:59Z',
      status: 'ready',
      summary: {
        totalRevenue: 12600,
        totalExpenses: 3200,
        netIncome: 9400,
        roi: 7.5
      }
    },
    {
      id: '2',
      title: 'Occupancy Analysis',
      type: 'occupancy',
      period: 'Q4 2023',
      generatedDate: '2024-01-15T10:30:00Z',
      status: 'ready',
      summary: {
        occupancyRate: 92,
        averageRent: 3150
      }
    },
    {
      id: '3',
      title: 'Maintenance Cost Report',
      type: 'maintenance',
      period: 'December 2023',
      generatedDate: '2024-01-05T14:20:00Z',
      status: 'ready',
      summary: {
        maintenanceCosts: 1850,
        totalExpenses: 3200
      }
    },
    {
      id: '4',
      title: 'Portfolio Performance',
      type: 'performance',
      period: '2023 Annual',
      generatedDate: '2024-01-01T00:00:00Z',
      status: 'generating',
      summary: {
        totalRevenue: 145000,
        netIncome: 98000,
        roi: 8.2
      }
    }
  ]);

  // Mock current period data
  const currentPeriodData = {
    totalRevenue: 12600,
    totalExpenses: 3200,
    netIncome: 9400,
    occupancyRate: 92,
    averageRent: 3150,
    maintenanceCosts: 1850,
    roi: 7.5,
    properties: 4,
    tenants: 4,
    vacantUnits: 0,
    maintenanceRequests: 8
  };

  const filteredReports = reports.filter(report => {
    return selectedType === 'all' || report.type === selectedType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return 'bg-green-100 text-green-800';
      case 'generating':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'financial':
        return <DollarSign className="w-5 h-5 text-green-600" />;
      case 'occupancy':
        return <Users className="w-5 h-5 text-blue-600" />;
      case 'maintenance':
        return <Building2 className="w-5 h-5 text-orange-600" />;
      case 'performance':
        return <TrendingUp className="w-5 h-5 text-purple-600" />;
      default:
        return <FileBarChart className="w-5 h-5 text-gray-600" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Reports & Analytics</h2>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
            <RefreshCw className="w-4 h-4 mr-2" />
            Generate Report
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'financial', label: 'Financial', icon: DollarSign },
              { id: 'occupancy', label: 'Occupancy', icon: Users },
              { id: 'maintenance', label: 'Maintenance', icon: Building2 },
            ].map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-600">Total Revenue</p>
                      <p className="text-2xl font-bold text-green-900">{formatCurrency(currentPeriodData.totalRevenue)}</p>
                    </div>
                    <div className="w-12 h-12 bg-green-200 rounded-lg flex items-center justify-center">
                      <TrendingUp className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-green-600 font-medium">+12.5%</span>
                    <span className="text-green-600 ml-1">vs last month</span>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-600">Net Income</p>
                      <p className="text-2xl font-bold text-blue-900">{formatCurrency(currentPeriodData.netIncome)}</p>
                    </div>
                    <div className="w-12 h-12 bg-blue-200 rounded-lg flex items-center justify-center">
                      <DollarSign className="w-6 h-6 text-blue-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingUp className="w-4 h-4 text-blue-500 mr-1" />
                    <span className="text-blue-600 font-medium">+8.3%</span>
                    <span className="text-blue-600 ml-1">vs last month</span>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-600">Occupancy Rate</p>
                      <p className="text-2xl font-bold text-purple-900">{currentPeriodData.occupancyRate}%</p>
                    </div>
                    <div className="w-12 h-12 bg-purple-200 rounded-lg flex items-center justify-center">
                      <Users className="w-6 h-6 text-purple-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingUp className="w-4 h-4 text-purple-500 mr-1" />
                    <span className="text-purple-600 font-medium">+2.1%</span>
                    <span className="text-purple-600 ml-1">vs last month</span>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-6 border border-orange-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-orange-600">ROI</p>
                      <p className="text-2xl font-bold text-orange-900">{currentPeriodData.roi}%</p>
                    </div>
                    <div className="w-12 h-12 bg-orange-200 rounded-lg flex items-center justify-center">
                      <Target className="w-6 h-6 text-orange-600" />
                    </div>
                  </div>
                  <div className="mt-4 flex items-center text-sm">
                    <TrendingUp className="w-4 h-4 text-orange-500 mr-1" />
                    <span className="text-orange-600 font-medium">+0.5%</span>
                    <span className="text-orange-600 ml-1">vs last month</span>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <Building2 className="w-8 h-8 text-blue-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Properties</p>
                      <p className="text-xl font-bold text-gray-900">{currentPeriodData.properties}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <Users className="w-8 h-8 text-green-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Active Tenants</p>
                      <p className="text-xl font-bold text-gray-900">{currentPeriodData.tenants}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertCircle className="w-8 h-8 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Vacant Units</p>
                      <p className="text-xl font-bold text-gray-900">{currentPeriodData.vacantUnits}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <FileText className="w-8 h-8 text-purple-600 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Maintenance</p>
                      <p className="text-xl font-bold text-gray-900">{currentPeriodData.maintenanceRequests}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Financial Tab */}
          {activeTab === 'financial' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Breakdown</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Rental Income</span>
                      <span className="font-semibold">{formatCurrency(11200)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Late Fees</span>
                      <span className="font-semibold">{formatCurrency(150)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Other Income</span>
                      <span className="font-semibold">{formatCurrency(1250)}</span>
                    </div>
                    <div className="border-t pt-3 flex justify-between font-bold">
                      <span>Total Revenue</span>
                      <span>{formatCurrency(currentPeriodData.totalRevenue)}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Expense Breakdown</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Maintenance</span>
                      <span className="font-semibold">{formatCurrency(1850)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Property Tax</span>
                      <span className="font-semibold">{formatCurrency(800)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Insurance</span>
                      <span className="font-semibold">{formatCurrency(350)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Management</span>
                      <span className="font-semibold">{formatCurrency(200)}</span>
                    </div>
                    <div className="border-t pt-3 flex justify-between font-bold">
                      <span>Total Expenses</span>
                      <span>{formatCurrency(currentPeriodData.totalExpenses)}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Profitability</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Gross Income</span>
                      <span className="font-semibold">{formatCurrency(currentPeriodData.totalRevenue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Expenses</span>
                      <span className="font-semibold text-red-600">-{formatCurrency(currentPeriodData.totalExpenses)}</span>
                    </div>
                    <div className="border-t pt-3 flex justify-between font-bold text-green-600">
                      <span>Net Income</span>
                      <span>{formatCurrency(currentPeriodData.netIncome)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Profit Margin</span>
                      <span className="font-semibold">{((currentPeriodData.netIncome / currentPeriodData.totalRevenue) * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Other tabs would have similar content */}
          {activeTab === 'occupancy' && (
            <div className="text-center py-12">
              <PieChart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Occupancy Analytics</h3>
              <p className="text-gray-600">Detailed occupancy reports and trends coming soon.</p>
            </div>
          )}

          {activeTab === 'maintenance' && (
            <div className="text-center py-12">
              <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Maintenance Reports</h3>
              <p className="text-gray-600">Maintenance cost analysis and trends coming soon.</p>
            </div>
          )}
        </div>
      </div>

      {/* Generated Reports */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Generated Reports</h3>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Types</option>
            <option value="financial">Financial</option>
            <option value="occupancy">Occupancy</option>
            <option value="maintenance">Maintenance</option>
            <option value="performance">Performance</option>
          </select>
        </div>

        <div className="space-y-4">
          {filteredReports.map((report) => (
            <div key={report.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="mt-1">
                    {getTypeIcon(report.type)}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{report.title}</h4>
                    <p className="text-sm text-gray-600">{report.period}</p>
                    <p className="text-xs text-gray-500">Generated on {formatDate(report.generatedDate)}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                    {report.status === 'generating' && <RefreshCw className="w-3 h-3 mr-1 animate-spin" />}
                    {report.status}
                  </span>
                  {report.status === 'ready' && (
                    <div className="flex space-x-2">
                      <button className="text-gray-600 hover:text-gray-700 p-1 rounded">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-700 p-1 rounded">
                        <Download className="w-4 h-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-700 p-1 rounded">
                        <Share2 className="w-4 h-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
              
              {report.summary && report.status === 'ready' && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
                  {report.summary.totalRevenue && (
                    <div>
                      <p className="text-xs text-gray-500">Revenue</p>
                      <p className="font-semibold">{formatCurrency(report.summary.totalRevenue)}</p>
                    </div>
                  )}
                  {report.summary.netIncome && (
                    <div>
                      <p className="text-xs text-gray-500">Net Income</p>
                      <p className="font-semibold">{formatCurrency(report.summary.netIncome)}</p>
                    </div>
                  )}
                  {report.summary.occupancyRate && (
                    <div>
                      <p className="text-xs text-gray-500">Occupancy</p>
                      <p className="font-semibold">{report.summary.occupancyRate}%</p>
                    </div>
                  )}
                  {report.summary.roi && (
                    <div>
                      <p className="text-xs text-gray-500">ROI</p>
                      <p className="font-semibold">{report.summary.roi}%</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
