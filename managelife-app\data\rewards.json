[{"id": "md4n08st8kflrl4oobj", "userId": "md4l5sjobzt3qb9dhw", "amount": 1000, "source": "welcome_bonus", "description": "Welcome to ManageLife! Here's your starter bonus.", "status": "claimed", "createdAt": "2025-07-15T14:38:07.757Z", "expiresAt": "2025-08-14T14:38:07.757Z", "claimedAt": "2025-07-15T14:38:10.669Z"}, {"id": "md4n0d9wfukmy6clcpv", "userId": "md4l5sjobzt3qb9dhw", "amount": 50, "source": "rent_payment", "description": "Reward for paying rent on time", "status": "claimed", "createdAt": "2025-07-15T14:38:13.556Z", "expiresAt": "2025-08-14T14:38:13.556Z", "claimedAt": "2025-07-15T14:38:23.784Z"}, {"id": "md4n0fhrcobywjdgesw", "userId": "md4l5sjobzt3qb9dhw", "amount": 100, "source": "property_listing", "description": "Reward for listing a property", "status": "claimed", "createdAt": "2025-07-15T14:38:16.431Z", "expiresAt": "2025-08-14T14:38:16.431Z", "claimedAt": "2025-07-15T14:38:23.785Z"}, {"id": "md4n0naacewj17xamgj", "userId": "md4l5sjobzt3qb9dhw", "amount": 150, "source": "kyc_completion", "description": "Reward for completing KYC verification", "status": "claimed", "createdAt": "2025-07-15T14:38:26.530Z", "expiresAt": "2025-08-14T14:38:26.530Z", "claimedAt": "2025-07-22T16:33:26.521Z"}, {"id": "md4n0ojyybmdeb1eeyr", "userId": "md4l5sjobzt3qb9dhw", "amount": 50, "source": "rent_payment", "description": "Reward for paying rent on time", "status": "pending", "createdAt": "2025-07-15T14:38:28.174Z", "expiresAt": "2025-08-14T14:38:28.174Z"}]