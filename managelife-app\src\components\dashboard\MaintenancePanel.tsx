'use client';

import { useState } from 'react';
import { 
  Wrench,
  Plus,
  Search,
  Filter,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  Phone,
  Mail,
  MapPin,
  Camera,
  MessageSquare,
  Star,
  Upload,
  Eye,
  Edit,
  X
} from 'lucide-react';

interface MaintenanceRequest {
  id: string;
  title: string;
  description: string;
  category: 'plumbing' | 'electrical' | 'hvac' | 'appliance' | 'structural' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'submitted' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  submittedDate: string;
  scheduledDate?: string;
  completedDate?: string;
  property: string;
  assignedTo?: string;
  contactInfo?: string;
  estimatedCost?: number;
  actualCost?: number;
  images?: string[];
  notes?: string;
  rating?: number;
  feedback?: string;
}

export default function MaintenancePanel() {
  const [filterStatus, setFilterStatus] = useState<'all' | 'submitted' | 'scheduled' | 'in_progress' | 'completed'>('all');
  const [filterPriority, setFilterPriority] = useState<'all' | 'low' | 'medium' | 'high' | 'urgent'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showNewRequestForm, setShowNewRequestForm] = useState(false);

  // Mock maintenance requests data
  const [requests] = useState<MaintenanceRequest[]>([
    {
      id: '1',
      title: 'Kitchen Faucet Leak',
      description: 'The kitchen faucet has been dripping constantly for the past week. Water is pooling under the sink.',
      category: 'plumbing',
      priority: 'medium',
      status: 'scheduled',
      submittedDate: '2024-01-20T10:30:00Z',
      scheduledDate: '2024-01-30T14:00:00Z',
      property: '123 Main St, Downtown, NY 10001',
      assignedTo: 'Mike Johnson - Plumbing Pro',
      contactInfo: '(555) 123-4567',
      estimatedCost: 150,
      images: ['/api/placeholder/300/200'],
    },
    {
      id: '2',
      title: 'Broken Light Switch',
      description: 'Living room light switch is not working. Lights won\'t turn on.',
      category: 'electrical',
      priority: 'high',
      status: 'in_progress',
      submittedDate: '2024-01-18T16:45:00Z',
      scheduledDate: '2024-01-25T09:00:00Z',
      property: '123 Main St, Downtown, NY 10001',
      assignedTo: 'Sarah Electric Services',
      contactInfo: '(555) 987-6543',
      estimatedCost: 75,
    },
    {
      id: '3',
      title: 'AC Not Cooling',
      description: 'Air conditioning unit is running but not cooling the apartment effectively.',
      category: 'hvac',
      priority: 'urgent',
      status: 'submitted',
      submittedDate: '2024-01-22T08:15:00Z',
      property: '123 Main St, Downtown, NY 10001',
    },
    {
      id: '4',
      title: 'Dishwasher Repair',
      description: 'Dishwasher is making loud noises and not cleaning dishes properly.',
      category: 'appliance',
      priority: 'low',
      status: 'completed',
      submittedDate: '2024-01-10T12:00:00Z',
      scheduledDate: '2024-01-15T10:00:00Z',
      completedDate: '2024-01-15T11:30:00Z',
      property: '123 Main St, Downtown, NY 10001',
      assignedTo: 'Appliance Fix Co.',
      contactInfo: '(*************',
      estimatedCost: 120,
      actualCost: 95,
      rating: 5,
      feedback: 'Excellent service! Fixed quickly and professionally.',
    },
  ]);

  const filteredRequests = requests.filter(request => {
    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || request.status === filterStatus;
    const matchesPriority = filterPriority === 'all' || request.priority === filterPriority;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-orange-100 text-orange-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'plumbing':
        return '🔧';
      case 'electrical':
        return '⚡';
      case 'hvac':
        return '❄️';
      case 'appliance':
        return '🏠';
      case 'structural':
        return '🏗️';
      default:
        return '🔨';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const activeRequests = requests.filter(r => ['submitted', 'scheduled', 'in_progress'].includes(r.status)).length;
  const completedRequests = requests.filter(r => r.status === 'completed').length;
  const urgentRequests = requests.filter(r => r.priority === 'urgent' && r.status !== 'completed').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Maintenance Requests</h2>
        <button 
          onClick={() => setShowNewRequestForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Request
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Requests</p>
              <p className="text-2xl font-bold text-gray-900">{activeRequests}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-gray-900">{completedRequests}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Urgent</p>
              <p className="text-2xl font-bold text-gray-900">{urgentRequests}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="submitted">Submitted</option>
              <option value="scheduled">Scheduled</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>

            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Priority</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
        </div>
      </div>

      {/* Requests List */}
      <div className="space-y-4">
        {filteredRequests.map((request) => (
          <div key={request.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4 flex-1">
                <div className="text-2xl">{getCategoryIcon(request.category)}</div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{request.title}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                      {request.status.replace('_', ' ')}
                    </span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getPriorityColor(request.priority)}`}>
                      {request.priority}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{request.description}</p>
                  <p className="text-sm text-gray-500 mb-4 flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {request.property}
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Submitted</p>
                      <p className="font-medium">{formatDate(request.submittedDate)}</p>
                    </div>
                    {request.scheduledDate && (
                      <div>
                        <p className="text-gray-500">Scheduled</p>
                        <p className="font-medium">{formatDate(request.scheduledDate)}</p>
                      </div>
                    )}
                    {request.completedDate && (
                      <div>
                        <p className="text-gray-500">Completed</p>
                        <p className="font-medium">{formatDate(request.completedDate)}</p>
                      </div>
                    )}
                  </div>

                  {request.assignedTo && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm font-medium text-gray-900 flex items-center">
                        <User className="w-4 h-4 mr-2" />
                        {request.assignedTo}
                      </p>
                      {request.contactInfo && (
                        <p className="text-sm text-gray-600 flex items-center mt-1">
                          <Phone className="w-4 h-4 mr-2" />
                          {request.contactInfo}
                        </p>
                      )}
                    </div>
                  )}

                  {request.rating && (
                    <div className="mt-4 p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm font-medium text-gray-900">Rating:</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < request.rating! ? 'text-yellow-400 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      {request.feedback && (
                        <p className="text-sm text-gray-600">{request.feedback}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex flex-col items-end space-y-2 ml-4">
                {request.estimatedCost && (
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {request.actualCost ? 'Actual Cost' : 'Estimated Cost'}
                    </p>
                    <p className="font-semibold text-gray-900">
                      ${(request.actualCost || request.estimatedCost).toLocaleString()}
                    </p>
                  </div>
                )}
                
                <div className="flex space-x-2">
                  <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                  {request.status !== 'completed' && (
                    <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredRequests.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Wrench className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No maintenance requests found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterStatus !== 'all' || filterPriority !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'No maintenance requests submitted yet.'
            }
          </p>
          <button 
            onClick={() => setShowNewRequestForm(true)}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Submit Request
          </button>
        </div>
      )}

      {/* New Request Form Modal would go here */}
      {showNewRequestForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">New Maintenance Request</h3>
              <button 
                onClick={() => setShowNewRequestForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <p className="text-gray-600 text-center py-8">
              Request form would be implemented here with fields for title, description, category, priority, and image upload.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
