{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/constants/index.ts"], "sourcesContent": ["import { UserRole } from '@/types';\n\n// Application constants\nexport const APP_NAME = 'ManageLife';\nexport const APP_DESCRIPTION = 'Real Estate Tokenization Platform';\n\n// Supported chains\nexport const SUPPORTED_CHAINS = {\n  ETHEREUM: 1,\n  POLYGON: 137,\n  SEPOLIA: 11155111, // Testnet\n} as const;\n\n// Contract addresses (placeholder - replace with actual deployed contracts)\nexport const CONTRACT_ADDRESSES = {\n  NFTi: '******************************************',\n  NFTr: '******************************************',\n  MLIFE_TOKEN: '******************************************',\n  MARKETPLACE: '******************************************',\n  REWARDS: '******************************************',\n} as const;\n\n// User roles\nexport const USER_ROLES: UserRole[] = [\n  'homeowner',\n  'renter',\n  'buyer',\n  'portfolio-manager',\n  'community-member',\n];\n\n// Role permissions\nexport const ROLE_PERMISSIONS = {\n  homeowner: [\n    'create_property',\n    'tokenize_property',\n    'list_property',\n    'manage_leases',\n    'view_analytics',\n    'request_maintenance',\n  ],\n  renter: [\n    'browse_properties',\n    'create_lease',\n    'pay_rent',\n    'request_maintenance',\n    'view_lease_history',\n  ],\n  buyer: [\n    'browse_marketplace',\n    'purchase_property',\n    'make_offers',\n    'view_property_details',\n    'save_favorites',\n  ],\n  'portfolio-manager': [\n    'manage_multiple_properties',\n    'view_portfolio_analytics',\n    'manage_tenants',\n    'handle_maintenance',\n    'generate_reports',\n  ],\n  'community-member': [\n    'view_events',\n    'participate_discussions',\n    'earn_rewards',\n    'refer_users',\n    'access_resources',\n  ],\n} as const;\n\n// Property types\nexport const PROPERTY_TYPES = [\n  { value: 'house', label: 'House' },\n  { value: 'apartment', label: 'Apartment' },\n  { value: 'condo', label: 'Condominium' },\n  { value: 'commercial', label: 'Commercial' },\n] as const;\n\n// Property statuses\nexport const PROPERTY_STATUSES = [\n  { value: 'available', label: 'Available', color: 'green' },\n  { value: 'rented', label: 'Rented', color: 'blue' },\n  { value: 'sold', label: 'Sold', color: 'gray' },\n  { value: 'maintenance', label: 'Under Maintenance', color: 'yellow' },\n] as const;\n\n// Currencies\nexport const CURRENCIES = [\n  { value: 'USD', label: 'US Dollar', symbol: '$' },\n  { value: 'ETH', label: 'Ethereum', symbol: 'Ξ' },\n  { value: 'MLIFE', label: 'ManageLife Token', symbol: '$MLIFE' },\n] as const;\n\n// Maintenance categories\nexport const MAINTENANCE_CATEGORIES = [\n  { value: 'plumbing', label: 'Plumbing' },\n  { value: 'electrical', label: 'Electrical' },\n  { value: 'hvac', label: 'HVAC' },\n  { value: 'appliance', label: 'Appliance' },\n  { value: 'structural', label: 'Structural' },\n  { value: 'other', label: 'Other' },\n] as const;\n\n// Maintenance priorities\nexport const MAINTENANCE_PRIORITIES = [\n  { value: 'low', label: 'Low', color: 'green' },\n  { value: 'medium', label: 'Medium', color: 'yellow' },\n  { value: 'high', label: 'High', color: 'orange' },\n  { value: 'urgent', label: 'Urgent', color: 'red' },\n] as const;\n\n// Payment statuses\nexport const PAYMENT_STATUSES = [\n  { value: 'pending', label: 'Pending', color: 'yellow' },\n  { value: 'paid', label: 'Paid', color: 'green' },\n  { value: 'overdue', label: 'Overdue', color: 'red' },\n] as const;\n\n// Reward sources\nexport const REWARD_SOURCES = [\n  { value: 'rent_payment', label: 'Rent Payment' },\n  { value: 'referral', label: 'Referral' },\n  { value: 'community_activity', label: 'Community Activity' },\n  { value: 'staking', label: 'Staking' },\n  { value: 'marketplace_activity', label: 'Marketplace Activity' },\n] as const;\n\n// Event types\nexport const EVENT_TYPES = [\n  { value: 'webinar', label: 'Webinar' },\n  { value: 'meetup', label: 'Meetup' },\n  { value: 'workshop', label: 'Workshop' },\n  { value: 'announcement', label: 'Announcement' },\n] as const;\n\n// Social media links\nexport const SOCIAL_LINKS = {\n  TELEGRAM: 'https://t.me/managelife',\n  DISCORD: 'https://discord.gg/managelife',\n  TWITTER: 'https://twitter.com/managelife',\n  LINKEDIN: 'https://linkedin.com/company/managelife',\n  MEDIUM: 'https://medium.com/@managelife',\n} as const;\n\n// API endpoints\nexport const API_ENDPOINTS = {\n  PROPERTIES: '/api/properties',\n  USERS: '/api/users',\n  LEASES: '/api/leases',\n  MARKETPLACE: '/api/marketplace',\n  REWARDS: '/api/rewards',\n  MAINTENANCE: '/api/maintenance',\n  EVENTS: '/api/events',\n  AUTH: '/api/auth',\n} as const;\n\n// Local storage keys\nexport const STORAGE_KEYS = {\n  USER_PREFERENCES: 'managelife_user_preferences',\n  WALLET_CONNECTION: 'managelife_wallet_connection',\n  THEME: 'managelife_theme',\n  LANGUAGE: 'managelife_language',\n} as const;\n\n// Pagination\nexport const PAGINATION = {\n  DEFAULT_PAGE_SIZE: 10,\n  MAX_PAGE_SIZE: 100,\n} as const;\n\n// File upload\nexport const FILE_UPLOAD = {\n  MAX_SIZE: 10 * 1024 * 1024, // 10MB\n  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],\n  MAX_FILES: 10,\n} as const;\n\n// Validation rules\nexport const VALIDATION = {\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  DESCRIPTION_MAX_LENGTH: 1000,\n  TITLE_MAX_LENGTH: 100,\n} as const;\n\n// Theme colors\nexport const THEME_COLORS = {\n  PRIMARY: {\n    50: '#eff6ff',\n    100: '#dbeafe',\n    500: '#3b82f6',\n    600: '#2563eb',\n    700: '#1d4ed8',\n    900: '#1e3a8a',\n  },\n  SECONDARY: {\n    50: '#faf5ff',\n    100: '#f3e8ff',\n    500: '#8b5cf6',\n    600: '#7c3aed',\n    700: '#6d28d9',\n    900: '#4c1d95',\n  },\n} as const;\n\n// Dashboard navigation items\nexport const DASHBOARD_NAV_ITEMS = {\n  homeowner: [\n    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },\n    { href: '/dashboard?tab=properties', label: 'My Properties', icon: 'Building', tab: 'properties' },\n    { href: '/dashboard?tab=rentals', label: 'Rental Management', icon: 'FileText', tab: 'rentals' },\n    { href: '/dashboard?tab=nfts', label: 'My NFTs', icon: 'Coins', tab: 'nfts' },\n    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },\n    { href: '/dashboard?tab=rewards', label: 'Rewards', icon: 'Gift', tab: 'rewards' },\n    { href: '/dashboard?tab=analytics', label: 'Analytics', icon: 'BarChart3', tab: 'analytics' },\n  ],\n  renter: [\n    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },\n    { href: '/dashboard?tab=rentals', label: 'My Rental', icon: 'FileText', tab: 'rentals' },\n    { href: '/dashboard?tab=payments', label: 'Payments', icon: 'CreditCard', tab: 'payments' },\n    { href: '/dashboard?tab=maintenance', label: 'Maintenance', icon: 'Wrench', tab: 'maintenance' },\n    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },\n    { href: '/dashboard?tab=rewards', label: 'Rewards', icon: 'Gift', tab: 'rewards' },\n  ],\n  buyer: [\n    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },\n    { href: '/dashboard?tab=marketplace', label: 'Browse Properties', icon: 'Search', tab: 'marketplace' },\n    { href: '/dashboard?tab=favorites', label: 'Favorites', icon: 'Heart', tab: 'favorites' },\n    { href: '/dashboard?tab=offers', label: 'My Offers', icon: 'HandCoins', tab: 'offers' },\n    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },\n    { href: '/dashboard?tab=purchases', label: 'Purchases', icon: 'ShoppingCart', tab: 'purchases' },\n  ],\n  'portfolio-manager': [\n    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },\n    { href: '/dashboard?tab=portfolio', label: 'Portfolio', icon: 'Building2', tab: 'portfolio' },\n    { href: '/dashboard?tab=tenants', label: 'Tenants', icon: 'Users', tab: 'tenants' },\n    { href: '/dashboard?tab=maintenance', label: 'Maintenance', icon: 'Wrench', tab: 'maintenance' },\n    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },\n    { href: '/dashboard?tab=reports', label: 'Reports', icon: 'FileBarChart', tab: 'reports' },\n    { href: '/dashboard?tab=analytics', label: 'Analytics', icon: 'BarChart3', tab: 'analytics' },\n  ],\n  'community-member': [\n    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },\n    { href: '/dashboard?tab=community', label: 'Community', icon: 'Users', tab: 'community' },\n    { href: '/dashboard?tab=events', label: 'Events', icon: 'Calendar', tab: 'events' },\n    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },\n    { href: '/dashboard?tab=rewards', label: 'Rewards', icon: 'Gift', tab: 'rewards' },\n    { href: '/dashboard?tab=referrals', label: 'Referrals', icon: 'UserPlus', tab: 'referrals' },\n  ],\n} as const;\n\n// Utility functions for roles\nexport function getRoleDisplayName(role: string): string {\n  switch (role) {\n    case 'homeowner':\n      return 'Homeowner';\n    case 'renter':\n      return 'Renter';\n    case 'buyer':\n      return 'Buyer';\n    case 'portfolio-manager':\n      return 'Portfolio Manager';\n    case 'community-member':\n      return 'Community Member';\n    default:\n      return role;\n  }\n}\n\nexport function getRoleColor(role: string): string {\n  switch (role) {\n    case 'homeowner':\n      return 'text-blue-600 bg-blue-100';\n    case 'renter':\n      return 'text-green-600 bg-green-100';\n    case 'buyer':\n      return 'text-purple-600 bg-purple-100';\n    case 'portfolio-manager':\n      return 'text-orange-600 bg-orange-100';\n    case 'community-member':\n      return 'text-pink-600 bg-pink-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAM,WAAW;AACjB,MAAM,kBAAkB;AAGxB,MAAM,mBAAmB;IAC9B,UAAU;IACV,SAAS;IACT,SAAS;AACX;AAGO,MAAM,qBAAqB;IAChC,MAAM;IACN,MAAM;IACN,aAAa;IACb,aAAa;IACb,SAAS;AACX;AAGO,MAAM,aAAyB;IACpC;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,mBAAmB;IAC9B,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;QACA;KACD;IACD,OAAO;QACL;QACA;QACA;QACA;QACA;KACD;IACD,qBAAqB;QACnB;QACA;QACA;QACA;QACA;KACD;IACD,oBAAoB;QAClB;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAS,OAAO;IAAc;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;CAC5C;AAGM,MAAM,oBAAoB;IAC/B;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAQ;IACzD;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAO;IAClD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAO;IAC9C;QAAE,OAAO;QAAe,OAAO;QAAqB,OAAO;IAAS;CACrE;AAGM,MAAM,aAAa;IACxB;QAAE,OAAO;QAAO,OAAO;QAAa,QAAQ;IAAI;IAChD;QAAE,OAAO;QAAO,OAAO;QAAY,QAAQ;IAAI;IAC/C;QAAE,OAAO;QAAS,OAAO;QAAoB,QAAQ;IAAS;CAC/D;AAGM,MAAM,yBAAyB;IACpC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAGM,MAAM,yBAAyB;IACpC;QAAE,OAAO;QAAO,OAAO;QAAO,OAAO;IAAQ;IAC7C;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAS;IACpD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAS;IAChD;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAAM;CAClD;AAGM,MAAM,mBAAmB;IAC9B;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAS;IACtD;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAQ;IAC/C;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAM;CACpD;AAGM,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAsB,OAAO;IAAqB;IAC3D;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAwB,OAAO;IAAuB;CAChE;AAGM,MAAM,cAAc;IACzB;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAgB,OAAO;IAAe;CAChD;AAGM,MAAM,eAAe;IAC1B,UAAU;IACV,SAAS;IACT,SAAS;IACT,UAAU;IACV,QAAQ;AACV;AAGO,MAAM,gBAAgB;IAC3B,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,aAAa;IACb,SAAS;IACT,aAAa;IACb,QAAQ;IACR,MAAM;AACR;AAGO,MAAM,eAAe;IAC1B,kBAAkB;IAClB,mBAAmB;IACnB,OAAO;IACP,UAAU;AACZ;AAGO,MAAM,aAAa;IACxB,mBAAmB;IACnB,eAAe;AACjB;AAGO,MAAM,cAAc;IACzB,UAAU,KAAK,OAAO;IACtB,eAAe;QAAC;QAAc;QAAa;QAAc;KAAkB;IAC3E,WAAW;AACb;AAGO,MAAM,aAAa;IACxB,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,wBAAwB;IACxB,kBAAkB;AACpB;AAGO,MAAM,eAAe;IAC1B,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,sBAAsB;IACjC,WAAW;QACT;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;YAAQ,KAAK;QAAW;QACvE;YAAE,MAAM;YAA6B,OAAO;YAAiB,MAAM;YAAY,KAAK;QAAa;QACjG;YAAE,MAAM;YAA0B,OAAO;YAAqB,MAAM;YAAY,KAAK;QAAU;QAC/F;YAAE,MAAM;YAAuB,OAAO;YAAW,MAAM;YAAS,KAAK;QAAO;QAC5E;YAAE,MAAM;YAAgC,OAAO;YAAiB,MAAM;YAAQ,KAAK;QAAgB;QACnG;YAAE,MAAM;YAA0B,OAAO;YAAW,MAAM;YAAQ,KAAK;QAAU;QACjF;YAAE,MAAM;YAA4B,OAAO;YAAa,MAAM;YAAa,KAAK;QAAY;KAC7F;IACD,QAAQ;QACN;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;YAAQ,KAAK;QAAW;QACvE;YAAE,MAAM;YAA0B,OAAO;YAAa,MAAM;YAAY,KAAK;QAAU;QACvF;YAAE,MAAM;YAA2B,OAAO;YAAY,MAAM;YAAc,KAAK;QAAW;QAC1F;YAAE,MAAM;YAA8B,OAAO;YAAe,MAAM;YAAU,KAAK;QAAc;QAC/F;YAAE,MAAM;YAAgC,OAAO;YAAiB,MAAM;YAAQ,KAAK;QAAgB;QACnG;YAAE,MAAM;YAA0B,OAAO;YAAW,MAAM;YAAQ,KAAK;QAAU;KAClF;IACD,OAAO;QACL;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;YAAQ,KAAK;QAAW;QACvE;YAAE,MAAM;YAA8B,OAAO;YAAqB,MAAM;YAAU,KAAK;QAAc;QACrG;YAAE,MAAM;YAA4B,OAAO;YAAa,MAAM;YAAS,KAAK;QAAY;QACxF;YAAE,MAAM;YAAyB,OAAO;YAAa,MAAM;YAAa,KAAK;QAAS;QACtF;YAAE,MAAM;YAAgC,OAAO;YAAiB,MAAM;YAAQ,KAAK;QAAgB;QACnG;YAAE,MAAM;YAA4B,OAAO;YAAa,MAAM;YAAgB,KAAK;QAAY;KAChG;IACD,qBAAqB;QACnB;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;YAAQ,KAAK;QAAW;QACvE;YAAE,MAAM;YAA4B,OAAO;YAAa,MAAM;YAAa,KAAK;QAAY;QAC5F;YAAE,MAAM;YAA0B,OAAO;YAAW,MAAM;YAAS,KAAK;QAAU;QAClF;YAAE,MAAM;YAA8B,OAAO;YAAe,MAAM;YAAU,KAAK;QAAc;QAC/F;YAAE,MAAM;YAAgC,OAAO;YAAiB,MAAM;YAAQ,KAAK;QAAgB;QACnG;YAAE,MAAM;YAA0B,OAAO;YAAW,MAAM;YAAgB,KAAK;QAAU;QACzF;YAAE,MAAM;YAA4B,OAAO;YAAa,MAAM;YAAa,KAAK;QAAY;KAC7F;IACD,oBAAoB;QAClB;YAAE,MAAM;YAAc,OAAO;YAAY,MAAM;YAAQ,KAAK;QAAW;QACvE;YAAE,MAAM;YAA4B,OAAO;YAAa,MAAM;YAAS,KAAK;QAAY;QACxF;YAAE,MAAM;YAAyB,OAAO;YAAU,MAAM;YAAY,KAAK;QAAS;QAClF;YAAE,MAAM;YAAgC,OAAO;YAAiB,MAAM;YAAQ,KAAK;QAAgB;QACnG;YAAE,MAAM;YAA0B,OAAO;YAAW,MAAM;YAAQ,KAAK;QAAU;QACjF;YAAE,MAAM;YAA4B,OAAO;YAAa,MAAM;YAAY,KAAK;QAAY;KAC5F;AACH;AAGO,SAAS,mBAAmB,IAAY;IAC7C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,aAAa,IAAY;IACvC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/hooks/useRewards.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { Reward, RewardType, UserRewardStats, RewardRule } from '@/types';\n\nexport function useRewards() {\n  const [rewards, setRewards] = useState<Reward[]>([]);\n  const [pendingRewards, setPendingRewards] = useState<Reward[]>([]);\n  const [stats, setStats] = useState<UserRewardStats | null>(null);\n  const [rules, setRules] = useState<RewardRule[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch user rewards\n  const fetchRewards = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/rewards');\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch rewards');\n      }\n\n      setRewards(data.rewards);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // Fetch pending rewards\n  const fetchPendingRewards = useCallback(async () => {\n    try {\n      const response = await fetch('/api/rewards?type=pending');\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch pending rewards');\n      }\n\n      setPendingRewards(data.rewards);\n    } catch (err: any) {\n      setError(err.message);\n    }\n  }, []);\n\n  // Fetch user stats\n  const fetchStats = useCallback(async () => {\n    try {\n      const response = await fetch('/api/rewards?type=stats');\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch stats');\n      }\n\n      setStats(data.stats);\n    } catch (err: any) {\n      setError(err.message);\n    }\n  }, []);\n\n  // Fetch reward rules\n  const fetchRules = useCallback(async () => {\n    try {\n      const response = await fetch('/api/rewards?type=rules');\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch rules');\n      }\n\n      setRules(data.rules);\n    } catch (err: any) {\n      setError(err.message);\n    }\n  }, []);\n\n  // Claim a specific reward\n  const claimReward = useCallback(async (rewardId: string) => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/rewards', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'claim',\n          rewardId,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to claim reward');\n      }\n\n      // Refresh data\n      await Promise.all([fetchRewards(), fetchPendingRewards(), fetchStats()]);\n\n      return data.reward;\n    } catch (err: any) {\n      setError(err.message);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchRewards, fetchPendingRewards, fetchStats]);\n\n  // Claim all pending rewards\n  const claimAllRewards = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/rewards', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'claim',\n          rewardId: 'all',\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to claim rewards');\n      }\n\n      // Refresh data\n      await Promise.all([fetchRewards(), fetchPendingRewards(), fetchStats()]);\n\n      return { claimed: data.claimed, total: data.total };\n    } catch (err: any) {\n      setError(err.message);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchRewards, fetchPendingRewards, fetchStats]);\n\n  // Award a reward (for testing or admin purposes)\n  const awardReward = useCallback(async (type: RewardType, metadata?: Record<string, any>) => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/rewards', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'award',\n          type,\n          metadata,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to award reward');\n      }\n\n      // Refresh data\n      await Promise.all([fetchRewards(), fetchPendingRewards(), fetchStats()]);\n\n      return data.reward;\n    } catch (err: any) {\n      setError(err.message);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [fetchRewards, fetchPendingRewards, fetchStats]);\n\n  // Clear error\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  // Initialize data on mount\n  useEffect(() => {\n    Promise.all([\n      fetchRewards(),\n      fetchPendingRewards(),\n      fetchStats(),\n      fetchRules(),\n    ]);\n  }, [fetchRewards, fetchPendingRewards, fetchStats, fetchRules]);\n\n  return {\n    rewards,\n    pendingRewards,\n    stats,\n    rules,\n    loading,\n    error,\n    claimReward,\n    claimAllRewards,\n    awardReward,\n    fetchRewards,\n    fetchPendingRewards,\n    fetchStats,\n    clearError,\n  };\n}\n\nexport function useLeaderboard() {\n  const [leaderboard, setLeaderboard] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchLeaderboard = useCallback(async (limit: number = 10) => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/rewards/leaderboard?limit=${limit}`);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to fetch leaderboard');\n      }\n\n      setLeaderboard(data.leaderboard);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchLeaderboard();\n  }, [fetchLeaderboard]);\n\n  return {\n    leaderboard,\n    loading,\n    error,\n    fetchLeaderboard,\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,qBAAqB;IACrB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,WAAW,KAAK,OAAO;QACzB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,kBAAkB,KAAK,OAAO;QAChC,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,SAAS,KAAK,KAAK;QACrB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,SAAS,KAAK,KAAK;QACrB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB;IACF,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,eAAe;YACf,MAAM,QAAQ,GAAG,CAAC;gBAAC;gBAAgB;gBAAuB;aAAa;YAEvE,OAAO,KAAK,MAAM;QACpB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAc;QAAqB;KAAW;IAElD,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,UAAU;gBACZ;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,eAAe;YACf,MAAM,QAAQ,GAAG,CAAC;gBAAC;gBAAgB;gBAAuB;aAAa;YAEvE,OAAO;gBAAE,SAAS,KAAK,OAAO;gBAAE,OAAO,KAAK,KAAK;YAAC;QACpD,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAc;QAAqB;KAAW;IAElD,iDAAiD;IACjD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAkB;QACvD,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,eAAe;YACf,MAAM,QAAQ,GAAG,CAAC;gBAAC;gBAAgB;gBAAuB;aAAa;YAEvE,OAAO,KAAK,MAAM;QACpB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAc;QAAqB;KAAW;IAElD,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;YACV;YACA;YACA;YACA;SACD;IACH,GAAG;QAAC;QAAc;QAAqB;QAAY;KAAW;IAE9D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAgB,EAAE;QAC5D,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,OAAO;YACtE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,eAAe,KAAK,WAAW;QACjC,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAiB;IAErB,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n// Utility function for merging Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format currency values\nexport function formatCurrency(amount: number, currency: 'USD' | 'ETH' | 'MLIFE' = 'USD'): string {\n  switch (currency) {\n    case 'USD':\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD',\n      }).format(amount);\n    case 'ETH':\n      return `${amount.toFixed(4)} ETH`;\n    case 'MLIFE':\n      return `${amount.toLocaleString()} $MLIFE`;\n    default:\n      return amount.toString();\n  }\n}\n\n// Format wallet address\nexport function formatWalletAddress(address: string, chars: number = 4): string {\n  if (!address) return '';\n  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`;\n}\n\n// Format date\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n}\n\n// Format time only\nexport function formatTime(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Format date with time\nexport function formatDateTime(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n}\n\n// Format time ago (relative time)\nexport function formatTimeAgo(date: Date | string): string {\n  const d = new Date(date);\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7);\n  if (diffInWeeks < 4) {\n    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInMonths = Math.floor(diffInDays / 30);\n  if (diffInMonths < 12) {\n    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInYears = Math.floor(diffInDays / 365);\n  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;\n}\n\n// Calculate days between dates\nexport function daysBetween(date1: Date, date2: Date): number {\n  const oneDay = 24 * 60 * 60 * 1000;\n  return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));\n}\n\n// Check if date is overdue\nexport function isOverdue(dueDate: Date): boolean {\n  return new Date() > new Date(dueDate);\n}\n\n// Generate random ID\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Validate Ethereum address\nexport function isValidEthereumAddress(address: string): boolean {\n  return /^0x[a-fA-F0-9]{40}$/.test(address);\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0;\n  return Math.round((value / total) * 100);\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// Convert file to base64\nexport function fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => resolve(reader.result as string);\n    reader.onerror = error => reject(error);\n  });\n}\n\n// Get property status color\nexport function getPropertyStatusColor(status: string): string {\n  switch (status) {\n    case 'available':\n      return 'text-green-600 bg-green-100';\n    case 'rented':\n      return 'text-blue-600 bg-blue-100';\n    case 'sold':\n      return 'text-gray-600 bg-gray-100';\n    case 'maintenance':\n      return 'text-yellow-600 bg-yellow-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Get maintenance priority color\nexport function getMaintenancePriorityColor(priority: string): string {\n  switch (priority) {\n    case 'urgent':\n      return 'text-red-600 bg-red-100';\n    case 'high':\n      return 'text-orange-600 bg-orange-100';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'low':\n      return 'text-green-600 bg-green-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Get payment status color\nexport function getPaymentStatusColor(status: string): string {\n  switch (status) {\n    case 'paid':\n      return 'text-green-600 bg-green-100';\n    case 'pending':\n      return 'text-yellow-600 bg-yellow-100';\n    case 'overdue':\n      return 'text-red-600 bg-red-100';\n    default:\n      return 'text-gray-600 bg-gray-100';\n  }\n}\n\n// Calculate annual yield\nexport function calculateAnnualYield(monthlyRent: number, propertyValue: number): number {\n  if (propertyValue === 0) return 0;\n  return ((monthlyRent * 12) / propertyValue) * 100;\n}\n\n// Format property type\nexport function formatPropertyType(type: string): string {\n  return type.charAt(0).toUpperCase() + type.slice(1);\n}\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAoC,KAAK;IACtF,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;gBACpC,OAAO;gBACP,UAAU;YACZ,GAAG,MAAM,CAAC;QACZ,KAAK;YACH,OAAO,GAAG,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC;QACnC,KAAK;YACH,OAAO,GAAG,OAAO,cAAc,GAAG,OAAO,CAAC;QAC5C;YACE,OAAO,OAAO,QAAQ;IAC1B;AACF;AAGO,SAAS,oBAAoB,OAAe,EAAE,QAAgB,CAAC;IACpE,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACpE;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,cAAc,IAAmB;IAC/C,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa;IAC7C,IAAI,eAAe,IAAI;QACrB,OAAO,GAAG,aAAa,MAAM,EAAE,eAAe,IAAI,MAAM,GAAG,IAAI,CAAC;IAClE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;AAC/D;AAGO,SAAS,YAAY,KAAW,EAAE,KAAW;IAClD,MAAM,SAAS,KAAK,KAAK,KAAK;IAC9B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI;AACnE;AAGO,SAAS,UAAU,OAAa;IACrC,OAAO,IAAI,SAAS,IAAI,KAAK;AAC/B;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,uBAAuB,OAAe;IACpD,OAAO,sBAAsB,IAAI,CAAC;AACpC;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,aAAa,IAAU;IACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,aAAa,CAAC;QACrB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;QAC3C,OAAO,OAAO,GAAG,CAAA,QAAS,OAAO;IACnC;AACF;AAGO,SAAS,uBAAuB,MAAc;IACnD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,4BAA4B,QAAgB;IAC1D,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,sBAAsB,MAAc;IAClD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS,qBAAqB,WAAmB,EAAE,aAAqB;IAC7E,IAAI,kBAAkB,GAAG,OAAO;IAChC,OAAO,AAAE,cAAc,KAAM,gBAAiB;AAChD;AAGO,SAAS,mBAAmB,IAAY;IAC7C,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC;AACnD", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/rewards/RewardCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Gift, \n  Coins, \n  Trophy, \n  Clock, \n  CheckCircle, \n  AlertCircle,\n  TrendingUp,\n  Calendar,\n  Star,\n  Zap\n} from 'lucide-react';\nimport { useRewards } from '@/hooks/useRewards';\nimport { formatCurrency, formatDate } from '@/utils';\nimport { RewardType } from '@/types';\n\nexport default function RewardCenter() {\n  const {\n    pendingRewards,\n    stats,\n    rules,\n    loading,\n    error,\n    claimReward,\n    claimAllRewards,\n    awardReward,\n    clearError,\n  } = useRewards();\n\n  const [claiming, setClaiming] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const handleClaimReward = async (rewardId: string) => {\n    setClaiming(rewardId);\n    try {\n      await claimReward(rewardId);\n      setSuccess('Reward claimed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to claim reward:', error);\n    } finally {\n      setClaiming(null);\n    }\n  };\n\n  const handleClaimAll = async () => {\n    setClaiming('all');\n    try {\n      const result = await claimAllRewards();\n      setSuccess(`Claimed ${result.claimed.length} rewards totaling ${formatCurrency(result.total, 'MLIFE')}!`);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to claim all rewards:', error);\n    } finally {\n      setClaiming(null);\n    }\n  };\n\n  const handleTestReward = async (type: RewardType) => {\n    try {\n      await awardReward(type);\n      setSuccess(`${type.replace('_', ' ')} reward awarded!`);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to award test reward:', error);\n    }\n  };\n\n  const getRewardIcon = (type: RewardType) => {\n    switch (type) {\n      case 'welcome_bonus':\n        return <Gift className=\"w-5 h-5\" />;\n      case 'daily_login':\n        return <Calendar className=\"w-5 h-5\" />;\n      case 'rent_payment':\n        return <Coins className=\"w-5 h-5\" />;\n      case 'property_listing':\n        return <TrendingUp className=\"w-5 h-5\" />;\n      case 'referral':\n        return <Star className=\"w-5 h-5\" />;\n      case 'kyc_completion':\n        return <CheckCircle className=\"w-5 h-5\" />;\n      default:\n        return <Zap className=\"w-5 h-5\" />;\n    }\n  };\n\n  const getRewardColor = (type: RewardType) => {\n    switch (type) {\n      case 'welcome_bonus':\n        return 'text-purple-600 bg-purple-100';\n      case 'daily_login':\n        return 'text-blue-600 bg-blue-100';\n      case 'rent_payment':\n        return 'text-green-600 bg-green-100';\n      case 'property_listing':\n        return 'text-orange-600 bg-orange-100';\n      case 'referral':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'kyc_completion':\n        return 'text-indigo-600 bg-indigo-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n          <Gift className=\"w-7 h-7 mr-3 text-blue-600\" />\n          Reward Center\n        </h2>\n      </div>\n\n      {/* Error/Success Messages */}\n      {error && (\n        <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\">\n          <AlertCircle className=\"w-5 h-5 text-red-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-red-700 text-sm\">{error}</p>\n          <button\n            onClick={clearError}\n            className=\"ml-auto text-red-600 hover:text-red-700\"\n          >\n            ×\n          </button>\n        </div>\n      )}\n\n      {success && (\n        <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg flex items-center\">\n          <CheckCircle className=\"w-5 h-5 text-green-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-green-700 text-sm\">{success}</p>\n        </div>\n      )}\n\n      {/* Stats Overview */}\n      {stats && (\n        <div className=\"grid md:grid-cols-4 gap-6\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6 text-center\">\n            <Coins className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-blue-600\">{formatCurrency(stats.totalEarned, 'MLIFE')}</p>\n            <p className=\"text-sm text-blue-700\">Total Earned</p>\n          </div>\n          <div className=\"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6 text-center\">\n            <CheckCircle className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-green-600\">{formatCurrency(stats.totalClaimed, 'MLIFE')}</p>\n            <p className=\"text-sm text-green-700\">Total Claimed</p>\n          </div>\n          <div className=\"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-6 text-center\">\n            <Clock className=\"w-8 h-8 text-orange-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-orange-600\">{formatCurrency(stats.pendingRewards, 'MLIFE')}</p>\n            <p className=\"text-sm text-orange-700\">Pending</p>\n          </div>\n          <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-6 text-center\">\n            <Trophy className=\"w-8 h-8 text-purple-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-purple-600\">{stats.streakDays}</p>\n            <p className=\"text-sm text-purple-700\">Day Streak</p>\n          </div>\n        </div>\n      )}\n\n      {/* Pending Rewards */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <Clock className=\"w-5 h-5 mr-2 text-orange-600\" />\n            Pending Rewards ({pendingRewards.length})\n          </h3>\n          {pendingRewards.length > 0 && (\n            <button\n              onClick={handleClaimAll}\n              disabled={claiming === 'all' || loading}\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50\"\n            >\n              {claiming === 'all' ? 'Claiming...' : 'Claim All'}\n            </button>\n          )}\n        </div>\n\n        {pendingRewards.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <Gift className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">No Pending Rewards</h4>\n            <p className=\"text-gray-600\">Complete activities to earn $MLIFE rewards!</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {pendingRewards.map((reward) => (\n              <div\n                key={reward.id}\n                className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <div className=\"flex items-center space-x-4\">\n                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getRewardColor(reward.source)}`}>\n                    {getRewardIcon(reward.source)}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{reward.description}</h4>\n                    <p className=\"text-sm text-gray-600\">\n                      {formatCurrency(reward.amount, 'MLIFE')} • {formatDate(reward.createdAt)}\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => handleClaimReward(reward.id)}\n                  disabled={claiming === reward.id || loading}\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50\"\n                >\n                  {claiming === reward.id ? 'Claiming...' : 'Claim'}\n                </button>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Available Rewards */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-6 flex items-center\">\n          <Star className=\"w-5 h-5 mr-2 text-yellow-600\" />\n          Available Rewards\n        </h3>\n\n        <div className=\"grid md:grid-cols-2 gap-4\">\n          {rules.filter(rule => rule.isActive).map((rule) => (\n            <div\n              key={rule.id}\n              className=\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors\"\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getRewardColor(rule.type)}`}>\n                    {getRewardIcon(rule.type)}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{rule.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{rule.description}</p>\n                    <p className=\"text-sm font-medium text-blue-600 mt-1\">\n                      {formatCurrency(rule.amount, 'MLIFE')}\n                    </p>\n                  </div>\n                </div>\n                {/* Test button for demo purposes */}\n                <button\n                  onClick={() => handleTestReward(rule.type)}\n                  className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded hover:bg-gray-200 transition-colors\"\n                >\n                  Test\n                </button>\n              </div>\n              \n              {rule.cooldownPeriod && (\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  Cooldown: {rule.cooldownPeriod} hours\n                </p>\n              )}\n              \n              {rule.maxClaims && (\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  Max claims: {rule.maxClaims}\n                </p>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAhBA;;;;;;AAmBe,SAAS;IACtB,MAAM,EACJ,cAAc,EACd,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,WAAW,EACX,eAAe,EACf,WAAW,EACX,UAAU,EACX,GAAG,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,oBAAoB,OAAO;QAC/B,YAAY;QACZ,IAAI;YACF,MAAM,YAAY;YAClB,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB,YAAY;QACZ,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,WAAW,CAAC,QAAQ,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,EAAE,SAAS,CAAC,CAAC;YACxG,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,YAAY;YAClB,WAAW,GAAG,KAAK,OAAO,CAAC,KAAK,KAAK,gBAAgB,CAAC;YACtD,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAA+B;;;;;;;;;;;;YAMlD,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMJ,yBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;YAK1C,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAE,WAAU;0CAAoC,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;0CACnF,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAE,WAAU;0CAAqC,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY,EAAE;;;;;;0CACrF,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAExC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAE,WAAU;0CAAsC,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,cAAc,EAAE;;;;;;0CACxF,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAE,WAAU;0CAAsC,MAAM,UAAU;;;;;;0CACnE,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiC;oCAChC,eAAe,MAAM;oCAAC;;;;;;;4BAEzC,eAAe,MAAM,GAAG,mBACvB,8OAAC;gCACC,SAAS;gCACT,UAAU,aAAa,SAAS;gCAChC,WAAU;0CAET,aAAa,QAAQ,gBAAgB;;;;;;;;;;;;oBAK3C,eAAe,MAAM,KAAK,kBACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,sDAAsD,EAAE,eAAe,OAAO,MAAM,GAAG;0DACrG,cAAc,OAAO,MAAM;;;;;;0DAE9B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B,OAAO,WAAW;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;;4DACV,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM,EAAE;4DAAS;4DAAI,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;kDAI7E,8OAAC;wCACC,SAAS,IAAM,kBAAkB,OAAO,EAAE;wCAC1C,UAAU,aAAa,OAAO,EAAE,IAAI;wCACpC,WAAU;kDAET,aAAa,OAAO,EAAE,GAAG,gBAAgB;;;;;;;+BAnBvC,OAAO,EAAE;;;;;;;;;;;;;;;;0BA4BxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAInD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,qBACxC,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,oDAAoD,EAAE,eAAe,KAAK,IAAI,GAAG;kEAC/F,cAAc,KAAK,IAAI;;;;;;kEAE1B,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA+B,KAAK,IAAI;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EAAyB,KAAK,WAAW;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,EAAE;;;;;;;;;;;;;;;;;;0DAKnC,8OAAC;gDACC,SAAS,IAAM,iBAAiB,KAAK,IAAI;gDACzC,WAAU;0DACX;;;;;;;;;;;;oCAKF,KAAK,cAAc,kBAClB,8OAAC;wCAAE,WAAU;;4CAA6B;4CAC7B,KAAK,cAAc;4CAAC;;;;;;;oCAIlC,KAAK,SAAS,kBACb,8OAAC;wCAAE,WAAU;;4CAA6B;4CAC3B,KAAK,SAAS;;;;;;;;+BAjC1B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AA0C1B", "debugId": null}}, {"offset": {"line": 1672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/rewards/Leaderboard.tsx"], "sourcesContent": ["'use client';\n\nimport { Trophy, Medal, Award, Crown, TrendingUp } from 'lucide-react';\nimport { useLeaderboard } from '@/hooks/useRewards';\nimport { formatCurrency } from '@/utils';\n\nexport default function Leaderboard() {\n  const { leaderboard, loading, error } = useLeaderboard();\n\n  const getRankIcon = (rank: number) => {\n    switch (rank) {\n      case 1:\n        return <Crown className=\"w-6 h-6 text-yellow-500\" />;\n      case 2:\n        return <Medal className=\"w-6 h-6 text-gray-400\" />;\n      case 3:\n        return <Award className=\"w-6 h-6 text-amber-600\" />;\n      default:\n        return <span className=\"w-6 h-6 flex items-center justify-center text-sm font-bold text-gray-600\">#{rank}</span>;\n    }\n  };\n\n  const getRankBadge = (rank: number) => {\n    switch (rank) {\n      case 1:\n        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';\n      case 2:\n        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';\n      case 3:\n        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center mb-6\">\n          <Trophy className=\"w-6 h-6 mr-3 text-yellow-600\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">Leaderboard</h3>\n        </div>\n        <div className=\"space-y-4\">\n          {[...Array(5)].map((_, i) => (\n            <div key={i} className=\"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg animate-pulse\">\n              <div className=\"w-8 h-8 bg-gray-300 rounded-full\"></div>\n              <div className=\"flex-1\">\n                <div className=\"h-4 bg-gray-300 rounded w-1/3 mb-2\"></div>\n                <div className=\"h-3 bg-gray-300 rounded w-1/4\"></div>\n              </div>\n              <div className=\"h-4 bg-gray-300 rounded w-20\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center mb-6\">\n          <Trophy className=\"w-6 h-6 mr-3 text-yellow-600\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">Leaderboard</h3>\n        </div>\n        <div className=\"text-center py-8\">\n          <TrendingUp className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Failed to load leaderboard</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n      <div className=\"flex items-center mb-6\">\n        <Trophy className=\"w-6 h-6 mr-3 text-yellow-600\" />\n        <h3 className=\"text-lg font-semibold text-gray-900\">Top Earners</h3>\n      </div>\n\n      {leaderboard.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <Trophy className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">No Data Yet</h4>\n          <p className=\"text-gray-600\">Be the first to earn rewards!</p>\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {leaderboard.map((entry, index) => {\n            const rank = index + 1;\n            return (\n              <div\n                key={entry.userId}\n                className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-200 ${\n                  rank <= 3 \n                    ? 'bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200' \n                    : 'bg-gray-50 hover:bg-gray-100'\n                }`}\n              >\n                {/* Rank */}\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getRankBadge(rank)}`}>\n                  {rank <= 3 ? getRankIcon(rank) : <span className=\"font-bold\">#{rank}</span>}\n                </div>\n\n                {/* User Info */}\n                <div className=\"flex items-center space-x-3 flex-1\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-semibold text-sm\">\n                      {entry.user?.name ? entry.user.name.split(' ').map(n => n[0]).join('') : 'U'}\n                    </span>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">\n                      {entry.user?.name || 'Anonymous User'}\n                    </h4>\n                    <p className=\"text-sm text-gray-600\">\n                      {entry.achievements?.length || 0} achievements\n                    </p>\n                  </div>\n                </div>\n\n                {/* Stats */}\n                <div className=\"text-right\">\n                  <p className=\"font-bold text-lg text-blue-600\">\n                    {formatCurrency(entry.totalClaimed, 'MLIFE')}\n                  </p>\n                  <p className=\"text-sm text-gray-600\">\n                    {entry.streakDays} day streak\n                  </p>\n                </div>\n\n                {/* Special badges for top 3 */}\n                {rank <= 3 && (\n                  <div className=\"flex flex-col items-center\">\n                    {rank === 1 && (\n                      <span className=\"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full font-medium\">\n                        Champion\n                      </span>\n                    )}\n                    {rank === 2 && (\n                      <span className=\"text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-medium\">\n                        Runner-up\n                      </span>\n                    )}\n                    {rank === 3 && (\n                      <span className=\"text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full font-medium\">\n                        Third Place\n                      </span>\n                    )}\n                  </div>\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n\n      {/* Footer */}\n      <div className=\"mt-6 pt-4 border-t border-gray-200\">\n        <p className=\"text-center text-sm text-gray-600\">\n          Earn more $MLIFE tokens by completing activities and climb the leaderboard!\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,8OAAC;oBAAK,WAAU;;wBAA2E;wBAAE;;;;;;;QACxG;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAEtD,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;;2BANP;;;;;;;;;;;;;;;;IAYpB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAEtD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;YAGrD,YAAY,MAAM,KAAK,kBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;qCAG/B,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,OAAO;oBACvB,MAAM,OAAO,QAAQ;oBACrB,qBACE,8OAAC;wBAEC,WAAW,CAAC,uEAAuE,EACjF,QAAQ,IACJ,sEACA,gCACJ;;0CAGF,8OAAC;gCAAI,WAAW,CAAC,wDAAwD,EAAE,aAAa,OAAO;0CAC5F,QAAQ,IAAI,YAAY,sBAAQ,8OAAC;oCAAK,WAAU;;wCAAY;wCAAE;;;;;;;;;;;;0CAIjE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,IAAI,EAAE,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM;;;;;;;;;;;kDAG7E,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,MAAM,IAAI,EAAE,QAAQ;;;;;;0DAEvB,8OAAC;gDAAE,WAAU;;oDACV,MAAM,YAAY,EAAE,UAAU;oDAAE;;;;;;;;;;;;;;;;;;;0CAMvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY,EAAE;;;;;;kDAEtC,8OAAC;wCAAE,WAAU;;4CACV,MAAM,UAAU;4CAAC;;;;;;;;;;;;;4BAKrB,QAAQ,mBACP,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,mBACR,8OAAC;wCAAK,WAAU;kDAA2E;;;;;;oCAI5F,SAAS,mBACR,8OAAC;wCAAK,WAAU;kDAAuE;;;;;;oCAIxF,SAAS,mBACR,8OAAC;wCAAK,WAAU;kDAAyE;;;;;;;;;;;;;uBArD1F,MAAM,MAAM;;;;;gBA6DvB;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAoC;;;;;;;;;;;;;;;;;AAMzD", "debugId": null}}, {"offset": {"line": 2124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/SettingsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  User,\n  Shield,\n  Bell,\n  Globe,\n  Palette,\n  Database,\n  Key,\n  Smartphone,\n  Mail,\n  Lock,\n  Eye,\n  EyeOff,\n  Save,\n  X,\n  Check,\n  AlertTriangle,\n  Trash2,\n  Download,\n  Upload,\n  Settings\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface SettingsData {\n  // Profile Settings\n  displayName: string;\n  bio: string;\n  location: string;\n  website: string;\n  \n  // Privacy Settings\n  profileVisibility: 'public' | 'private' | 'friends';\n  showEmail: boolean;\n  showWallet: boolean;\n  showActivity: boolean;\n  \n  // Notification Settings\n  emailNotifications: boolean;\n  pushNotifications: boolean;\n  rewardNotifications: boolean;\n  communityNotifications: boolean;\n  marketingEmails: boolean;\n  \n  // App Preferences\n  language: string;\n  currency: string;\n  timezone: string;\n  theme: string;\n  \n  // Security Settings\n  twoFactorEnabled: boolean;\n  loginAlerts: boolean;\n  sessionTimeout: number;\n}\n\nexport default function SettingsPanel() {\n  const { user } = useAuth();\n  const [activeSection, setActiveSection] = useState<'profile' | 'privacy' | 'notifications' | 'preferences' | 'security' | 'data'>('profile');\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n\n  const [settings, setSettings] = useState<SettingsData>({\n    displayName: user?.name || '',\n    bio: '',\n    location: '',\n    website: '',\n    profileVisibility: 'public',\n    showEmail: false,\n    showWallet: false,\n    showActivity: true,\n    emailNotifications: true,\n    pushNotifications: true,\n    rewardNotifications: true,\n    communityNotifications: false,\n    marketingEmails: false,\n    language: 'en',\n    currency: 'usd',\n    timezone: 'UTC',\n    theme: 'light',\n    twoFactorEnabled: false,\n    loginAlerts: true,\n    sessionTimeout: 30,\n  });\n\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n\n  const handleSaveSettings = async () => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSaveMessage({ type: 'success', text: 'Settings saved successfully!' });\n    } catch (error: any) {\n      setSaveMessage({ type: 'error', text: error.message || 'Failed to save settings' });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const sections = [\n    { id: 'profile', label: 'Profile', icon: User },\n    { id: 'privacy', label: 'Privacy', icon: Shield },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'preferences', label: 'Preferences', icon: Globe },\n    { id: 'security', label: 'Security', icon: Lock },\n    { id: 'data', label: 'Data & Privacy', icon: Database },\n  ];\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n      <div className=\"border-b border-gray-200\">\n        <div className=\"flex space-x-8 px-6\">\n          {sections.map((section) => {\n            const Icon = section.icon;\n            return (\n              <button\n                key={section.id}\n                onClick={() => setActiveSection(section.id as any)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                  activeSection === section.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"w-4 h-4 mr-2\" />\n                {section.label}\n              </button>\n            );\n          })}\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Save Message */}\n        {saveMessage && (\n          <div className={`mb-6 p-4 rounded-lg flex items-center ${\n            saveMessage.type === 'success' \n              ? 'bg-green-50 border border-green-200 text-green-700' \n              : 'bg-red-50 border border-red-200 text-red-700'\n          }`}>\n            {saveMessage.type === 'success' ? (\n              <Check className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n            ) : (\n              <AlertTriangle className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n            )}\n            <p>{saveMessage.text}</p>\n            <button\n              onClick={() => setSaveMessage(null)}\n              className=\"ml-auto text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          </div>\n        )}\n\n        {/* Profile Settings */}\n        {activeSection === 'profile' && (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Profile Information</h3>\n            \n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Display Name\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.displayName}\n                  onChange={(e) => setSettings(prev => ({ ...prev, displayName: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Location\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.location}\n                  onChange={(e) => setSettings(prev => ({ ...prev, location: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"City, Country\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Bio\n              </label>\n              <textarea\n                value={settings.bio}\n                onChange={(e) => setSettings(prev => ({ ...prev, bio: e.target.value }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Tell us about yourself...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Website\n              </label>\n              <input\n                type=\"url\"\n                value={settings.website}\n                onChange={(e) => setSettings(prev => ({ ...prev, website: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"https://yourwebsite.com\"\n              />\n            </div>\n          </div>\n        )}\n\n        {/* Notifications Settings */}\n        {activeSection === 'notifications' && (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Notification Preferences</h3>\n            \n            <div className=\"space-y-4\">\n              {[\n                { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },\n                { key: 'pushNotifications', label: 'Push Notifications', description: 'Receive push notifications in your browser' },\n                { key: 'rewardNotifications', label: 'Reward Notifications', description: 'Get notified when you earn rewards' },\n                { key: 'communityNotifications', label: 'Community Updates', description: 'Updates from the ManageLife community' },\n                { key: 'marketingEmails', label: 'Marketing Emails', description: 'Promotional emails and product updates' },\n              ].map((item) => (\n                <div key={item.key} className=\"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0\">\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">{item.label}</h4>\n                    <p className=\"text-sm text-gray-600\">{item.description}</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings[item.key as keyof SettingsData] as boolean}\n                      onChange={(e) => setSettings(prev => ({ ...prev, [item.key]: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Other sections would go here... */}\n        {activeSection !== 'profile' && activeSection !== 'notifications' && (\n          <div className=\"text-center py-12\">\n            <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Settings className=\"w-8 h-8 text-gray-400\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {sections.find(s => s.id === activeSection)?.label} Settings\n            </h3>\n            <p className=\"text-gray-600\">This section is coming soon.</p>\n          </div>\n        )}\n\n        {/* Save Button */}\n        {(activeSection === 'profile' || activeSection === 'notifications') && (\n          <div className=\"mt-8 pt-6 border-t border-gray-200\">\n            <button\n              onClick={handleSaveSettings}\n              disabled={isSaving}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n            >\n              {isSaving ? (\n                <>\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                  Saving...\n                </>\n              ) : (\n                <>\n                  <Save className=\"w-4 h-4 mr-2\" />\n                  Save Changes\n                </>\n              )}\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAzBA;;;;;AA2De,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiF;IAClI,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IACnG,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,aAAa,MAAM,QAAQ;QAC3B,KAAK;QACL,UAAU;QACV,SAAS;QACT,mBAAmB;QACnB,WAAW;QACX,YAAY;QACZ,cAAc;QACd,oBAAoB;QACpB,mBAAmB;QACnB,qBAAqB;QACrB,wBAAwB;QACxB,iBAAiB;QACjB,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IAEA,MAAM,qBAAqB;QACzB,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,eAAe;gBAAE,MAAM;gBAAW,MAAM;YAA+B;QACzE,EAAE,OAAO,OAAY;YACnB,eAAe;gBAAE,MAAM;gBAAS,MAAM,MAAM,OAAO,IAAI;YAA0B;QACnF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC9C;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,sMAAA,CAAA,SAAM;QAAC;QAChD;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM,oMAAA,CAAA,QAAK;QAAC;QACvD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,kMAAA,CAAA,OAAI;QAAC;QAChD;YAAE,IAAI;YAAQ,OAAO;YAAkB,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,8OAAC;4BAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;4BAC1C,WAAW,CAAC,2DAA2D,EACrE,kBAAkB,QAAQ,EAAE,GACxB,kCACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;;;;;;gCACf,QAAQ,KAAK;;2BATT,QAAQ,EAAE;;;;;oBAYrB;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;;oBAEZ,6BACC,8OAAC;wBAAI,WAAW,CAAC,sCAAsC,EACrD,YAAY,IAAI,KAAK,YACjB,uDACA,gDACJ;;4BACC,YAAY,IAAI,KAAK,0BACpB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;qDAEjB,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CAE3B,8OAAC;0CAAG,YAAY,IAAI;;;;;;0CACpB,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMlB,kBAAkB,2BACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC9E,WAAU;;;;;;;;;;;;kDAId,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC3E,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO,SAAS,GAAG;wCACnB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,KAAK,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACtE,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;oBAOnB,kBAAkB,iCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,KAAK;wCAAsB,OAAO;wCAAuB,aAAa;oCAAkC;oCAC1G;wCAAE,KAAK;wCAAqB,OAAO;wCAAsB,aAAa;oCAA6C;oCACnH;wCAAE,KAAK;wCAAuB,OAAO;wCAAwB,aAAa;oCAAqC;oCAC/G;wCAAE,KAAK;wCAA0B,OAAO;wCAAqB,aAAa;oCAAwC;oCAClH;wCAAE,KAAK;wCAAmB,OAAO;wCAAoB,aAAa;oCAAyC;iCAC5G,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6B,KAAK,KAAK;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;0DAExD,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,QAAQ,CAAC,KAAK,GAAG,CAAuB;wDACjD,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;gEAAC,CAAC;wDAC/E,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAZT,KAAK,GAAG;;;;;;;;;;;;;;;;oBAqBzB,kBAAkB,aAAa,kBAAkB,iCAChD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;;oCACX,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB;oCAAM;;;;;;;0CAErD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAKhC,CAAC,kBAAkB,aAAa,kBAAkB,eAAe,mBAChE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,yBACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAA0F;;6DAI3G;;kDACE,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD", "debugId": null}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/PortfolioPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  TrendingUp, \n  TrendingDown,\n  DollarSign,\n  Building2,\n  Pie<PERSON>hart,\n  BarChart3,\n  Calendar,\n  Filter,\n  Download,\n  Plus,\n  Eye,\n  Edit,\n  Trash2,\n  MapPin,\n  Users,\n  Coins\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface Property {\n  id: string;\n  title: string;\n  location: string;\n  type: 'residential' | 'commercial' | 'industrial';\n  value: number;\n  purchasePrice: number;\n  purchaseDate: string;\n  monthlyIncome: number;\n  occupancyRate: number;\n  roi: number;\n  status: 'active' | 'pending' | 'sold';\n  image: string;\n  tenants: number;\n  maxTenants: number;\n}\n\ninterface PortfolioStats {\n  totalValue: number;\n  totalInvestment: number;\n  totalReturn: number;\n  monthlyIncome: number;\n  averageROI: number;\n  propertiesCount: number;\n  occupancyRate: number;\n}\n\nexport default function PortfolioPanel() {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'analytics'>('overview');\n  const [filterType, setFilterType] = useState<'all' | 'residential' | 'commercial' | 'industrial'>('all');\n\n  // Mock portfolio data\n  const portfolioStats: PortfolioStats = {\n    totalValue: 2450000,\n    totalInvestment: 1850000,\n    totalReturn: 600000,\n    monthlyIncome: 18500,\n    averageROI: 12.5,\n    propertiesCount: 8,\n    occupancyRate: 92.3,\n  };\n\n  const properties: Property[] = [\n    {\n      id: '1',\n      title: 'Luxury Downtown Apartment',\n      location: 'Manhattan, NY',\n      type: 'residential',\n      value: 850000,\n      purchasePrice: 720000,\n      purchaseDate: '2023-03-15',\n      monthlyIncome: 6500,\n      occupancyRate: 100,\n      roi: 15.2,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 1,\n      maxTenants: 1,\n    },\n    {\n      id: '2',\n      title: 'Modern Office Complex',\n      location: 'Austin, TX',\n      type: 'commercial',\n      value: 1200000,\n      purchasePrice: 950000,\n      purchaseDate: '2022-11-20',\n      monthlyIncome: 8200,\n      occupancyRate: 85,\n      roi: 18.7,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 12,\n      maxTenants: 15,\n    },\n    {\n      id: '3',\n      title: 'Suburban Family Home',\n      location: 'Phoenix, AZ',\n      type: 'residential',\n      value: 400000,\n      purchasePrice: 350000,\n      purchaseDate: '2023-07-10',\n      monthlyIncome: 2800,\n      occupancyRate: 100,\n      roi: 11.4,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 1,\n      maxTenants: 1,\n    },\n  ];\n\n  const filteredProperties = properties.filter(property => \n    filterType === 'all' || property.type === filterType\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Portfolio Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Portfolio Value</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.totalValue.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-blue-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+12.5%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Monthly Income</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.monthlyIncome.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Coins className=\"w-6 h-6 text-green-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+8.2%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Average ROI</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{portfolioStats.averageROI}%</p>\n            </div>\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-purple-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+2.1%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last quarter</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Occupancy Rate</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{portfolioStats.occupancyRate}%</p>\n            </div>\n            <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <Building2 className=\"w-6 h-6 text-orange-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingDown className=\"w-4 h-4 text-red-500 mr-1\" />\n            <span className=\"text-sm text-red-600 font-medium\">-1.2%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {[\n              { id: 'overview', label: 'Overview', icon: PieChart },\n              { id: 'properties', label: 'Properties', icon: Building2 },\n              { id: 'analytics', label: 'Analytics', icon: BarChart3 },\n            ].map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4 mr-2\" />\n                  {tab.label}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'overview' && (\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Portfolio Distribution</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-600\">Residential</span>\n                    <span className=\"font-medium\">65%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div className=\"bg-blue-600 h-2 rounded-full\" style={{ width: '65%' }}></div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-600\">Commercial</span>\n                    <span className=\"font-medium\">30%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div className=\"bg-green-600 h-2 rounded-full\" style={{ width: '30%' }}></div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-600\">Industrial</span>\n                    <span className=\"font-medium\">5%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div className=\"bg-purple-600 h-2 rounded-full\" style={{ width: '5%' }}></div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activity</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm text-gray-600\">Rent payment received - $6,500</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span className=\"text-sm text-gray-600\">Property valuation updated</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                    <span className=\"text-sm text-gray-600\">Maintenance request submitted</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'properties' && (\n            <div className=\"space-y-6\">\n              {/* Filters */}\n              <div className=\"flex items-center justify-between\">\n                <select\n                  value={filterType}\n                  onChange={(e) => setFilterType(e.target.value as any)}\n                  className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"all\">All Types</option>\n                  <option value=\"residential\">Residential</option>\n                  <option value=\"commercial\">Commercial</option>\n                  <option value=\"industrial\">Industrial</option>\n                </select>\n                \n                <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  Add Property\n                </button>\n              </div>\n\n              {/* Properties Grid */}\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {filteredProperties.map((property) => (\n                  <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n                    <img\n                      src={property.image}\n                      alt={property.title}\n                      className=\"w-full h-48 object-cover\"\n                    />\n                    <div className=\"p-6\">\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-1\">{property.title}</h3>\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                          property.status === 'active' ? 'bg-green-100 text-green-800' :\n                          property.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                          'bg-gray-100 text-gray-800'\n                        }`}>\n                          {property.status}\n                        </span>\n                      </div>\n                      \n                      <div className=\"flex items-center text-gray-600 mb-3\">\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        <span className=\"text-sm\">{property.location}</span>\n                      </div>\n                      \n                      <div className=\"space-y-2 mb-4\">\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">Current Value</span>\n                          <span className=\"font-medium\">${property.value.toLocaleString()}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">Monthly Income</span>\n                          <span className=\"font-medium text-green-600\">${property.monthlyIncome.toLocaleString()}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">ROI</span>\n                          <span className=\"font-medium text-blue-600\">{property.roi}%</span>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center text-sm text-gray-600\">\n                          <Users className=\"w-4 h-4 mr-1\" />\n                          {property.tenants}/{property.maxTenants} tenants\n                        </div>\n                        <div className=\"flex space-x-2\">\n                          <button className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\">\n                            <Eye className=\"w-4 h-4\" />\n                          </button>\n                          <button className=\"p-2 text-gray-400 hover:text-green-600 transition-colors\">\n                            <Edit className=\"w-4 h-4\" />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'analytics' && (\n            <div className=\"text-center py-12\">\n              <BarChart3 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Analytics Dashboard</h3>\n              <p className=\"text-gray-600\">Detailed analytics and performance metrics coming soon.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AArBA;;;;;AAkDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2C;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuD;IAElG,sBAAsB;IACtB,MAAM,iBAAiC;QACrC,YAAY;QACZ,iBAAiB;QACjB,aAAa;QACb,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,aAAyB;QAC7B;YACE,IAAI;YAC<PERSON>,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;KACD;IAED,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,WAC3C,eAAe,SAAS,SAAS,IAAI,KAAK;IAG5C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAAmC;oDAAE,eAAe,UAAU,CAAC,cAAc;;;;;;;;;;;;;kDAE5F,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAAmC;oDAAE,eAAe,aAAa,CAAC,cAAc;;;;;;;;;;;;;kDAE/F,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAAoC,eAAe,UAAU;oDAAC;;;;;;;;;;;;;kDAE7E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDAAoC,eAAe,aAAa;oDAAC;;;;;;;;;;;;;kDAEhF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM,8MAAA,CAAA,WAAQ;gCAAC;gCACpD;oCAAE,IAAI;oCAAc,OAAO;oCAAc,MAAM,gNAAA,CAAA,YAAS;gCAAC;gCACzD;oCAAE,IAAI;oCAAa,OAAO;oCAAa,MAAM,kNAAA,CAAA,YAAS;gCAAC;6BACxD,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAGtE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAgC,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAGvE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAiC,OAAO;gEAAE,OAAO;4DAAK;;;;;;;;;;;;;;;;;;;;;;;kDAK3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOjD,cAAc,8BACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAc;;;;;;kEAC5B,8OAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,8OAAC;wDAAO,OAAM;kEAAa;;;;;;;;;;;;0DAG7B,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAMrC,8OAAC;wCAAI,WAAU;kDACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,8OAAC;gDAAsB,WAAU;;kEAC/B,8OAAC;wDACC,KAAK,SAAS,KAAK;wDACnB,KAAK,SAAS,KAAK;wDACnB,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAoD,SAAS,KAAK;;;;;;kFAChF,8OAAC;wEAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,MAAM,KAAK,WAAW,gCAC/B,SAAS,MAAM,KAAK,YAAY,kCAChC,6BACA;kFACC,SAAS,MAAM;;;;;;;;;;;;0EAIpB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAK,WAAU;kFAAW,SAAS,QAAQ;;;;;;;;;;;;0EAG9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;;oFAAc;oFAAE,SAAS,KAAK,CAAC,cAAc;;;;;;;;;;;;;kFAE/D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;;oFAA6B;oFAAE,SAAS,aAAa,CAAC,cAAc;;;;;;;;;;;;;kFAEtF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;;oFAA6B,SAAS,GAAG;oFAAC;;;;;;;;;;;;;;;;;;;0EAI9D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,SAAS,OAAO;4EAAC;4EAAE,SAAS,UAAU;4EAAC;;;;;;;kFAE1C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC,gMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;0FAEjB,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC,2MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAhDhB,SAAS,EAAE;;;;;;;;;;;;;;;;4BA2D5B,cAAc,6BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}, {"offset": {"line": 3825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/RentalsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Home,\n  Calendar,\n  DollarSign,\n  User,\n  Phone,\n  Mail,\n  MapPin,\n  Clock,\n  AlertCircle,\n  CheckCircle,\n  XCircle,\n  Filter,\n  Search,\n  Plus,\n  Eye,\n  Edit,\n  MessageSquare,\n  FileText,\n  Wrench\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface Rental {\n  id: string;\n  propertyTitle: string;\n  propertyAddress: string;\n  tenantName: string;\n  tenantEmail: string;\n  tenantPhone: string;\n  rentAmount: number;\n  leaseStart: string;\n  leaseEnd: string;\n  status: 'active' | 'pending' | 'expired' | 'terminated';\n  paymentStatus: 'paid' | 'pending' | 'overdue';\n  lastPayment: string;\n  nextPayment: string;\n  securityDeposit: number;\n  propertyImage: string;\n}\n\ninterface MaintenanceRequest {\n  id: string;\n  propertyTitle: string;\n  tenantName: string;\n  issue: string;\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  status: 'open' | 'in-progress' | 'completed';\n  dateSubmitted: string;\n  description: string;\n}\n\nexport default function RentalsPanel() {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'rentals' | 'maintenance' | 'applications'>('rentals');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'pending' | 'expired'>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock rental data\n  const rentals: Rental[] = [\n    {\n      id: '1',\n      propertyTitle: 'Luxury Downtown Apartment',\n      propertyAddress: '123 Main St, Manhattan, NY',\n      tenantName: 'Sarah Johnson',\n      tenantEmail: '<EMAIL>',\n      tenantPhone: '+****************',\n      rentAmount: 6500,\n      leaseStart: '2023-01-01',\n      leaseEnd: '2024-12-31',\n      status: 'active',\n      paymentStatus: 'paid',\n      lastPayment: '2024-01-01',\n      nextPayment: '2024-02-01',\n      securityDeposit: 13000,\n      propertyImage: '/api/placeholder/300/200',\n    },\n    {\n      id: '2',\n      propertyTitle: 'Suburban Family Home',\n      propertyAddress: '456 Oak Ave, Phoenix, AZ',\n      tenantName: 'Michael Chen',\n      tenantEmail: '<EMAIL>',\n      tenantPhone: '+****************',\n      rentAmount: 2800,\n      leaseStart: '2023-07-01',\n      leaseEnd: '2024-06-30',\n      status: 'active',\n      paymentStatus: 'pending',\n      lastPayment: '2023-12-01',\n      nextPayment: '2024-01-01',\n      securityDeposit: 5600,\n      propertyImage: '/api/placeholder/300/200',\n    },\n    {\n      id: '3',\n      propertyTitle: 'Modern Office Space',\n      propertyAddress: '789 Business Blvd, Austin, TX',\n      tenantName: 'TechStart Inc.',\n      tenantEmail: '<EMAIL>',\n      tenantPhone: '+****************',\n      rentAmount: 8200,\n      leaseStart: '2022-11-01',\n      leaseEnd: '2025-10-31',\n      status: 'active',\n      paymentStatus: 'overdue',\n      lastPayment: '2023-11-01',\n      nextPayment: '2023-12-01',\n      securityDeposit: 16400,\n      propertyImage: '/api/placeholder/300/200',\n    },\n  ];\n\n  const maintenanceRequests: MaintenanceRequest[] = [\n    {\n      id: '1',\n      propertyTitle: 'Luxury Downtown Apartment',\n      tenantName: 'Sarah Johnson',\n      issue: 'Leaking faucet in kitchen',\n      priority: 'medium',\n      status: 'open',\n      dateSubmitted: '2024-01-15',\n      description: 'The kitchen faucet has been dripping constantly for the past week.',\n    },\n    {\n      id: '2',\n      propertyTitle: 'Suburban Family Home',\n      tenantName: 'Michael Chen',\n      issue: 'Heating system not working',\n      priority: 'urgent',\n      status: 'in-progress',\n      dateSubmitted: '2024-01-10',\n      description: 'The heating system stopped working completely. House is very cold.',\n    },\n  ];\n\n  const filteredRentals = rentals.filter(rental => {\n    const matchesStatus = filterStatus === 'all' || rental.status === filterStatus;\n    const matchesSearch = rental.propertyTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         rental.tenantName.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesStatus && matchesSearch;\n  });\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'expired': return 'bg-red-100 text-red-800';\n      case 'terminated': return 'bg-gray-100 text-gray-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPaymentStatusColor = (status: string) => {\n    switch (status) {\n      case 'paid': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'overdue': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'low': return 'bg-blue-100 text-blue-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'high': return 'bg-orange-100 text-orange-800';\n      case 'urgent': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Active Rentals</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{rentals.filter(r => r.status === 'active').length}</p>\n            </div>\n            <Home className=\"w-8 h-8 text-blue-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Monthly Revenue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                ${rentals.filter(r => r.status === 'active').reduce((sum, r) => sum + r.rentAmount, 0).toLocaleString()}\n              </p>\n            </div>\n            <DollarSign className=\"w-8 h-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Overdue Payments</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{rentals.filter(r => r.paymentStatus === 'overdue').length}</p>\n            </div>\n            <AlertCircle className=\"w-8 h-8 text-red-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Maintenance Requests</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{maintenanceRequests.filter(r => r.status !== 'completed').length}</p>\n            </div>\n            <Wrench className=\"w-8 h-8 text-orange-600\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {[\n              { id: 'rentals', label: 'Active Rentals', icon: Home },\n              { id: 'maintenance', label: 'Maintenance', icon: Wrench },\n              { id: 'applications', label: 'Applications', icon: FileText },\n            ].map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4 mr-2\" />\n                  {tab.label}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'rentals' && (\n            <div className=\"space-y-6\">\n              {/* Filters */}\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search properties or tenants...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n                <select\n                  value={filterStatus}\n                  onChange={(e) => setFilterStatus(e.target.value as any)}\n                  className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                  <option value=\"expired\">Expired</option>\n                </select>\n              </div>\n\n              {/* Rentals List */}\n              <div className=\"space-y-4\">\n                {filteredRentals.map((rental) => (\n                  <div key={rental.id} className=\"bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors\">\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div className=\"flex items-start space-x-4\">\n                        <img\n                          src={rental.propertyImage}\n                          alt={rental.propertyTitle}\n                          className=\"w-16 h-16 rounded-lg object-cover\"\n                        />\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-gray-900\">{rental.propertyTitle}</h3>\n                          <div className=\"flex items-center text-gray-600 mt-1\">\n                            <MapPin className=\"w-4 h-4 mr-1\" />\n                            <span className=\"text-sm\">{rental.propertyAddress}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(rental.status)}`}>\n                          {rental.status}\n                        </span>\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPaymentStatusColor(rental.paymentStatus)}`}>\n                          {rental.paymentStatus}\n                        </span>\n                      </div>\n                    </div>\n\n                    <div className=\"grid md:grid-cols-3 gap-6\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 mb-2\">Tenant Information</h4>\n                        <div className=\"space-y-1 text-sm text-gray-600\">\n                          <div className=\"flex items-center\">\n                            <User className=\"w-4 h-4 mr-2\" />\n                            {rental.tenantName}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Mail className=\"w-4 h-4 mr-2\" />\n                            {rental.tenantEmail}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Phone className=\"w-4 h-4 mr-2\" />\n                            {rental.tenantPhone}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 mb-2\">Lease Details</h4>\n                        <div className=\"space-y-1 text-sm text-gray-600\">\n                          <div>Rent: <span className=\"font-medium text-green-600\">${rental.rentAmount.toLocaleString()}/month</span></div>\n                          <div>Lease: {new Date(rental.leaseStart).toLocaleDateString()} - {new Date(rental.leaseEnd).toLocaleDateString()}</div>\n                          <div>Security Deposit: ${rental.securityDeposit.toLocaleString()}</div>\n                        </div>\n                      </div>\n\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 mb-2\">Payment Status</h4>\n                        <div className=\"space-y-1 text-sm text-gray-600\">\n                          <div>Last Payment: {new Date(rental.lastPayment).toLocaleDateString()}</div>\n                          <div>Next Payment: {new Date(rental.nextPayment).toLocaleDateString()}</div>\n                        </div>\n                        <div className=\"flex space-x-2 mt-3\">\n                          <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                            View Details\n                          </button>\n                          <button className=\"text-green-600 hover:text-green-800 text-sm font-medium\">\n                            Contact Tenant\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'maintenance' && (\n            <div className=\"space-y-4\">\n              {maintenanceRequests.map((request) => (\n                <div key={request.id} className=\"bg-gray-50 rounded-lg p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{request.issue}</h3>\n                      <p className=\"text-gray-600\">{request.propertyTitle} • {request.tenantName}</p>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(request.priority)}`}>\n                        {request.priority}\n                      </span>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        request.status === 'completed' ? 'bg-green-100 text-green-800' :\n                        request.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {request.status}\n                      </span>\n                    </div>\n                  </div>\n                  <p className=\"text-gray-700 mb-4\">{request.description}</p>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">\n                      Submitted: {new Date(request.dateSubmitted).toLocaleDateString()}\n                    </span>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                        Update Status\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-800 text-sm font-medium\">\n                        Contact Tenant\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {activeTab === 'applications' && (\n            <div className=\"text-center py-12\">\n              <FileText className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Rental Applications</h3>\n              <p className=\"text-gray-600\">No pending applications at this time.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AAxBA;;;;;AAuDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8C;IACvF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAC3F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,mBAAmB;IACnB,MAAM,UAAoB;QACxB;YACE,IAAI;YACJ,eAAe;YACf,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,eAAe;QACjB;QACA;YACE,IAAI;YACJ,eAAe;YACf,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,eAAe;QACjB;QACA;YACE,IAAI;YACJ,eAAe;YACf,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,eAAe;QACjB;KACD;IAED,MAAM,sBAA4C;QAChD;YACE,IAAI;YACJ,eAAe;YACf,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;QACf;QACA;YACE,IAAI;YACJ,eAAe;YACf,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;QACf;KACD;IAED,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,KAAK;QAClE,MAAM,gBAAgB,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,OAAO,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACpF,OAAO,iBAAiB;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;8CAEpG,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDAAmC;gDAC5C,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,GAAG,cAAc;;;;;;;;;;;;;8CAGzG,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,WAAW,MAAM;;;;;;;;;;;;8CAE5G,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;8CAEnH,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAW,OAAO;oCAAkB,MAAM,mMAAA,CAAA,OAAI;gCAAC;gCACrD;oCAAE,IAAI;oCAAe,OAAO;oCAAe,MAAM,sMAAA,CAAA,SAAM;gCAAC;gCACxD;oCAAE,IAAI;oCAAgB,OAAO;oCAAgB,MAAM,8MAAA,CAAA,WAAQ;gCAAC;6BAC7D,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,2BACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAU;;;;;;;;;;;;;;;;;0DAIhB,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,KAAK,OAAO,aAAa;wEACzB,KAAK,OAAO,aAAa;wEACzB,WAAU;;;;;;kFAEZ,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAuC,OAAO,aAAa;;;;;;0FACzE,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;kGAClB,8OAAC;wFAAK,WAAU;kGAAW,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;0EAIvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,OAAO,MAAM,GAAG;kFAC3F,OAAO,MAAM;;;;;;kFAEhB,8OAAC;wEAAK,WAAW,CAAC,2CAA2C,EAAE,sBAAsB,OAAO,aAAa,GAAG;kFACzG,OAAO,aAAa;;;;;;;;;;;;;;;;;;kEAK3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFACf,OAAO,UAAU;;;;;;;0FAEpB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFACf,OAAO,WAAW;;;;;;;0FAErB,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAChB,OAAO,WAAW;;;;;;;;;;;;;;;;;;;0EAKzB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;oFAAI;kGAAM,8OAAC;wFAAK,WAAU;;4FAA6B;4FAAE,OAAO,UAAU,CAAC,cAAc;4FAAG;;;;;;;;;;;;;0FAC7F,8OAAC;;oFAAI;oFAAQ,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB;oFAAG;oFAAI,IAAI,KAAK,OAAO,QAAQ,EAAE,kBAAkB;;;;;;;0FAC9G,8OAAC;;oFAAI;oFAAoB,OAAO,eAAe,CAAC,cAAc;;;;;;;;;;;;;;;;;;;0EAIlE,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;oFAAI;oFAAe,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB;;;;;;;0FACnE,8OAAC;;oFAAI;oFAAe,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;kFAErE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAO,WAAU;0FAAwD;;;;;;0FAG1E,8OAAC;gFAAO,WAAU;0FAA0D;;;;;;;;;;;;;;;;;;;;;;;;;+CAhE1E,OAAO,EAAE;;;;;;;;;;;;;;;;4BA4E1B,cAAc,+BACb,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAC,wBACxB,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAuC,QAAQ,KAAK;;;;;;0EAClE,8OAAC;gEAAE,WAAU;;oEAAiB,QAAQ,aAAa;oEAAC;oEAAI,QAAQ,UAAU;;;;;;;;;;;;;kEAE5E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,QAAQ,QAAQ,GAAG;0EAChG,QAAQ,QAAQ;;;;;;0EAEnB,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAC3D,QAAQ,MAAM,KAAK,cAAc,gCACjC,QAAQ,MAAM,KAAK,gBAAgB,8BACnC,6BACA;0EACC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAwB;4DAC1B,IAAI,KAAK,QAAQ,aAAa,EAAE,kBAAkB;;;;;;;kEAEhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAAwD;;;;;;0EAG1E,8OAAC;gEAAO,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;;uCA5BxE,QAAQ,EAAE;;;;;;;;;;4BAsCzB,cAAc,gCACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}, {"offset": {"line": 4844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/ProfilePanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  Briefcase,\n  Globe,\n  Camera,\n  Edit,\n  Save,\n  X,\n  Check,\n  AlertTriangle,\n  Shield,\n  Wallet,\n  Award,\n  TrendingUp,\n  Building2,\n  Coins,\n  DollarSign\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface UserProfile {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  location: string;\n  bio: string;\n  website: string;\n  joinDate: string;\n  avatar: string;\n  roles: string[];\n  verified: boolean;\n  kycStatus: 'pending' | 'verified' | 'rejected';\n}\n\ninterface UserStats {\n  totalInvestment: number;\n  portfolioValue: number;\n  monthlyIncome: number;\n  propertiesOwned: number;\n  totalReturns: number;\n  rewardPoints: number;\n  membershipLevel: string;\n  joinedCommunities: number;\n}\n\nexport default function ProfilePanel() {\n  const { user } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n\n  const [profile, setProfile] = useState<UserProfile>({\n    id: user?.id || '1',\n    name: user?.name || 'John Doe',\n    email: user?.email || '<EMAIL>',\n    phone: '+****************',\n    location: 'New York, NY',\n    bio: 'Real estate investor passionate about blockchain technology and decentralized finance.',\n    website: 'https://johndoe.com',\n    joinDate: '2023-01-15',\n    avatar: '/api/placeholder/150/150',\n    roles: user?.roles || ['homeowner'],\n    verified: true,\n    kycStatus: 'verified',\n  });\n\n  const [editedProfile, setEditedProfile] = useState(profile);\n\n  const userStats: UserStats = {\n    totalInvestment: 850000,\n    portfolioValue: 1200000,\n    monthlyIncome: 8500,\n    propertiesOwned: 3,\n    totalReturns: 350000,\n    rewardPoints: 12500,\n    membershipLevel: 'Gold',\n    joinedCommunities: 5,\n  };\n\n  const handleSave = async () => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setProfile(editedProfile);\n      setIsEditing(false);\n      setSaveMessage({ type: 'success', text: 'Profile updated successfully!' });\n    } catch (error: any) {\n      setSaveMessage({ type: 'error', text: error.message || 'Failed to update profile' });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setEditedProfile(profile);\n    setIsEditing(false);\n    setSaveMessage(null);\n  };\n\n  const getRoleDisplayName = (role: string) => {\n    const roleMap: { [key: string]: string } = {\n      'homeowner': 'Property Owner',\n      'renter': 'Renter',\n      'buyer': 'Investor',\n      'portfolio-manager': 'Portfolio Manager',\n      'community-member': 'Community Member',\n    };\n    return roleMap[role] || role;\n  };\n\n  const getKycStatusColor = (status: string) => {\n    switch (status) {\n      case 'verified': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'rejected': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Save Message */}\n      {saveMessage && (\n        <div className={`p-4 rounded-lg flex items-center ${\n          saveMessage.type === 'success' \n            ? 'bg-green-50 border border-green-200 text-green-700' \n            : 'bg-red-50 border border-red-200 text-red-700'\n        }`}>\n          {saveMessage.type === 'success' ? (\n            <Check className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n          ) : (\n            <AlertTriangle className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n          )}\n          <p>{saveMessage.text}</p>\n          <button\n            onClick={() => setSaveMessage(null)}\n            className=\"ml-auto text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        </div>\n      )}\n\n      <div className=\"grid lg:grid-cols-3 gap-6\">\n        {/* Profile Information */}\n        <div className=\"lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">Profile Information</h2>\n              {!isEditing ? (\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"text-blue-600 hover:text-blue-800 font-medium flex items-center\"\n                >\n                  <Edit className=\"w-4 h-4 mr-1\" />\n                  Edit Profile\n                </button>\n              ) : (\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={handleCancel}\n                    className=\"text-gray-600 hover:text-gray-800 font-medium flex items-center\"\n                  >\n                    <X className=\"w-4 h-4 mr-1\" />\n                    Cancel\n                  </button>\n                  <button\n                    onClick={handleSave}\n                    disabled={isSaving}\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center\"\n                  >\n                    {isSaving ? (\n                      <>\n                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                        Saving...\n                      </>\n                    ) : (\n                      <>\n                        <Save className=\"w-4 h-4 mr-1\" />\n                        Save\n                      </>\n                    )}\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            {/* Avatar and Basic Info */}\n            <div className=\"flex items-start space-x-6 mb-8\">\n              <div className=\"relative\">\n                <img\n                  src={profile.avatar}\n                  alt={profile.name}\n                  className=\"w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg\"\n                />\n                {isEditing && (\n                  <button className=\"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors\">\n                    <Camera className=\"w-4 h-4\" />\n                  </button>\n                )}\n              </div>\n              \n              <div className=\"flex-1\">\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  {isEditing ? (\n                    <input\n                      type=\"text\"\n                      value={editedProfile.name}\n                      onChange={(e) => setEditedProfile(prev => ({ ...prev, name: e.target.value }))}\n                      className=\"text-2xl font-bold text-gray-900 border-b-2 border-blue-500 bg-transparent focus:outline-none\"\n                    />\n                  ) : (\n                    <h1 className=\"text-2xl font-bold text-gray-900\">{profile.name}</h1>\n                  )}\n                  \n                  {profile.verified && (\n                    <div className=\"flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                      <Shield className=\"w-3 h-3 mr-1\" />\n                      Verified\n                    </div>\n                  )}\n                  \n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getKycStatusColor(profile.kycStatus)}`}>\n                    KYC {profile.kycStatus}\n                  </span>\n                </div>\n                \n                <div className=\"flex flex-wrap gap-2 mb-3\">\n                  {profile.roles.map((role) => (\n                    <span key={role} className=\"bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium\">\n                      {getRoleDisplayName(role)}\n                    </span>\n                  ))}\n                </div>\n                \n                <p className=\"text-gray-600\">Member since {new Date(profile.joinDate).toLocaleDateString()}</p>\n              </div>\n            </div>\n\n            {/* Contact Information */}\n            <div className=\"grid md:grid-cols-2 gap-6 mb-8\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email</label>\n                {isEditing ? (\n                  <input\n                    type=\"email\"\n                    value={editedProfile.email}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, email: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <Mail className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    {profile.email}\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Phone</label>\n                {isEditing ? (\n                  <input\n                    type=\"tel\"\n                    value={editedProfile.phone}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, phone: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <Phone className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    {profile.phone}\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Location</label>\n                {isEditing ? (\n                  <input\n                    type=\"text\"\n                    value={editedProfile.location}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, location: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <MapPin className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    {profile.location}\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Website</label>\n                {isEditing ? (\n                  <input\n                    type=\"url\"\n                    value={editedProfile.website}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, website: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <Globe className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    <a href={profile.website} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 hover:text-blue-800\">\n                      {profile.website}\n                    </a>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Bio */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Bio</label>\n              {isEditing ? (\n                <textarea\n                  value={editedProfile.bio}\n                  onChange={(e) => setEditedProfile(prev => ({ ...prev, bio: e.target.value }))}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              ) : (\n                <p className=\"text-gray-700\">{profile.bio}</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Stats and Achievements */}\n        <div className=\"space-y-6\">\n          {/* Portfolio Stats */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Portfolio Overview</h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <DollarSign className=\"w-5 h-5 text-green-600 mr-2\" />\n                  <span className=\"text-gray-600\">Total Value</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">${userStats.portfolioValue.toLocaleString()}</span>\n              </div>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <TrendingUp className=\"w-5 h-5 text-blue-600 mr-2\" />\n                  <span className=\"text-gray-600\">Monthly Income</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">${userStats.monthlyIncome.toLocaleString()}</span>\n              </div>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <Building2 className=\"w-5 h-5 text-purple-600 mr-2\" />\n                  <span className=\"text-gray-600\">Properties</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{userStats.propertiesOwned}</span>\n              </div>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <Coins className=\"w-5 h-5 text-orange-600 mr-2\" />\n                  <span className=\"text-gray-600\">Reward Points</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{userStats.rewardPoints.toLocaleString()}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Membership Level */}\n          <div className=\"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl shadow-sm p-6 text-white\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Membership Level</h3>\n                <p className=\"text-2xl font-bold\">{userStats.membershipLevel}</p>\n              </div>\n              <Award className=\"w-12 h-12 opacity-80\" />\n            </div>\n            <div className=\"mt-4\">\n              <div className=\"flex justify-between text-sm mb-1\">\n                <span>Progress to Platinum</span>\n                <span>75%</span>\n              </div>\n              <div className=\"w-full bg-white bg-opacity-30 rounded-full h-2\">\n                <div className=\"bg-white h-2 rounded-full\" style={{ width: '75%' }}></div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n            <div className=\"space-y-3\">\n              <button className=\"w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Wallet className=\"w-5 h-5 text-blue-600 mr-3\" />\n                  <span className=\"font-medium\">Connect Wallet</span>\n                </div>\n              </button>\n              \n              <button className=\"w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Shield className=\"w-5 h-5 text-green-600 mr-3\" />\n                  <span className=\"font-medium\">Security Settings</span>\n                </div>\n              </button>\n              \n              <button className=\"w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Award className=\"w-5 h-5 text-purple-600 mr-3\" />\n                  <span className=\"font-medium\">View Achievements</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAzBA;;;;;AAqDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IAEnG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,IAAI,MAAM,MAAM;QAChB,MAAM,MAAM,QAAQ;QACpB,OAAO,MAAM,SAAS;QACtB,OAAO;QACP,UAAU;QACV,KAAK;QACL,SAAS;QACT,UAAU;QACV,QAAQ;QACR,OAAO,MAAM,SAAS;YAAC;SAAY;QACnC,UAAU;QACV,WAAW;IACb;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,YAAuB;QAC3B,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;YACX,aAAa;YACb,eAAe;gBAAE,MAAM;gBAAW,MAAM;YAAgC;QAC1E,EAAE,OAAO,OAAY;YACnB,eAAe;gBAAE,MAAM;gBAAS,MAAM,MAAM,OAAO,IAAI;YAA2B;QACpF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,iBAAiB;QACjB,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,UAAqC;YACzC,aAAa;YACb,UAAU;YACV,SAAS;YACT,qBAAqB;YACrB,oBAAoB;QACtB;QACA,OAAO,OAAO,CAAC,KAAK,IAAI;IAC1B;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBAAI,WAAW,CAAC,iCAAiC,EAChD,YAAY,IAAI,KAAK,YACjB,uDACA,gDACJ;;oBACC,YAAY,IAAI,KAAK,0BACpB,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;6CAEjB,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCAE3B,8OAAC;kCAAG,YAAY,IAAI;;;;;;kCACpB,8OAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;wCACnD,CAAC,0BACA,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;;8DAEV,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;iEAInC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGhC,8OAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,yBACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAA0F;;qFAI3G;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAU/C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,KAAK,QAAQ,MAAM;wDACnB,KAAK,QAAQ,IAAI;wDACjB,WAAU;;;;;;oDAEX,2BACC,8OAAC;wDAAO,WAAU;kEAChB,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,0BACC,8OAAC;gEACC,MAAK;gEACL,OAAO,cAAc,IAAI;gEACzB,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC5E,WAAU;;;;;qFAGZ,8OAAC;gEAAG,WAAU;0EAAoC,QAAQ,IAAI;;;;;;4DAG/D,QAAQ,QAAQ,kBACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAKvC,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,kBAAkB,QAAQ,SAAS,GAAG;;oEAAE;oEAChG,QAAQ,SAAS;;;;;;;;;;;;;kEAI1B,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;gEAAgB,WAAU;0EACxB,mBAAmB;+DADX;;;;;;;;;;kEAMf,8OAAC;wDAAE,WAAU;;4DAAgB;4DAAc,IAAI,KAAK,QAAQ,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;kDAK5F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,8OAAC;wDACC,MAAK;wDACL,OAAO,cAAc,KAAK;wDAC1B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC7E,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,QAAQ,KAAK;;;;;;;;;;;;;0DAKpB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,8OAAC;wDACC,MAAK;wDACL,OAAO,cAAc,KAAK;wDAC1B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC7E,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,QAAQ,KAAK;;;;;;;;;;;;;0DAKpB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,8OAAC;wDACC,MAAK;wDACL,OAAO,cAAc,QAAQ;wDAC7B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAChF,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,QAAQ,QAAQ;;;;;;;;;;;;;0DAKvB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,8OAAC;wDACC,MAAK;wDACL,OAAO,cAAc,OAAO;wDAC5B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC/E,WAAU;;;;;6EAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAE,MAAM,QAAQ,OAAO;gEAAE,QAAO;gEAAS,KAAI;gEAAsB,WAAU;0EAC3E,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;4CAC/D,0BACC,8OAAC;gDACC,OAAO,cAAc,GAAG;gDACxB,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC3E,MAAM;gDACN,WAAU;;;;;qEAGZ,8OAAC;gDAAE,WAAU;0DAAiB,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAOjD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAK,WAAU;;4DAA8B;4DAAE,UAAU,cAAc,CAAC,cAAc;;;;;;;;;;;;;0DAGzF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAK,WAAU;;4DAA8B;4DAAE,UAAU,aAAa,CAAC,cAAc;;;;;;;;;;;;;0DAGxF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAK,WAAU;kEAA+B,UAAU,eAAe;;;;;;;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAK,WAAU;kEAA+B,UAAU,YAAY,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0CAM1F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAsB,UAAU,eAAe;;;;;;;;;;;;0DAE9D,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAA4B,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD", "debugId": null}}, {"offset": {"line": 5927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/NotificationsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  <PERSON>,\n  <PERSON>O<PERSON>,\n  Check,\n  X,\n  Trash2,\n  Filter,\n  DollarSign,\n  Home,\n  Users,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  Calendar,\n  Settings,\n  Gift\n} from 'lucide-react';\n\ninterface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: 'payment' | 'maintenance' | 'community' | 'system' | 'reward' | 'alert';\n  priority: 'low' | 'medium' | 'high';\n  read: boolean;\n  timestamp: string;\n  actionUrl?: string;\n  actionText?: string;\n}\n\nexport default function NotificationsPanel() {\n  const [filter, setFilter] = useState<'all' | 'unread' | 'payment' | 'maintenance' | 'community' | 'system' | 'reward' | 'alert'>('all');\n  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);\n\n  // Mock notifications data\n  const [notifications, setNotifications] = useState<Notification[]>([\n    {\n      id: '1',\n      title: 'Rent Payment Due',\n      message: 'Your rent payment of $2,800 is due on February 1st, 2024.',\n      type: 'payment',\n      priority: 'high',\n      read: false,\n      timestamp: '2024-01-25T10:30:00Z',\n      actionUrl: '/dashboard?tab=payments',\n      actionText: 'Pay Now',\n    },\n    {\n      id: '2',\n      title: '$MLIFE Rewards Earned',\n      message: 'You earned 150 $MLIFE tokens for timely rent payment!',\n      type: 'reward',\n      priority: 'medium',\n      read: false,\n      timestamp: '2024-01-24T14:15:00Z',\n      actionUrl: '/dashboard?tab=rewards',\n      actionText: 'View Rewards',\n    },\n    {\n      id: '3',\n      title: 'Maintenance Request Update',\n      message: 'Your kitchen faucet repair has been scheduled for January 30th.',\n      type: 'maintenance',\n      priority: 'medium',\n      read: true,\n      timestamp: '2024-01-23T09:45:00Z',\n      actionUrl: '/dashboard?tab=maintenance',\n      actionText: 'View Details',\n    },\n    {\n      id: '4',\n      title: 'Community Event',\n      message: 'Join our virtual real estate investment webinar this Friday at 2 PM.',\n      type: 'community',\n      priority: 'low',\n      read: true,\n      timestamp: '2024-01-22T16:20:00Z',\n      actionUrl: '/dashboard?tab=community',\n      actionText: 'Learn More',\n    },\n    {\n      id: '5',\n      title: 'System Maintenance',\n      message: 'Scheduled maintenance will occur on January 28th from 2-4 AM EST.',\n      type: 'system',\n      priority: 'medium',\n      read: false,\n      timestamp: '2024-01-21T11:00:00Z',\n    },\n    {\n      id: '6',\n      title: 'Security Alert',\n      message: 'New login detected from a different device. If this wasn\\'t you, please secure your account.',\n      type: 'alert',\n      priority: 'high',\n      read: true,\n      timestamp: '2024-01-20T08:30:00Z',\n      actionUrl: '/dashboard?tab=settings',\n      actionText: 'Review Security',\n    },\n  ]);\n\n  const filteredNotifications = notifications.filter(notification => {\n    if (filter === 'all') return true;\n    if (filter === 'unread') return !notification.read;\n    return notification.type === filter;\n  });\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'payment':\n        return <DollarSign className=\"w-5 h-5 text-green-600\" />;\n      case 'maintenance':\n        return <Home className=\"w-5 h-5 text-orange-600\" />;\n      case 'community':\n        return <Users className=\"w-5 h-5 text-blue-600\" />;\n      case 'system':\n        return <Settings className=\"w-5 h-5 text-gray-600\" />;\n      case 'reward':\n        return <Gift className=\"w-5 h-5 text-purple-600\" />;\n      case 'alert':\n        return <AlertTriangle className=\"w-5 h-5 text-red-600\" />;\n      default:\n        return <Info className=\"w-5 h-5 text-gray-600\" />;\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'border-l-red-500';\n      case 'medium':\n        return 'border-l-yellow-500';\n      case 'low':\n        return 'border-l-green-500';\n      default:\n        return 'border-l-gray-300';\n    }\n  };\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id ? { ...notification, read: true } : notification\n      )\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n  };\n\n  const deleteNotification = (id: string) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  };\n\n  const deleteSelected = () => {\n    setNotifications(prev => \n      prev.filter(notification => !selectedNotifications.includes(notification.id))\n    );\n    setSelectedNotifications([]);\n  };\n\n  const toggleSelection = (id: string) => {\n    setSelectedNotifications(prev =>\n      prev.includes(id)\n        ? prev.filter(notificationId => notificationId !== id)\n        : [...prev, id]\n    );\n  };\n\n  const selectAll = () => {\n    setSelectedNotifications(filteredNotifications.map(n => n.id));\n  };\n\n  const clearSelection = () => {\n    setSelectedNotifications([]);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Notifications</h2>\n          {unreadCount > 0 && (\n            <span className=\"bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded-full\">\n              {unreadCount} unread\n            </span>\n          )}\n        </div>\n        {unreadCount > 0 && (\n          <button\n            onClick={markAllAsRead}\n            className=\"text-blue-600 hover:text-blue-700 font-medium text-sm\"\n          >\n            Mark all as read\n          </button>\n        )}\n      </div>\n\n      {/* Filters and Actions */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex flex-wrap gap-2\">\n            {[\n              { id: 'all', label: 'All', count: notifications.length },\n              { id: 'unread', label: 'Unread', count: unreadCount },\n              { id: 'payment', label: 'Payments', count: notifications.filter(n => n.type === 'payment').length },\n              { id: 'maintenance', label: 'Maintenance', count: notifications.filter(n => n.type === 'maintenance').length },\n              { id: 'community', label: 'Community', count: notifications.filter(n => n.type === 'community').length },\n              { id: 'reward', label: 'Rewards', count: notifications.filter(n => n.type === 'reward').length },\n            ].map((filterOption) => (\n              <button\n                key={filterOption.id}\n                onClick={() => setFilter(filterOption.id as any)}\n                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors ${\n                  filter === filterOption.id\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                }`}\n              >\n                {filterOption.label} ({filterOption.count})\n              </button>\n            ))}\n          </div>\n\n          {selectedNotifications.length > 0 && (\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600\">\n                {selectedNotifications.length} selected\n              </span>\n              <button\n                onClick={deleteSelected}\n                className=\"text-red-600 hover:text-red-700 p-1\"\n              >\n                <Trash2 className=\"w-4 h-4\" />\n              </button>\n              <button\n                onClick={clearSelection}\n                className=\"text-gray-600 hover:text-gray-700 p-1\"\n              >\n                <X className=\"w-4 h-4\" />\n              </button>\n            </div>\n          )}\n        </div>\n\n        {filteredNotifications.length > 0 && (\n          <div className=\"mt-4 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={selectedNotifications.length === filteredNotifications.length ? clearSelection : selectAll}\n                className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\"\n              >\n                {selectedNotifications.length === filteredNotifications.length ? 'Deselect All' : 'Select All'}\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Notifications List */}\n      <div className=\"space-y-3\">\n        {filteredNotifications.length === 0 ? (\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n            <Bell className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No notifications</h3>\n            <p className=\"text-gray-600\">\n              {filter === 'unread' \n                ? \"You're all caught up! No unread notifications.\"\n                : `No ${filter === 'all' ? '' : filter} notifications found.`\n              }\n            </p>\n          </div>\n        ) : (\n          filteredNotifications.map((notification) => (\n            <div\n              key={notification.id}\n              className={`bg-white rounded-xl shadow-sm border border-gray-200 border-l-4 ${getPriorityColor(notification.priority)} p-6 hover:shadow-md transition-shadow ${\n                !notification.read ? 'bg-blue-50' : ''\n              }`}\n            >\n              <div className=\"flex items-start space-x-4\">\n                <input\n                  type=\"checkbox\"\n                  checked={selectedNotifications.includes(notification.id)}\n                  onChange={() => toggleSelection(notification.id)}\n                  className=\"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                \n                <div className=\"flex-shrink-0 mt-1\">\n                  {getNotificationIcon(notification.type)}\n                </div>\n\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <h3 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>\n                        {notification.title}\n                        {!notification.read && (\n                          <span className=\"ml-2 w-2 h-2 bg-blue-600 rounded-full inline-block\"></span>\n                        )}\n                      </h3>\n                      <p className=\"mt-1 text-sm text-gray-600\">{notification.message}</p>\n                      <div className=\"mt-2 flex items-center space-x-4 text-xs text-gray-500\">\n                        <span>{new Date(notification.timestamp).toLocaleString()}</span>\n                        <span className=\"capitalize\">{notification.type}</span>\n                        <span className=\"capitalize\">{notification.priority} priority</span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      {notification.actionUrl && (\n                        <button\n                          onClick={() => {\n                            // Handle navigation to specific tab\n                            const url = new URL(notification.actionUrl!, window.location.origin);\n                            const tab = url.searchParams.get('tab');\n                            if (tab) {\n                              // This would trigger tab change in parent component\n                              window.dispatchEvent(new CustomEvent('changeTab', { detail: tab }));\n                            }\n                          }}\n                          className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n                        >\n                          {notification.actionText || 'View'}\n                        </button>\n                      )}\n                      \n                      {!notification.read && (\n                        <button\n                          onClick={() => markAsRead(notification.id)}\n                          className=\"text-gray-400 hover:text-blue-600 p-1\"\n                          title=\"Mark as read\"\n                        >\n                          <Check className=\"w-4 h-4\" />\n                        </button>\n                      )}\n                      \n                      <button\n                        onClick={() => deleteNotification(notification.id)}\n                        className=\"text-gray-400 hover:text-red-600 p-1\"\n                        title=\"Delete notification\"\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiCe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8F;IACjI,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/E,0BAA0B;IAC1B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjE;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YAC<PERSON>,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;KACD;IAED,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,OAAO,OAAO;QAC7B,IAAI,WAAW,UAAU,OAAO,CAAC,aAAa,IAAI;QAClD,OAAO,aAAa,IAAI,KAAK;IAC/B;IAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,mMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAAK;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAAI;IAGjE;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;IAE7D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAC3E;IAEA,MAAM,iBAAiB;QACrB,iBAAiB,CAAA,OACf,KAAK,MAAM,CAAC,CAAA,eAAgB,CAAC,sBAAsB,QAAQ,CAAC,aAAa,EAAE;QAE7E,yBAAyB,EAAE;IAC7B;IAEA,MAAM,kBAAkB,CAAC;QACvB,yBAAyB,CAAA,OACvB,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,iBAAkB,mBAAmB,MACjD;mBAAI;gBAAM;aAAG;IAErB;IAEA,MAAM,YAAY;QAChB,yBAAyB,sBAAsB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAC9D;IAEA,MAAM,iBAAiB;QACrB,yBAAyB,EAAE;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,cAAc,mBACb,8OAAC;gCAAK,WAAU;;oCACb;oCAAY;;;;;;;;;;;;;oBAIlB,cAAc,mBACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,IAAI;wCAAO,OAAO;wCAAO,OAAO,cAAc,MAAM;oCAAC;oCACvD;wCAAE,IAAI;wCAAU,OAAO;wCAAU,OAAO;oCAAY;oCACpD;wCAAE,IAAI;wCAAW,OAAO;wCAAY,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;oCAAC;oCAClG;wCAAE,IAAI;wCAAe,OAAO;wCAAe,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,eAAe,MAAM;oCAAC;oCAC7G;wCAAE,IAAI;wCAAa,OAAO;wCAAa,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;oCAAC;oCACvG;wCAAE,IAAI;wCAAU,OAAO;wCAAW,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;oCAAC;iCAChG,CAAC,GAAG,CAAC,CAAC,6BACL,8OAAC;wCAEC,SAAS,IAAM,UAAU,aAAa,EAAE;wCACxC,WAAW,CAAC,6DAA6D,EACvE,WAAW,aAAa,EAAE,GACtB,8BACA,uDACJ;;4CAED,aAAa,KAAK;4CAAC;4CAAG,aAAa,KAAK;4CAAC;;uCARrC,aAAa,EAAE;;;;;;;;;;4BAazB,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CACb,sBAAsB,MAAM;4CAAC;;;;;;;kDAEhC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMpB,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,sBAAsB,MAAM,KAAK,sBAAsB,MAAM,GAAG,iBAAiB;gCAC1F,WAAU;0CAET,sBAAsB,MAAM,KAAK,sBAAsB,MAAM,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;;;;0BAQ5F,8OAAC;gBAAI,WAAU;0BACZ,sBAAsB,MAAM,KAAK,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCACV,WAAW,WACR,mDACA,CAAC,GAAG,EAAE,WAAW,QAAQ,KAAK,OAAO,qBAAqB,CAAC;;;;;;;;;;;2BAKnE,sBAAsB,GAAG,CAAC,CAAC,6BACzB,8OAAC;wBAEC,WAAW,CAAC,gEAAgE,EAAE,iBAAiB,aAAa,QAAQ,EAAE,uCAAuC,EAC3J,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,sBAAsB,QAAQ,CAAC,aAAa,EAAE;oCACvD,UAAU,IAAM,gBAAgB,aAAa,EAAE;oCAC/C,WAAU;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;8CACZ,oBAAoB,aAAa,IAAI;;;;;;8CAGxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAW,CAAC,oBAAoB,EAAE,CAAC,aAAa,IAAI,GAAG,kBAAkB,iBAAiB;;4DAC3F,aAAa,KAAK;4DAClB,CAAC,aAAa,IAAI,kBACjB,8OAAC;gEAAK,WAAU;;;;;;;;;;;;kEAGpB,8OAAC;wDAAE,WAAU;kEAA8B,aAAa,OAAO;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;0EACtD,8OAAC;gEAAK,WAAU;0EAAc,aAAa,IAAI;;;;;;0EAC/C,8OAAC;gEAAK,WAAU;;oEAAc,aAAa,QAAQ;oEAAC;;;;;;;;;;;;;;;;;;;0DAIxD,8OAAC;gDAAI,WAAU;;oDACZ,aAAa,SAAS,kBACrB,8OAAC;wDACC,SAAS;4DACP,oCAAoC;4DACpC,MAAM,MAAM,IAAI,IAAI,aAAa,SAAS,EAAG,OAAO,QAAQ,CAAC,MAAM;4DACnE,MAAM,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC;4DACjC,IAAI,KAAK;gEACP,oDAAoD;gEACpD,OAAO,aAAa,CAAC,IAAI,YAAY,aAAa;oEAAE,QAAQ;gEAAI;4DAClE;wDACF;wDACA,WAAU;kEAET,aAAa,UAAU,IAAI;;;;;;oDAI/B,CAAC,aAAa,IAAI,kBACjB,8OAAC;wDACC,SAAS,IAAM,WAAW,aAAa,EAAE;wDACzC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAIrB,8OAAC;wDACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;wDACjD,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAnEvB,aAAa,EAAE;;;;;;;;;;;;;;;;AA+ElC", "debugId": null}}, {"offset": {"line": 6546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/hooks/useWeb3.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { useAccount, useChainId, useWriteContract, useReadContract, useWaitForTransactionReceipt } from 'wagmi';\nimport { parseEther, formatEther } from 'viem';\nimport { CONTRACT_ABIS, getContractAddress, TransactionType, Web3Error } from '@/lib/web3';\n\nexport function useWeb3() {\n  const { address, isConnected } = useAccount();\n  const chainId = useChainId();\n  const { writeContract } = useWriteContract();\n  \n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Helper function to handle contract writes\n  const executeTransaction = useCallback(async (\n    contractName: 'NFTi' | 'NFTr' | 'MLIFE_TOKEN' | 'MARKETPLACE' | 'PROPERTY_REGISTRY',\n    functionName: string,\n    args: any[] = [],\n    value?: bigint\n  ) => {\n    if (!isConnected || !address) {\n      throw new Web3Error('Wallet not connected');\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const contractAddress = getContractAddress(contractName, chainId);\n      let abi;\n\n      switch (contractName) {\n        case 'NFTi':\n        case 'NFTr':\n          abi = CONTRACT_ABIS.NFT;\n          break;\n        case 'MLIFE_TOKEN':\n          abi = CONTRACT_ABIS.ERC20;\n          break;\n        case 'MARKETPLACE':\n          abi = CONTRACT_ABIS.MARKETPLACE;\n          break;\n        case 'PROPERTY_REGISTRY':\n          abi = CONTRACT_ABIS.PROPERTY_REGISTRY;\n          break;\n        default:\n          throw new Web3Error('Unknown contract');\n      }\n\n      const result = await writeContract({\n        address: contractAddress as `0x${string}`,\n        abi,\n        functionName,\n        args,\n        value,\n      });\n\n      return result;\n    } catch (err: any) {\n      const errorMessage = err.message || 'Transaction failed';\n      setError(errorMessage);\n      throw new Web3Error(errorMessage, err.code, err.data);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [isConnected, address, chainId, writeContract]);\n\n  return {\n    address,\n    isConnected,\n    chainId,\n    isLoading,\n    error,\n    executeTransaction,\n    clearError: () => setError(null),\n  };\n}\n\n// Hook for reading contract data\nexport function useContractRead(\n  contractName: 'NFTi' | 'NFTr' | 'MLIFE_TOKEN' | 'MARKETPLACE' | 'PROPERTY_REGISTRY',\n  functionName: string,\n  args: any[] = []\n) {\n  const chainId = useChainId();\n  \n  const contractAddress = getContractAddress(contractName, chainId);\n  let abi;\n\n  switch (contractName) {\n    case 'NFTi':\n    case 'NFTr':\n      abi = CONTRACT_ABIS.NFT;\n      break;\n    case 'MLIFE_TOKEN':\n      abi = CONTRACT_ABIS.ERC20;\n      break;\n    case 'MARKETPLACE':\n      abi = CONTRACT_ABIS.MARKETPLACE;\n      break;\n    case 'PROPERTY_REGISTRY':\n      abi = CONTRACT_ABIS.PROPERTY_REGISTRY;\n      break;\n    default:\n      throw new Error('Unknown contract');\n  }\n\n  return useReadContract({\n    address: contractAddress as `0x${string}`,\n    abi,\n    functionName,\n    args,\n  });\n}\n\n// Hook for $MLIFE token operations\nexport function useMLifeToken() {\n  const { executeTransaction } = useWeb3();\n  const { address } = useAccount();\n  const chainId = useChainId();\n\n  // Get token balance\n  const { data: balance, refetch: refetchBalance } = useContractRead(\n    'MLIFE_TOKEN',\n    'balanceOf',\n    address ? [address] : []\n  );\n\n  // Transfer tokens\n  const transfer = useCallback(async (to: string, amount: string) => {\n    const amountWei = parseEther(amount);\n    return executeTransaction('MLIFE_TOKEN', 'transfer', [to, amountWei]);\n  }, [executeTransaction]);\n\n  // Approve tokens\n  const approve = useCallback(async (spender: string, amount: string) => {\n    const amountWei = parseEther(amount);\n    return executeTransaction('MLIFE_TOKEN', 'approve', [spender, amountWei]);\n  }, [executeTransaction]);\n\n  return {\n    balance: balance ? formatEther(balance as bigint) : '0',\n    transfer,\n    approve,\n    refetchBalance,\n  };\n}\n\n// Hook for NFT operations\nexport function useNFTOperations() {\n  const { executeTransaction } = useWeb3();\n  const { address } = useAccount();\n\n  // Mint NFTi (Property NFT)\n  const mintPropertyNFT = useCallback(async (tokenId: number, tokenURI: string) => {\n    if (!address) throw new Web3Error('Wallet not connected');\n    return executeTransaction('NFTi', 'mint', [address, tokenId, tokenURI]);\n  }, [executeTransaction, address]);\n\n  // Mint NFTr (Rental NFT)\n  const mintRentalNFT = useCallback(async (tokenId: number, tokenURI: string) => {\n    if (!address) throw new Web3Error('Wallet not connected');\n    return executeTransaction('NFTr', 'mint', [address, tokenId, tokenURI]);\n  }, [executeTransaction, address]);\n\n  // Transfer NFT\n  const transferNFT = useCallback(async (\n    contractType: 'NFTi' | 'NFTr',\n    from: string,\n    to: string,\n    tokenId: number\n  ) => {\n    return executeTransaction(contractType, 'transferFrom', [from, to, tokenId]);\n  }, [executeTransaction]);\n\n  // Get NFT owner\n  const getNFTOwner = useCallback((contractType: 'NFTi' | 'NFTr', tokenId: number) => {\n    return useContractRead(contractType, 'ownerOf', [tokenId]);\n  }, []);\n\n  return {\n    mintPropertyNFT,\n    mintRentalNFT,\n    transferNFT,\n    getNFTOwner,\n  };\n}\n\n// Hook for marketplace operations\nexport function useMarketplace() {\n  const { executeTransaction } = useWeb3();\n  const chainId = useChainId();\n\n  const { data: rawListings, isLoading } = useContractRead('MARKETPLACE', 'getActiveListings', []);\n\n  // List property for sale/rent\n  const listProperty = useCallback(async (\n    tokenId: number,\n    price: string,\n    isForRent: boolean\n  ) => {\n    const priceWei = parseEther(price);\n    return executeTransaction('MARKETPLACE', 'listProperty', [tokenId, priceWei, isForRent]);\n  }, [executeTransaction]);\n\n  // Buy property\n  const buyProperty = useCallback(async (listingId: number, price: string) => {\n    const priceWei = parseEther(price);\n    return executeTransaction('MARKETPLACE', 'buyProperty', [listingId], priceWei);\n  }, [executeTransaction]);\n\n  // Rent property\n  const rentProperty = useCallback(async (\n    listingId: number,\n    duration: number,\n    price: string\n  ) => {\n    const priceWei = parseEther(price);\n    return executeTransaction('MARKETPLACE', 'rentProperty', [listingId, duration], priceWei);\n  }, [executeTransaction]);\n\n  // Cancel listing\n  const cancelListing = useCallback(async (listingId: number) => {\n    return executeTransaction('MARKETPLACE', 'cancelListing', [listingId]);\n  }, [executeTransaction]);\n\n  // Get listing details\n  const getListing = useCallback((listingId: number) => {\n    return useContractRead('MARKETPLACE', 'getListing', [listingId]);\n  }, []);\n\n  // Make offer\n  const makeOffer = useCallback(async (listingId: number, offerPrice: string) => {\n    const offerWei = parseEther(offerPrice);\n    return executeTransaction('MARKETPLACE', 'makeOffer', [listingId], offerWei);\n  }, [executeTransaction]);\n\n  return {\n    properties: rawListings || [],\nisLoading,\n    listProperty,\n    buyProperty,\n    rentProperty,\n    cancelListing,\n    getListing,\n    makeOffer,\n  };\n}\n\n// Hook for property registry operations\nexport function usePropertyRegistry() {\n  const { executeTransaction } = useWeb3();\n\n  // Register new property\n  const registerProperty = useCallback(async (\n    propertyData: string,\n    location: string\n  ) => {\n    return executeTransaction('PROPERTY_REGISTRY', 'registerProperty', [propertyData, location]);\n  }, [executeTransaction]);\n\n  // Tokenize property\n  const tokenizeProperty = useCallback(async (\n    propertyId: number,\n    tokenURI: string\n  ) => {\n    return executeTransaction('PROPERTY_REGISTRY', 'tokenizeProperty', [propertyId, tokenURI]);\n  }, [executeTransaction]);\n\n  // Get property details\n  const getProperty = useCallback((propertyId: number) => {\n    return useContractRead('PROPERTY_REGISTRY', 'getProperty', [propertyId]);\n  }, []);\n\n  // Update property\n  const updateProperty = useCallback(async (\n    propertyId: number,\n    propertyData: string\n  ) => {\n    return executeTransaction('PROPERTY_REGISTRY', 'updateProperty', [propertyId, propertyData]);\n  }, [executeTransaction]);\n\n  return {\n    registerProperty,\n    tokenizeProperty,\n    getProperty,\n    updateProperty,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;;;AAEO,SAAS;IACd,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD;IAEzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,4CAA4C;IAC5C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACrC,cACA,cACA,OAAc,EAAE,EAChB;QAEA,IAAI,CAAC,eAAe,CAAC,SAAS;YAC5B,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;QACtB;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,kBAAkB,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc;YACzD,IAAI;YAEJ,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,GAAG;oBACvB;gBACF,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,KAAK;oBACzB;gBACF,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,WAAW;oBAC/B;gBACF,KAAK;oBACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,iBAAiB;oBACrC;gBACF;oBACE,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;YACxB;YAEA,MAAM,SAAS,MAAM,cAAc;gBACjC,SAAS;gBACT;gBACA;gBACA;gBACA;YACF;YAEA,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,MAAM,eAAe,IAAI,OAAO,IAAI;YACpC,SAAS;YACT,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC,cAAc,IAAI,IAAI,EAAE,IAAI,IAAI;QACtD,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAa;QAAS;QAAS;KAAc;IAEjD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,YAAY,IAAM,SAAS;IAC7B;AACF;AAGO,SAAS,gBACd,YAAmF,EACnF,YAAoB,EACpB,OAAc,EAAE;IAEhB,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAEzB,MAAM,kBAAkB,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc;IACzD,IAAI;IAEJ,OAAQ;QACN,KAAK;QACL,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,GAAG;YACvB;QACF,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,KAAK;YACzB;QACF,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,WAAW;YAC/B;QACF,KAAK;YACH,MAAM,kHAAA,CAAA,gBAAa,CAAC,iBAAiB;YACrC;QACF;YACE,MAAM,IAAI,MAAM;IACpB;IAEA,OAAO,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QACrB,SAAS;QACT;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAC/B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAEzB,oBAAoB;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,cAAc,EAAE,GAAG,gBACjD,eACA,aACA,UAAU;QAAC;KAAQ,GAAG,EAAE;IAG1B,kBAAkB;IAClB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QAC9C,MAAM,YAAY,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC7B,OAAO,mBAAmB,eAAe,YAAY;YAAC;YAAI;SAAU;IACtE,GAAG;QAAC;KAAmB;IAEvB,iBAAiB;IACjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAiB;QAClD,MAAM,YAAY,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC7B,OAAO,mBAAmB,eAAe,WAAW;YAAC;YAAS;SAAU;IAC1E,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL,SAAS,UAAU,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE,WAAqB;QACpD;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAC/B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAE7B,2BAA2B;IAC3B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAiB;QAC1D,IAAI,CAAC,SAAS,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;QAClC,OAAO,mBAAmB,QAAQ,QAAQ;YAAC;YAAS;YAAS;SAAS;IACxE,GAAG;QAAC;QAAoB;KAAQ;IAEhC,yBAAyB;IACzB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,SAAiB;QACxD,IAAI,CAAC,SAAS,MAAM,IAAI,kHAAA,CAAA,YAAS,CAAC;QAClC,OAAO,mBAAmB,QAAQ,QAAQ;YAAC;YAAS;YAAS;SAAS;IACxE,GAAG;QAAC;QAAoB;KAAQ;IAEhC,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC9B,cACA,MACA,IACA;QAEA,OAAO,mBAAmB,cAAc,gBAAgB;YAAC;YAAM;YAAI;SAAQ;IAC7E,GAAG;QAAC;KAAmB;IAEvB,gBAAgB;IAChB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAA+B;QAC9D,OAAO,gBAAgB,cAAc,WAAW;YAAC;SAAQ;IAC3D,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAC/B,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAEzB,MAAM,EAAE,MAAM,WAAW,EAAE,SAAS,EAAE,GAAG,gBAAgB,eAAe,qBAAqB,EAAE;IAE/F,8BAA8B;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC/B,SACA,OACA;QAEA,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,gBAAgB;YAAC;YAAS;YAAU;SAAU;IACzF,GAAG;QAAC;KAAmB;IAEvB,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QACxD,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,eAAe;YAAC;SAAU,EAAE;IACvE,GAAG;QAAC;KAAmB;IAEvB,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC/B,WACA,UACA;QAEA,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,gBAAgB;YAAC;YAAW;SAAS,EAAE;IAClF,GAAG;QAAC;KAAmB;IAEvB,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,OAAO,mBAAmB,eAAe,iBAAiB;YAAC;SAAU;IACvE,GAAG;QAAC;KAAmB;IAEvB,sBAAsB;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,OAAO,gBAAgB,eAAe,cAAc;YAAC;SAAU;IACjE,GAAG,EAAE;IAEL,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QACtD,MAAM,WAAW,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,OAAO,mBAAmB,eAAe,aAAa;YAAC;SAAU,EAAE;IACrE,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL,YAAY,eAAe,EAAE;QACjC;QACI;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,kBAAkB,EAAE,GAAG;IAE/B,wBAAwB;IACxB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACnC,cACA;QAEA,OAAO,mBAAmB,qBAAqB,oBAAoB;YAAC;YAAc;SAAS;IAC7F,GAAG;QAAC;KAAmB;IAEvB,oBAAoB;IACpB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACnC,YACA;QAEA,OAAO,mBAAmB,qBAAqB,oBAAoB;YAAC;YAAY;SAAS;IAC3F,GAAG;QAAC;KAAmB;IAEvB,uBAAuB;IACvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,OAAO,gBAAgB,qBAAqB,eAAe;YAAC;SAAW;IACzE,GAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,YACA;QAEA,OAAO,mBAAmB,qBAAqB,kBAAkB;YAAC;YAAY;SAAa;IAC7F,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 6857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/homeowner/HomeownerDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Building2, TrendingUp, Coins, FileText } from 'lucide-react';\nimport { useAccount } from 'wagmi';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useNFTOperations, useContractRead } from '@/hooks/useWeb3';\n\nexport default function HomeownerDashboard() {\n  const { user } = useAuth();\n  const { mintPropertyNFT } = useNFTOperations();\n  const { address } = useAccount();\n  const [tokenizedProperties, setTokenizedProperties] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  const { data: balance } = useContractRead('NFTi', 'balanceOf', address ? [address] : []);\n\n  useEffect(() => {\n    if (balance && address) {\n      const fetchTokens = async () => {\n        setLoading(true);\n        const tokens = [];\n        for (let i = 0; i < Number(balance); i++) {\n          const tokenId = await useContractRead('NFTi', 'tokenOfOwnerByIndex', [address, i]);\n          tokens.push({ id: tokenId, status: 'Active' });\n        }\n        setTokenizedProperties(tokens);\n        setLoading(false);\n      };\n      fetchTokens();\n    }\n  }, [balance, address]);\n\n  const handleTokenize = async () => {\n    try {\n      setLoading(true);\n      const tokenId = Math.floor(Math.random() * 1000); // Dummy tokenId\n      const tokenURI = 'ipfs://dummy-uri'; // Dummy URI\n      await mintPropertyNFT(tokenId, tokenURI);\n      alert('Property tokenized successfully!');\n    } catch (error) {\n      console.error(error);\n      alert('Failed to tokenize property.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Homeowner Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Building2 className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">My Properties</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Performance</h3>\n              <p className=\"text-2xl font-bold\">N/A</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Coins className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Tokens</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <FileText className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Reports</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">My Tokenized Properties</h3>\n        {loading ? (\n          <p>Loading...</p>\n        ) : tokenizedProperties.length > 0 ? (\n          <div className=\"space-y-4\">\n            {tokenizedProperties.map((prop, index) => (\n              <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium\">Property #{index + 1}</h4>\n                <p className=\"text-sm text-gray-600\">Token ID: {prop.id} | Status: {prop.status}</p>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <p>No tokenized properties yet.</p>\n        )}\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button \n            onClick={handleTokenize}\n            disabled={loading}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? 'Tokenizing...' : 'Tokenize Property'}\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            List on Marketplace\n          </button>\n          <button className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n            Redeem Tokens\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,aAAa,UAAU;QAAC;KAAQ,GAAG,EAAE;IAEvF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,SAAS;YACtB,MAAM,cAAc;gBAClB,WAAW;gBACX,MAAM,SAAS,EAAE;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,UAAU,IAAK;oBACxC,MAAM,UAAU,MAAM,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,uBAAuB;wBAAC;wBAAS;qBAAE;oBACjF,OAAO,IAAI,CAAC;wBAAE,IAAI;wBAAS,QAAQ;oBAAS;gBAC9C;gBACA,uBAAuB;gBACvB,WAAW;YACb;YACA;QACF;IACF,GAAG;QAAC;QAAS;KAAQ;IAErB,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,gBAAgB;YAClE,MAAM,WAAW,oBAAoB,YAAY;YACjD,MAAM,gBAAgB,SAAS;YAC/B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;oBAC1C,wBACC,8OAAC;kCAAE;;;;;+BACD,oBAAoB,MAAM,GAAG,kBAC/B,8OAAC;wBAAI,WAAU;kCACZ,oBAAoB,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAG,WAAU;;4CAAc;4CAAW,QAAQ;;;;;;;kDAC/C,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAW,KAAK,EAAE;4CAAC;4CAAY,KAAK,MAAM;;;;;;;;+BAFvE;;;;;;;;;6CAOd,8OAAC;kCAAE;;;;;;;;;;;;0BAIP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,kBAAkB;;;;;;0CAE/B,8OAAC;gCAAO,WAAU;0CAAkE;;;;;;0CAGpF,8OAAC;gCAAO,WAAU;0CAAoE;;;;;;;;;;;;;;;;;;;;;;;;AAOhG", "debugId": null}}, {"offset": {"line": 7290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/renter/RenterDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Home, CreditCard, Star, MessageSquare } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useMLifeToken } from '@/hooks/useWeb3';\n\nexport default function RenterDashboard() {\n  const { user } = useAuth();\n  const { transfer, claimRewards } = useMLifeToken(); // Assuming claimRewards is available or implement similarly\n  const [loading, setLoading] = useState(false);\n\n  const handlePayRent = async () => {\n    setLoading(true);\n    try {\n      await transfer('recipientAddress', 100); // Dummy amount and address\n      alert('Rent paid successfully!');\n    } catch (error) {\n      alert('Failed to pay rent.');\n    }\n    setLoading(false);\n  };\n\n  const handleClaimRewards = async () => {\n    setLoading(true);\n    try {\n      await claimRewards(); // Assuming this function exists\n      alert('Rewards claimed!');\n    } catch (error) {\n      alert('Failed to claim rewards.');\n    }\n    setLoading(false);\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Renter Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Home className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">My Rentals</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CreditCard className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Payments Due</h3>\n              <p className=\"text-2xl font-bold\">$0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Star className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Ratings</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <MessageSquare className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Messages</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Pending Payments</h3>\n        <p>Due: $500 (Dummy data)</p>\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Available Rewards</h3>\n        <p>100 MLife (Dummy data)</p>\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button \n            onClick={handlePayRent}\n            disabled={loading}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? 'Processing...' : 'Pay Rent'}\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            Submit Maintenance Request\n          </button>\n          <button \n            onClick={handleClaimRewards}\n            disabled={loading}\n            className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50\"\n          >\n            {loading ? 'Claiming...' : 'Claim Rewards'}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,KAAK,4DAA4D;IAChH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,oBAAoB,MAAM,2BAA2B;YACpE,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR;QACA,WAAW;IACb;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,IAAI;YACF,MAAM,gBAAgB,gCAAgC;YACtD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR;QACA,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;kCAAE;;;;;;;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;kCAAE;;;;;;;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,kBAAkB;;;;;;0CAE/B,8OAAC;gCAAO,WAAU;0CAAkE;;;;;;0CAGpF,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 7679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/buyer/BuyerDashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport { ShoppingCart, Heart, FileCheck, DollarSign } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function BuyerDashboard() {\n  const { user } = useAuth();\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Buyer Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <ShoppingCart className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">My Purchases</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Heart className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Saved Properties</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <FileCheck className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Pending Offers</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Total Spent</h3>\n              <p className=\"text-2xl font-bold\">$0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\">\n            Browse Marketplace\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            Make Offer\n          </button>\n          <button className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n            Track Purchases\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAEA;;;;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAAgE;;;;;;0CAGlF,8OAAC;gCAAO,WAAU;0CAAkE;;;;;;0CAGpF,8OAAC;gCAAO,WAAU;0CAAoE;;;;;;;;;;;;;;;;;;;;;;;;AAOhG", "debugId": null}}, {"offset": {"line": 7990, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/portfolio-manager/PortfolioManagerDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Briefcase, BarChart2, Wallet, AlertCircle, TrendingUp, TrendingDown, DollarSign, Building2, Plus, Eye, Edit, Trash2, MapPin, Users, Coins } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { usePropertyRegistry } from '@/hooks/useWeb3';\nimport { Property } from '@/types';\n\ninterface PortfolioStats {\n  totalValue: number;\n  totalInvestment: number;\n  totalReturn: number;\n  monthlyIncome: number;\n  averageROI: number;\n  propertiesCount: number;\n  occupancyRate: number;\n}\n\nexport default function PortfolioManagerDashboard() {\n  const { user } = useAuth();\n  const { registerProperty, tokenizeProperty } = usePropertyRegistry();\n  const [properties, setProperties] = useState<Property[]>([]);\n  const [portfolioStats, setPortfolioStats] = useState<PortfolioStats>({\n    totalValue: 0,\n    totalInvestment: 0,\n    totalReturn: 0,\n    monthlyIncome: 0,\n    averageROI: 0,\n    propertiesCount: 0,\n    occupancyRate: 0,\n  });\n  const [filterType, setFilterType] = useState<'all' | 'residential' | 'commercial' | 'industrial'>('all');\n  const [sortBy, setSortBy] = useState<'value' | 'roi' | 'income' | 'date'>('value');\n\n  // 模拟获取数据或集成真实数据\n  useEffect(() => {\n    // 这里应集成真实数据源，如从合约获取\n    const mockProperties = [\n      // 类似 portfolio/page.tsx 中的 mock 数据\n      {\n        id: '1',\n        title: 'Luxury Downtown Apartment',\n        location: 'Manhattan, NY',\n        type: 'residential',\n        value: 850000,\n        purchasePrice: 720000,\n        purchaseDate: '2023-03-15',\n        monthlyIncome: 6500,\n        occupancyRate: 100,\n        roi: 15.2,\n        status: 'active',\n        image: '/api/placeholder/300/200',\n        tenants: 1,\n        maxTenants: 1,\n      },\n      // 添加更多...\n    ];\n    setProperties(mockProperties);\n\n    // 计算统计\n    const stats = mockProperties.reduce((acc, prop) => ({\n      totalValue: acc.totalValue + prop.value,\n      totalInvestment: acc.totalInvestment + prop.purchasePrice,\n      totalReturn: acc.totalReturn + (prop.value - prop.purchasePrice),\n      monthlyIncome: acc.monthlyIncome + prop.monthlyIncome,\n      averageROI: (acc.averageROI + prop.roi) / (acc.propertiesCount + 1),\n      propertiesCount: acc.propertiesCount + 1,\n      occupancyRate: (acc.occupancyRate + prop.occupancyRate) / (acc.propertiesCount + 1),\n    }), { totalValue: 0, totalInvestment: 0, totalReturn: 0, monthlyIncome: 0, averageROI: 0, propertiesCount: 0, occupancyRate: 0 });\n    setPortfolioStats(stats);\n  }, []);\n\n  const filteredProperties = properties.filter(property => filterType === 'all' || property.type === filterType);\n\n  const sortedProperties = [...filteredProperties].sort((a, b) => {\n    switch (sortBy) {\n      case 'value': return b.value - a.value;\n      case 'roi': return b.roi - a.roi;\n      case 'income': return b.monthlyIncome - a.monthlyIncome;\n      case 'date': return new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime();\n      default: return 0;\n    }\n  });\n\n  const handleUpdateAsset = async (propertyId: string) => {\n    // 实现更新逻辑，使用 registerProperty 或 tokenizeProperty\n    try {\n      await tokenizeProperty(Number(propertyId), 'mockURI');\n      alert('Asset updated successfully');\n    } catch (error) {\n      console.error('Update failed', error);\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Portfolio Manager Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Portfolio Value</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.totalValue.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-blue-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+12.5%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n        {/* 类似添加其他统计卡片，使用 portfolioStats */}\n      </div>\n      {/* 添加属性列表、过滤、排序和更新按钮，类似 portfolio/page.tsx */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Properties</h3>\n        {/* 过滤和排序选择 */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {sortedProperties.map((property) => (\n            <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n              {/* 属性详情 */}\n              <button onClick={() => handleUpdateAsset(property.id)} className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n                Update Asset\n              </button>\n            </div>\n          ))}\n        </div>\n      </div>\n      {/* 添加监控部分，如警报列表 */}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAEA;AACA;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,YAAY;QACZ,iBAAiB;QACjB,aAAa;QACb,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;IACjB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuD;IAClG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1E,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;QACpB,MAAM,iBAAiB;YACrB,mCAAmC;YACnC;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,eAAe;gBACf,eAAe;gBACf,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,SAAS;gBACT,YAAY;YACd;SAED;QACD,cAAc;QAEd,OAAO;QACP,MAAM,QAAQ,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;gBAClD,YAAY,IAAI,UAAU,GAAG,KAAK,KAAK;gBACvC,iBAAiB,IAAI,eAAe,GAAG,KAAK,aAAa;gBACzD,aAAa,IAAI,WAAW,GAAG,CAAC,KAAK,KAAK,GAAG,KAAK,aAAa;gBAC/D,eAAe,IAAI,aAAa,GAAG,KAAK,aAAa;gBACrD,YAAY,CAAC,IAAI,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,eAAe,GAAG,CAAC;gBAClE,iBAAiB,IAAI,eAAe,GAAG;gBACvC,eAAe,CAAC,IAAI,aAAa,GAAG,KAAK,aAAa,IAAI,CAAC,IAAI,eAAe,GAAG,CAAC;YACpF,CAAC,GAAG;YAAE,YAAY;YAAG,iBAAiB;YAAG,aAAa;YAAG,eAAe;YAAG,YAAY;YAAG,iBAAiB;YAAG,eAAe;QAAE;QAC/H,kBAAkB;IACpB,GAAG,EAAE;IAEL,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,WAAY,eAAe,SAAS,SAAS,IAAI,KAAK;IAEnG,MAAM,mBAAmB;WAAI;KAAmB,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,OAAQ;YACN,KAAK;gBAAS,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YACtC,KAAK;gBAAO,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG;YAChC,KAAK;gBAAU,OAAO,EAAE,aAAa,GAAG,EAAE,aAAa;YACvD,KAAK;gBAAQ,OAAO,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;YACzF;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,gDAAgD;QAChD,IAAI;YACF,MAAM,iBAAiB,OAAO,aAAa;YAC3C,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDAAmC;gDAAE,eAAe,UAAU,CAAC,cAAc;;;;;;;;;;;;;8CAE5F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,8OAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAE3C,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;gCAAsB,WAAU;0CAE/B,cAAA,8OAAC;oCAAO,SAAS,IAAM,kBAAkB,SAAS,EAAE;oCAAG,WAAU;8CAAoE;;;;;;+BAF7H,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AAYjC", "debugId": null}}, {"offset": {"line": 8248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/community-member/CommunityMemberDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Users, Gift, MessageSquare, BarChart2 } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useMLifeToken } from '@/hooks/useWeb3';\n\nexport default function CommunityMemberDashboard() {\n  const { user } = useAuth();\n  const { balanceOf, claimRewards } = useMLifeToken();\n  const [earnedRewards, setEarnedRewards] = useState(0);\n\n  useEffect(() => {\n    const fetchRewards = async () => {\n      if (user?.walletAddress) {\n        const balance = await balanceOf(user.walletAddress);\n        setEarnedRewards(Number(balance));\n      }\n    };\n    fetchRewards();\n  }, [user, balanceOf]);\n\n  const handleClaimRewards = async () => {\n    try {\n      await claimRewards(); // 假设 claimRewards 函数存在于钩子中\n      alert('Rewards claimed successfully');\n      // 更新余额\n      if (user?.walletAddress) {\n        const balance = await balanceOf(user.walletAddress);\n        setEarnedRewards(Number(balance));\n      }\n    } catch (error) {\n      console.error('Claim failed', error);\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Community Member Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Users className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Referrals</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Gift className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Earned Rewards</h3>\n              <p className=\"text-2xl font-bold\">{earnedRewards} MLife</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <MessageSquare className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Discussions</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <BarChart2 className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Community Stats</h3>\n              <p className=\"text-2xl font-bold\">N/A</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\">\n            Refer a Friend\n          </button>\n          <button onClick={handleClaimRewards} className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            Claim Rewards\n          </button>\n          <button className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n            Join Discussion\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,MAAM,eAAe;gBACvB,MAAM,UAAU,MAAM,UAAU,KAAK,aAAa;gBAClD,iBAAiB,OAAO;YAC1B;QACF;QACA;IACF,GAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,gBAAgB,2BAA2B;YACjD,MAAM;YACN,OAAO;YACP,IAAI,MAAM,eAAe;gBACvB,MAAM,UAAU,MAAM,UAAU,KAAK,aAAa;gBAClD,iBAAiB,OAAO;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;;gDAAsB;gDAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAIvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gOAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAAgE;;;;;;0CAGlF,8OAAC;gCAAO,SAAS;gCAAoB,WAAU;0CAAkE;;;;;;0CAGjH,8OAAC;gCAAO,WAAU;0CAAoE;;;;;;;;;;;;;;;;;;;;;;;;AAOhG", "debugId": null}}, {"offset": {"line": 8594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport {\n  Building2,\n  Home,\n  Search,\n  Users,\n  Calendar,\n  TrendingUp,\n  Coins,\n  FileText,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  AlertCircle,\n  CheckCircle,\n  Gift,\n  Bell,\n  Briefcase,\n  UserCircle,\n  CreditCard,\n  Wrench,\n  Heart,\n  HandCoins,\n  ShoppingCart,\n  FileBarChart,\n  UserPlus,\n  BarChart3\n} from 'lucide-react';\nimport { UserRole } from '@/types';\nimport { DASHBOARD_NAV_ITEMS, getRoleDisplayName, getRoleColor } from '@/constants';\nimport { useAuth } from '@/contexts/AuthContext';\nimport RewardCenter from '@/components/rewards/RewardCenter';\nimport Leaderboard from '@/components/rewards/Leaderboard';\nimport SettingsPanel from '@/components/dashboard/SettingsPanel';\nimport PortfolioPanel from '@/components/dashboard/PortfolioPanel';\nimport RentalsPanel from '@/components/dashboard/RentalsPanel';\nimport ProfilePanel from '@/components/dashboard/ProfilePanel';\nimport NotificationsPanel from '@/components/dashboard/NotificationsPanel';\nimport PropertiesPanel from '@/components/dashboard/PropertiesPanel';\nimport NFTsPanel from '@/components/dashboard/NFTsPanel';\nimport HomeownerDashboard from '@/components/dashboard/homeowner/HomeownerDashboard';\nimport RenterDashboard from '@/components/dashboard/renter/RenterDashboard';\nimport BuyerDashboard from '@/components/dashboard/buyer/BuyerDashboard';\nimport PortfolioManagerDashboard from '@/components/dashboard/portfolio-manager/PortfolioManagerDashboard';\nimport CommunityMemberDashboard from '@/components/dashboard/community-member/CommunityMemberDashboard';\n\nexport default function DashboardPage() {\n  const { user, loading, logout } = useAuth();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const isWelcome = searchParams.get('welcome') === 'true';\n\n  const [activeRole, setActiveRole] = useState<UserRole>('buyer');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showWelcome, setShowWelcome] = useState(isWelcome);\n  const [activeTab, setActiveTab] = useState<'overview' | 'rewards' | 'leaderboard' | 'portfolio' | 'rentals' | 'settings' | 'profile' | 'properties' | 'nfts' | 'notifications' | 'analytics' | 'payments' | 'maintenance' | 'marketplace' | 'favorites' | 'offers' | 'purchases' | 'tenants' | 'reports' | 'community' | 'events' | 'referrals'>('overview');\n\n  // Handle URL tab parameter\n  useEffect(() => {\n    const tabParam = searchParams.get('tab');\n    const validTabs = ['overview', 'rewards', 'leaderboard', 'portfolio', 'rentals', 'settings', 'profile', 'properties', 'nfts', 'notifications', 'analytics', 'payments', 'maintenance', 'marketplace', 'favorites', 'offers', 'purchases', 'tenants', 'reports', 'community', 'events', 'referrals'];\n    if (tabParam && validTabs.includes(tabParam)) {\n      setActiveTab(tabParam as any);\n    }\n  }, [searchParams]);\n\n  // Redirect to login if not authenticated\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login');\n    }\n  }, [user, loading, router]);\n\n  // Set initial active role when user data loads\n  useEffect(() => {\n    if (user && user.roles.length > 0) {\n      setActiveRole(user.roles[0]);\n    }\n  }, [user]);\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading your dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error if no user\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  const navItems = DASHBOARD_NAV_ITEMS[activeRole] || [];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 lg:flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:z-auto lg:flex-shrink-0 ${\n        sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n      }`}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-2\">\n            <img\n              src=\"/logo/logo.png\"\n              alt=\"ManageLife\"\n              className=\"h-8 w-auto\"\n            />\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n          >\n            <X className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* User Info */}\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-3 mb-4\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-semibold\">\n                {user.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}\n              </span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">{user.name || 'User'}</h3>\n              <p className=\"text-sm text-gray-600\">{user.email || user.walletAddress}</p>\n              {user.walletAddress && (\n                <p className=\"text-xs text-gray-500 font-mono\">\n                  {user.walletAddress.slice(0, 6)}...{user.walletAddress.slice(-4)}\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* Role Selector */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Active Role</label>\n            <select\n              value={activeRole}\n              onChange={(e) => setActiveRole(e.target.value as UserRole)}\n              className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {user.roles.map((role) => (\n                <option key={role} value={role}>\n                  {getRoleDisplayName(role)}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* $MLIFE Balance */}\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm font-medium text-gray-700\">$MLIFE Balance</span>\n              <Coins className=\"w-4 h-4 text-blue-600\" />\n            </div>\n            <p className=\"text-lg font-bold text-blue-600\">{user.mlifeBalance.toLocaleString()}</p>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 px-6 py-4\">\n          <ul className=\"space-y-2\">\n            {navItems.map((item) => {\n              const IconComponent = {\n                Home,\n                Building: Building2,\n                Building2,\n                Coins,\n                FileText,\n                Search,\n                Users,\n                Calendar,\n                TrendingUp,\n                Bell,\n                Gift,\n                CreditCard,\n                Wrench,\n                Heart,\n                HandCoins,\n                ShoppingCart,\n                FileBarChart,\n                UserPlus,\n                BarChart3,\n              }[item.icon as keyof typeof import('lucide-react')] || Home;\n\n              const handleNavClick = (e: React.MouseEvent) => {\n                e.preventDefault();\n                if (item.tab) {\n                  setActiveTab(item.tab as any);\n                  // Update URL without page reload\n                  const newUrl = item.tab === 'overview' ? '/dashboard' : `/dashboard?tab=${item.tab}`;\n                  window.history.pushState({}, '', newUrl);\n                }\n              };\n\n              return (\n                <li key={item.href}>\n                  <button\n                    onClick={handleNavClick}\n                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors text-left ${\n                      (item.tab === 'overview' && activeTab === 'overview') || activeTab === item.tab\n                        ? 'bg-blue-50 text-blue-600'\n                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'\n                    }`}\n                  >\n                    <IconComponent className=\"w-5 h-5\" />\n                    <span>{item.label}</span>\n                  </button>\n                </li>\n              );\n            })}\n          </ul>\n        </nav>\n\n        {/* Bottom Actions */}\n        <div className=\"p-6 border-t border-gray-200\">\n          <div className=\"space-y-2\">\n            <Link\n              href=\"/settings\"\n              className=\"flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-5 h-5\" />\n              <span>Settings</span>\n            </Link>\n            <button\n              onClick={handleLogout}\n              className=\"flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors w-full text-left\"\n            >\n              <LogOut className=\"w-5 h-5\" />\n              <span>Sign Out</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 lg:flex lg:flex-col lg:min-w-0\">\n        {/* Top Bar */}\n        <header className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex items-center justify-between h-16 px-6\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Menu className=\"w-6 h-6\" />\n              </button>\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                {getRoleDisplayName(activeRole)} Dashboard\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(activeRole)}`}>\n                {getRoleDisplayName(activeRole)}\n              </span>\n            </div>\n          </div>\n        </header>\n\n        {/* Dashboard Content */}\n        <main className=\"p-6\">\n          <div className=\"max-w-7xl mx-auto\">\n            {/* Tab Navigation */}\n            <div className=\"flex justify-center mb-8\">\n              <div className=\"bg-gray-100 rounded-lg p-1 flex\">\n                <button\n                  onClick={() => setActiveTab('overview')}\n                  className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                    activeTab === 'overview'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <Home className=\"w-4 h-4 inline mr-2\" />\n                  Overview\n                </button>\n                <button\n                  onClick={() => setActiveTab('rewards')}\n                  className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                    activeTab === 'rewards'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <Gift className=\"w-4 h-4 inline mr-2\" />\n                  Rewards\n                </button>\n                <button\n                  onClick={() => setActiveTab('leaderboard')}\n                  className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                    activeTab === 'leaderboard'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <TrendingUp className=\"w-4 h-4 inline mr-2\" />\n                  Leaderboard\n                </button>\n                <button\n                  onClick={() => setActiveTab('portfolio')}\n                  className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                    activeTab === 'portfolio'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <Briefcase className=\"w-4 h-4 inline mr-2\" />\n                  Portfolio\n                </button>\n                <button\n                  onClick={() => setActiveTab('rentals')}\n                  className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                    activeTab === 'rentals'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <Home className=\"w-4 h-4 inline mr-2\" />\n                  Rentals\n                </button>\n                <button\n                  onClick={() => setActiveTab('profile')}\n                  className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                    activeTab === 'profile'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <UserCircle className=\"w-4 h-4 inline mr-2\" />\n                  Profile\n                </button>\n                <button\n                  onClick={() => setActiveTab('settings')}\n                  className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                    activeTab === 'settings'\n                      ? 'bg-white text-blue-600 shadow-sm'\n                      : 'text-gray-600 hover:text-gray-900'\n                  }`}\n                >\n                  <Settings className=\"w-4 h-4 inline mr-2\" />\n                  Settings\n                </button>\n              </div>\n            </div>\n            {/* Tab Content */}\n            {activeTab === 'overview' && (\n              <>\n                {/* Welcome Message for New Users */}\n                {showWelcome && (\n                  <div className=\"bg-green-50 border border-green-200 rounded-xl p-6 mb-6\">\n                    <div className=\"flex items-start\">\n                      <CheckCircle className=\"w-6 h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5\" />\n                      <div className=\"flex-1\">\n                        <h3 className=\"text-lg font-semibold text-green-900 mb-2\">\n                          Welcome to ManageLife! 🎉\n                        </h3>\n                        <p className=\"text-green-700 mb-4\">\n                          Your account has been created successfully. You've received 1,000 $MLIFE tokens as a welcome bonus!\n                        </p>\n                        <button\n                          onClick={() => setShowWelcome(false)}\n                          className=\"text-green-600 hover:text-green-700 font-medium text-sm\"\n                        >\n                          Dismiss\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Welcome Section */}\n                <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white mb-8\">\n                  <h2 className=\"text-2xl font-bold mb-2\">\n                    Welcome back, {user.name || 'User'}!\n                  </h2>\n                  <p className=\"text-blue-100 mb-4\">\n                    You're currently viewing your {getRoleDisplayName(activeRole).toLowerCase()} dashboard.\n                  </p>\n                  <div className=\"flex flex-wrap gap-4\">\n                    <div className=\"bg-white/20 rounded-lg p-4 backdrop-blur-sm\">\n                      <h3 className=\"font-semibold mb-1\">$MLIFE Balance</h3>\n                      <p className=\"text-2xl font-bold\">{user.mlifeBalance.toLocaleString()}</p>\n                    </div>\n                    <div className=\"bg-white/20 rounded-lg p-4 backdrop-blur-sm\">\n                      <h3 className=\"font-semibold mb-1\">Active Roles</h3>\n                      <p className=\"text-2xl font-bold\">{user.roles.length}</p>\n                    </div>\n                    <div className=\"bg-white/20 rounded-lg p-4 backdrop-blur-sm\">\n                      <h3 className=\"font-semibold mb-1\">Member Since</h3>\n                      <p className=\"text-2xl font-bold\">{new Date(user.joinedAt).getFullYear()}</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Quick Actions */}\n                <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                  {navItems.slice(1, 5).map((item) => {\n                    const IconComponent = {\n                      Home,\n                      Building: Building2,\n                      Building2,\n                      Coins,\n                      FileText,\n                      Search,\n                      Users,\n                      Calendar,\n                      TrendingUp,\n                      Bell,\n                    }[item.icon as keyof typeof import('lucide-react')] || Home;\n\n                    return (\n                      <Link\n                        key={item.href}\n                        href={item.href}\n                        className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow card-hover\"\n                      >\n                        <div className=\"flex items-center space-x-4\">\n                          <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                            <IconComponent className=\"w-6 h-6 text-blue-600\" />\n                          </div>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900\">{item.label}</h3>\n                            <p className=\"text-sm text-gray-600\">Quick access</p>\n                          </div>\n                        </div>\n                      </Link>\n                    );\n                  })}\n                </div>\n\n                {/* Role-specific dashboard */}\n                {activeRole === 'homeowner' && <HomeownerDashboard />}\n                {activeRole === 'renter' && <RenterDashboard />}\n                {activeRole === 'buyer' && <BuyerDashboard />}\n                {activeRole === 'portfolio-manager' && <PortfolioManagerDashboard />}\n                {activeRole === 'community-member' && <CommunityMemberDashboard />}\n              </>\n            )}\n\n            {/* Rewards Tab */}\n            {activeTab === 'rewards' && <RewardCenter />}\n\n            {/* Leaderboard Tab */}\n            {activeTab === 'leaderboard' && <Leaderboard />}\n\n            {/* Portfolio Tab */}\n            {activeTab === 'portfolio' && <PortfolioPanel />}\n\n            {/* Rentals Tab */}\n            {activeTab === 'rentals' && <RentalsPanel />}\n\n            {/* Profile Tab */}\n            {activeTab === 'profile' && <ProfilePanel />}\n\n            {/* Settings Tab */}\n            {activeTab === 'settings' && <SettingsPanel />}\n\n            {/* Additional Tabs */}\n            {activeTab === 'properties' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">My Properties</h2>\n                <p className=\"text-gray-600\">Property management features coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'nfts' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">My NFTs</h2>\n                <p className=\"text-gray-600\">NFT collection and management features coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'notifications' && <NotificationsPanel />}\n\n            {activeTab === 'analytics' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Analytics</h2>\n                <p className=\"text-gray-600\">Advanced analytics dashboard coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'payments' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Payments</h2>\n                <p className=\"text-gray-600\">Payment management features coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'maintenance' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Maintenance</h2>\n                <p className=\"text-gray-600\">Maintenance request system coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'marketplace' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Browse Properties</h2>\n                <p className=\"text-gray-600\">Property marketplace coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'favorites' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Favorites</h2>\n                <p className=\"text-gray-600\">Favorite properties list coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'offers' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">My Offers</h2>\n                <p className=\"text-gray-600\">Offer management system coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'purchases' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Purchases</h2>\n                <p className=\"text-gray-600\">Purchase history and management coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'tenants' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Tenants</h2>\n                <p className=\"text-gray-600\">Tenant management system coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'reports' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Reports</h2>\n                <p className=\"text-gray-600\">Financial and performance reports coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'community' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Community</h2>\n                <p className=\"text-gray-600\">Community features and discussions coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'events' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Events</h2>\n                <p className=\"text-gray-600\">Community events and calendar coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'referrals' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Referrals</h2>\n                <p className=\"text-gray-600\">Referral program and tracking coming soon.</p>\n              </div>\n            )}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAjDA;;;;;;;;;;;;;;;;;;;;AAmDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC,eAAe;IAElD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwS;IAEjV,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY;YAAC;YAAY;YAAW;YAAe;YAAa;YAAW;YAAY;YAAW;YAAc;YAAQ;YAAiB;YAAa;YAAY;YAAe;YAAe;YAAa;YAAU;YAAa;YAAW;YAAW;YAAa;YAAU;SAAY;QACnS,IAAI,YAAY,UAAU,QAAQ,CAAC,WAAW;YAC5C,aAAa;QACf;IACF,GAAG;QAAC;KAAa;IAEjB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;YACjC,cAAc,KAAK,KAAK,CAAC,EAAE;QAC7B;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,wBAAwB;IACxB,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,MAAM,WAAW,yHAAA,CAAA,sBAAmB,CAAC,WAAW,IAAI,EAAE;IAEtD,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,8OAAC;gBAAI,WAAW,CAAC,oKAAoK,EACnL,cAAc,kBAAkB,qBAChC;;kCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;;;;;;0CAGd,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM;;;;;;;;;;;kDAGhE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B,KAAK,IAAI,IAAI;;;;;;0DAC1D,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,KAAK,IAAI,KAAK,aAAa;;;;;;4CACrE,KAAK,aAAa,kBACjB,8OAAC;gDAAE,WAAU;;oDACV,KAAK,aAAa,CAAC,KAAK,CAAC,GAAG;oDAAG;oDAAI,KAAK,aAAa,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;0CAOtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;kDAET,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC;gDAAkB,OAAO;0DACvB,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;+CADT;;;;;;;;;;;;;;;;0CAQnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC;wCAAE,WAAU;kDAAmC,KAAK,YAAY,CAAC,cAAc;;;;;;;;;;;;;;;;;;kCAKpF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,gBAAgB;oCACpB,MAAA,mMAAA,CAAA,OAAI;oCACJ,UAAU,gNAAA,CAAA,YAAS;oCACnB,WAAA,gNAAA,CAAA,YAAS;oCACT,OAAA,oMAAA,CAAA,QAAK;oCACL,UAAA,8MAAA,CAAA,WAAQ;oCACR,QAAA,sMAAA,CAAA,SAAM;oCACN,OAAA,oMAAA,CAAA,QAAK;oCACL,UAAA,0MAAA,CAAA,WAAQ;oCACR,YAAA,kNAAA,CAAA,aAAU;oCACV,MAAA,kMAAA,CAAA,OAAI;oCACJ,MAAA,kMAAA,CAAA,OAAI;oCACJ,YAAA,kNAAA,CAAA,aAAU;oCACV,QAAA,sMAAA,CAAA,SAAM;oCACN,OAAA,oMAAA,CAAA,QAAK;oCACL,WAAA,gNAAA,CAAA,YAAS;oCACT,cAAA,sNAAA,CAAA,eAAY;oCACZ,cAAA,2OAAA,CAAA,eAAY;oCACZ,UAAA,8MAAA,CAAA,WAAQ;oCACR,WAAA,kNAAA,CAAA,YAAS;gCACX,CAAC,CAAC,KAAK,IAAI,CAAwC,IAAI,mMAAA,CAAA,OAAI;gCAE3D,MAAM,iBAAiB,CAAC;oCACtB,EAAE,cAAc;oCAChB,IAAI,KAAK,GAAG,EAAE;wCACZ,aAAa,KAAK,GAAG;wCACrB,iCAAiC;wCACjC,MAAM,SAAS,KAAK,GAAG,KAAK,aAAa,eAAe,CAAC,eAAe,EAAE,KAAK,GAAG,EAAE;wCACpF,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI;oCACnC;gCACF;gCAEA,qBACE,8OAAC;8CACC,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,oFAAoF,EAC9F,AAAC,KAAK,GAAG,KAAK,cAAc,cAAc,cAAe,cAAc,KAAK,GAAG,GAC3E,6BACA,sDACJ;;0DAEF,8OAAC;gDAAc,WAAU;;;;;;0DACzB,8OAAC;0DAAM,KAAK,KAAK;;;;;;;;;;;;mCAVZ,KAAK,IAAI;;;;;4BActB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;;gDACX,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;gDAAY;;;;;;;;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAAE,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,aAAa;kDACtF,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,aACV,qCACA,qCACJ;;kEAEF,8OAAC,mMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG1C,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,YACV,qCACA,qCACJ;;kEAEF,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG1C,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,gBACV,qCACA,qCACJ;;kEAEF,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAGhD,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,cACV,qCACA,qCACJ;;kEAEF,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG/C,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,YACV,qCACA,qCACJ;;kEAEF,8OAAC,mMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG1C,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,YACV,qCACA,qCACJ;;kEAEF,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAGhD,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,aACV,qCACA,qCACJ;;kEAEF,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;gCAMjD,cAAc,4BACb;;wCAEG,6BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA4C;;;;;;0EAG1D,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EAGnC,8OAAC;gEACC,SAAS,IAAM,eAAe;gEAC9B,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;sDAST,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;wDAA0B;wDACvB,KAAK,IAAI,IAAI;wDAAO;;;;;;;8DAErC,8OAAC;oDAAE,WAAU;;wDAAqB;wDACD,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,WAAW;wDAAG;;;;;;;8DAE9E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAqB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAsB,KAAK,YAAY,CAAC,cAAc;;;;;;;;;;;;sEAErE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAqB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAsB,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;sEAEtD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAqB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAsB,IAAI,KAAK,KAAK,QAAQ,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sDAM5E,8OAAC;4CAAI,WAAU;sDACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gDACzB,MAAM,gBAAgB;oDACpB,MAAA,mMAAA,CAAA,OAAI;oDACJ,UAAU,gNAAA,CAAA,YAAS;oDACnB,WAAA,gNAAA,CAAA,YAAS;oDACT,OAAA,oMAAA,CAAA,QAAK;oDACL,UAAA,8MAAA,CAAA,WAAQ;oDACR,QAAA,sMAAA,CAAA,SAAM;oDACN,OAAA,oMAAA,CAAA,QAAK;oDACL,UAAA,0MAAA,CAAA,WAAQ;oDACR,YAAA,kNAAA,CAAA,aAAU;oDACV,MAAA,kMAAA,CAAA,OAAI;gDACN,CAAC,CAAC,KAAK,IAAI,CAAwC,IAAI,mMAAA,CAAA,OAAI;gDAE3D,qBACE,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAc,WAAU;;;;;;;;;;;0EAE3B,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B,KAAK,KAAK;;;;;;kFACvD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;mDAVpC,KAAK,IAAI;;;;;4CAepB;;;;;;wCAID,eAAe,6BAAe,8OAAC,kKAAA,CAAA,UAAkB;;;;;wCACjD,eAAe,0BAAY,8OAAC,4JAAA,CAAA,UAAe;;;;;wCAC3C,eAAe,yBAAW,8OAAC,0JAAA,CAAA,UAAc;;;;;wCACzC,eAAe,qCAAuB,8OAAC,oLAAA,CAAA,UAAyB;;;;;wCAChE,eAAe,oCAAsB,8OAAC,kLAAA,CAAA,UAAwB;;;;;;;gCAKlE,cAAc,2BAAa,8OAAC,6IAAA,CAAA,UAAY;;;;;gCAGxC,cAAc,+BAAiB,8OAAC,4IAAA,CAAA,UAAW;;;;;gCAG3C,cAAc,6BAAe,8OAAC,iJAAA,CAAA,UAAc;;;;;gCAG5C,cAAc,2BAAa,8OAAC,+IAAA,CAAA,UAAY;;;;;gCAGxC,cAAc,2BAAa,8OAAC,+IAAA,CAAA,UAAY;;;;;gCAGxC,cAAc,4BAAc,8OAAC,gJAAA,CAAA,UAAa;;;;;gCAG1C,cAAc,8BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,wBACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,iCAAmB,8OAAC,qJAAA,CAAA,UAAkB;;;;;gCAEpD,cAAc,6BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,4BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,+BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,+BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,6BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,0BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,6BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,2BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,2BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,6BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,0BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,6BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C", "debugId": null}}]}