{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/not-found.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Home, ArrowLeft, Search, Building2 } from 'lucide-react';\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"max-w-md w-full text-center px-4\">\n        {/* Logo */}\n        <div className=\"flex items-center justify-center mb-8\">\n          <img\n            src=\"https://managelife.io/logo/ML-Logo.svg\"\n            alt=\"ManageLife\"\n            className=\"h-12 w-auto mr-3\"\n          />\n          <span className=\"text-2xl font-bold gradient-text\">ManageLife</span>\n        </div>\n\n        {/* 404 Illustration */}\n        <div className=\"mb-8\">\n          <div className=\"text-8xl font-bold text-gray-300 mb-4\">404</div>\n          <div className=\"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <Search className=\"w-12 h-12 text-blue-600\" />\n          </div>\n        </div>\n\n        {/* Error Message */}\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n          Page Not Found\n        </h1>\n        <p className=\"text-gray-600 mb-8\">\n          Sorry, we couldn't find the page you're looking for. \n          The page might have been moved, deleted, or you entered the wrong URL.\n        </p>\n\n        {/* Action Buttons */}\n        <div className=\"space-y-4\">\n          <Link\n            href=\"/\"\n            className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow flex items-center justify-center\"\n          >\n            <Home className=\"w-5 h-5 mr-2\" />\n            Go to Homepage\n          </Link>\n          \n          <button\n            onClick={() => window.history.back()}\n            className=\"w-full border-2 border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center\"\n          >\n            <ArrowLeft className=\"w-5 h-5 mr-2\" />\n            Go Back\n          </button>\n        </div>\n\n        {/* Quick Links */}\n        <div className=\"mt-12 pt-8 border-t border-gray-200\">\n          <p className=\"text-sm text-gray-500 mb-4\">Quick Links:</p>\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\n            <Link\n              href=\"/marketplace\"\n              className=\"text-blue-600 hover:text-blue-700 transition-colors\"\n            >\n              Marketplace\n            </Link>\n            <Link\n              href=\"/community\"\n              className=\"text-blue-600 hover:text-blue-700 transition-colors\"\n            >\n              Community\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-blue-600 hover:text-blue-700 transition-colors\"\n            >\n              About Us\n            </Link>\n            <Link\n              href=\"/support\"\n              className=\"text-blue-600 hover:text-blue-700 transition-colors\"\n            >\n              Support\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;8BAIrD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAwC;;;;;;sCACvD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKtB,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAGtD,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAMlC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,8OAAC;4BACC,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;4BAClC,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAM1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAC1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}