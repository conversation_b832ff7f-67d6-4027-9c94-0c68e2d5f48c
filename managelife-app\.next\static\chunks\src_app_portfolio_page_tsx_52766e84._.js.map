{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/portfolio/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { \n  TrendingUp, \n  TrendingDown,\n  DollarSign,\n  Building2,\n  <PERSON><PERSON><PERSON>,\n  BarChart3,\n  Calendar,\n  Filter,\n  Download,\n  Plus,\n  Eye,\n  Edit,\n  Trash2,\n  MapPin,\n  Users,\n  Coins\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface Property {\n  id: string;\n  title: string;\n  location: string;\n  type: 'residential' | 'commercial' | 'industrial';\n  value: number;\n  purchasePrice: number;\n  purchaseDate: string;\n  monthlyIncome: number;\n  occupancyRate: number;\n  roi: number;\n  status: 'active' | 'pending' | 'sold';\n  image: string;\n  tenants: number;\n  maxTenants: number;\n}\n\ninterface PortfolioStats {\n  totalValue: number;\n  totalInvestment: number;\n  totalReturn: number;\n  monthlyIncome: number;\n  averageROI: number;\n  propertiesCount: number;\n  occupancyRate: number;\n}\n\nexport default function PortfolioPage() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'analytics' | 'transactions'>('overview');\n  const [filterType, setFilterType] = useState<'all' | 'residential' | 'commercial' | 'industrial'>('all');\n  const [sortBy, setSortBy] = useState<'value' | 'roi' | 'income' | 'date'>('value');\n\n  // Mock portfolio data\n  const [portfolioStats] = useState<PortfolioStats>({\n    totalValue: 2450000,\n    totalInvestment: 1850000,\n    totalReturn: 600000,\n    monthlyIncome: 18500,\n    averageROI: 12.5,\n    propertiesCount: 8,\n    occupancyRate: 92.3,\n  });\n\n  const [properties] = useState<Property[]>([\n    {\n      id: '1',\n      title: 'Luxury Downtown Apartment',\n      location: 'Manhattan, NY',\n      type: 'residential',\n      value: 850000,\n      purchasePrice: 720000,\n      purchaseDate: '2023-03-15',\n      monthlyIncome: 6500,\n      occupancyRate: 100,\n      roi: 15.2,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 1,\n      maxTenants: 1,\n    },\n    {\n      id: '2',\n      title: 'Modern Office Complex',\n      location: 'Austin, TX',\n      type: 'commercial',\n      value: 1200000,\n      purchasePrice: 950000,\n      purchaseDate: '2022-11-20',\n      monthlyIncome: 8200,\n      occupancyRate: 85,\n      roi: 18.7,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 12,\n      maxTenants: 15,\n    },\n    {\n      id: '3',\n      title: 'Suburban Family Home',\n      location: 'Phoenix, AZ',\n      type: 'residential',\n      value: 400000,\n      purchasePrice: 350000,\n      purchaseDate: '2023-07-10',\n      monthlyIncome: 2800,\n      occupancyRate: 100,\n      roi: 11.4,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 1,\n      maxTenants: 1,\n    },\n  ]);\n\n  // Redirect to login if not authenticated\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login');\n    }\n  }, [user, loading, router]);\n\n  // Allow all authenticated users to access portfolio page\n  // Portfolio management is useful for all user types\n  useEffect(() => {\n    if (user) {\n      console.log('User roles:', user.roles);\n      console.log('User has access to portfolio page');\n    }\n  }, [user]);\n\n  const filteredProperties = properties.filter(property => \n    filterType === 'all' || property.type === filterType\n  );\n\n  const sortedProperties = [...filteredProperties].sort((a, b) => {\n    switch (sortBy) {\n      case 'value':\n        return b.value - a.value;\n      case 'roi':\n        return b.roi - a.roi;\n      case 'income':\n        return b.monthlyIncome - a.monthlyIncome;\n      case 'date':\n        return new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime();\n      default:\n        return 0;\n    }\n  });\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading portfolio...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-4\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">Portfolio Management</h1>\n              <span className=\"bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded\">\n                {user?.roles.includes('portfolio-manager') ? 'Portfolio Manager' :\n                 user?.roles.includes('homeowner') ? 'Property Owner' : 'Investor'}\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n                <Plus className=\"w-4 h-4 mr-2\" />\n                Add Property\n              </button>\n              <button\n                onClick={() => router.push('/dashboard')}\n                className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                Back to Dashboard\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Portfolio Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Portfolio Value</p>\n                <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.totalValue.toLocaleString()}</p>\n              </div>\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                <DollarSign className=\"w-6 h-6 text-blue-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center\">\n              <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n              <span className=\"text-sm text-green-600 font-medium\">+12.5%</span>\n              <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Monthly Income</p>\n                <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.monthlyIncome.toLocaleString()}</p>\n              </div>\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                <Coins className=\"w-6 h-6 text-green-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center\">\n              <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n              <span className=\"text-sm text-green-600 font-medium\">+8.2%</span>\n              <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Average ROI</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{portfolioStats.averageROI}%</p>\n              </div>\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n                <TrendingUp className=\"w-6 h-6 text-purple-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center\">\n              <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n              <span className=\"text-sm text-green-600 font-medium\">+2.1%</span>\n              <span className=\"text-sm text-gray-500 ml-1\">vs last quarter</span>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Occupancy Rate</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{portfolioStats.occupancyRate}%</p>\n              </div>\n              <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n                <Building2 className=\"w-6 h-6 text-orange-600\" />\n              </div>\n            </div>\n            <div className=\"mt-4 flex items-center\">\n              <TrendingDown className=\"w-4 h-4 text-red-500 mr-1\" />\n              <span className=\"text-sm text-red-600 font-medium\">-1.2%</span>\n              <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 mb-8\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"flex space-x-8 px-6\">\n              {[\n                { id: 'overview', label: 'Overview', icon: PieChart },\n                { id: 'properties', label: 'Properties', icon: Building2 },\n                { id: 'analytics', label: 'Analytics', icon: BarChart3 },\n                { id: 'transactions', label: 'Transactions', icon: Calendar },\n              ].map((tab) => {\n                const Icon = tab.icon;\n                return (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id as any)}\n                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                      activeTab === tab.id\n                        ? 'border-blue-500 text-blue-600'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    }`}\n                  >\n                    <Icon className=\"w-4 h-4 mr-2\" />\n                    {tab.label}\n                  </button>\n                );\n              })}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"p-6\">\n            {activeTab === 'overview' && (\n              <div className=\"space-y-6\">\n                <div className=\"grid md:grid-cols-2 gap-6\">\n                  <div className=\"bg-gray-50 rounded-lg p-6\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Portfolio Distribution</h3>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-600\">Residential</span>\n                        <span className=\"font-medium\">65%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div className=\"bg-blue-600 h-2 rounded-full\" style={{ width: '65%' }}></div>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-600\">Commercial</span>\n                        <span className=\"font-medium\">30%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div className=\"bg-green-600 h-2 rounded-full\" style={{ width: '30%' }}></div>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-600\">Industrial</span>\n                        <span className=\"font-medium\">5%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div className=\"bg-purple-600 h-2 rounded-full\" style={{ width: '5%' }}></div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-gray-50 rounded-lg p-6\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activity</h3>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                        <span className=\"text-sm text-gray-600\">Rent payment received - $6,500</span>\n                      </div>\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        <span className=\"text-sm text-gray-600\">Property valuation updated</span>\n                      </div>\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                        <span className=\"text-sm text-gray-600\">Maintenance request submitted</span>\n                      </div>\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                        <span className=\"text-sm text-gray-600\">New tenant application</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'properties' && (\n              <div className=\"space-y-6\">\n                {/* Filters */}\n                <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n                  <div className=\"flex flex-wrap gap-3\">\n                    <select\n                      value={filterType}\n                      onChange={(e) => setFilterType(e.target.value as any)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"all\">All Types</option>\n                      <option value=\"residential\">Residential</option>\n                      <option value=\"commercial\">Commercial</option>\n                      <option value=\"industrial\">Industrial</option>\n                    </select>\n                    \n                    <select\n                      value={sortBy}\n                      onChange={(e) => setSortBy(e.target.value as any)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"value\">Sort by Value</option>\n                      <option value=\"roi\">Sort by ROI</option>\n                      <option value=\"income\">Sort by Income</option>\n                      <option value=\"date\">Sort by Date</option>\n                    </select>\n                  </div>\n                  \n                  <button className=\"flex items-center text-gray-600 hover:text-blue-600 transition-colors\">\n                    <Download className=\"w-4 h-4 mr-2\" />\n                    Export Report\n                  </button>\n                </div>\n\n                {/* Properties Grid */}\n                <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                  {sortedProperties.map((property) => (\n                    <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n                      <img\n                        src={property.image}\n                        alt={property.title}\n                        className=\"w-full h-48 object-cover\"\n                      />\n                      <div className=\"p-6\">\n                        <div className=\"flex items-start justify-between mb-3\">\n                          <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-1\">{property.title}</h3>\n                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                            property.status === 'active' ? 'bg-green-100 text-green-800' :\n                            property.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-gray-100 text-gray-800'\n                          }`}>\n                            {property.status}\n                          </span>\n                        </div>\n                        \n                        <div className=\"flex items-center text-gray-600 mb-3\">\n                          <MapPin className=\"w-4 h-4 mr-1\" />\n                          <span className=\"text-sm\">{property.location}</span>\n                        </div>\n                        \n                        <div className=\"space-y-2 mb-4\">\n                          <div className=\"flex justify-between text-sm\">\n                            <span className=\"text-gray-600\">Current Value</span>\n                            <span className=\"font-medium\">${property.value.toLocaleString()}</span>\n                          </div>\n                          <div className=\"flex justify-between text-sm\">\n                            <span className=\"text-gray-600\">Monthly Income</span>\n                            <span className=\"font-medium text-green-600\">${property.monthlyIncome.toLocaleString()}</span>\n                          </div>\n                          <div className=\"flex justify-between text-sm\">\n                            <span className=\"text-gray-600\">ROI</span>\n                            <span className=\"font-medium text-blue-600\">{property.roi}%</span>\n                          </div>\n                          <div className=\"flex justify-between text-sm\">\n                            <span className=\"text-gray-600\">Occupancy</span>\n                            <span className=\"font-medium\">{property.occupancyRate}%</span>\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center text-sm text-gray-600\">\n                            <Users className=\"w-4 h-4 mr-1\" />\n                            {property.tenants}/{property.maxTenants} tenants\n                          </div>\n                          <div className=\"flex space-x-2\">\n                            <button className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\">\n                              <Eye className=\"w-4 h-4\" />\n                            </button>\n                            <button className=\"p-2 text-gray-400 hover:text-green-600 transition-colors\">\n                              <Edit className=\"w-4 h-4\" />\n                            </button>\n                            <button className=\"p-2 text-gray-400 hover:text-red-600 transition-colors\">\n                              <Trash2 className=\"w-4 h-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'analytics' && (\n              <div className=\"text-center py-12\">\n                <BarChart3 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Analytics Dashboard</h3>\n                <p className=\"text-gray-600\">Detailed analytics and performance metrics coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'transactions' && (\n              <div className=\"text-center py-12\">\n                <Calendar className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Transaction History</h3>\n                <p className=\"text-gray-600\">Complete transaction history and financial reports coming soon.</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;;;AAtBA;;;;;AAmDe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4D;IACrG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuD;IAClG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1E,sBAAsB;IACtB,MAAM,CAAC,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QAChD,YAAY;QACZ,iBAAiB;QACjB,aAAa;QACb,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACxC;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;KACD;IAED,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,yDAAyD;IACzD,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,eAAe,KAAK,KAAK;gBACrC,QAAQ,GAAG,CAAC;YACd;QACF;kCAAG;QAAC;KAAK;IAET,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,WAC3C,eAAe,SAAS,SAAS,IAAI,KAAK;IAG5C,MAAM,mBAAmB;WAAI;KAAmB,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG;YACtB,KAAK;gBACH,OAAO,EAAE,aAAa,GAAG,EAAE,aAAa;YAC1C,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;YAC9E;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAK,WAAU;kDACb,CAAA,iBAAA,2BAAA,KAAM,KAAK,CAAC,QAAQ,CAAC,wBAAuB,sBAC5C,CAAA,iBAAA,2BAAA,KAAM,KAAK,CAAC,QAAQ,CAAC,gBAAe,mBAAmB;;;;;;;;;;;;0CAG5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;;4DAAmC;4DAAE,eAAe,UAAU,CAAC,cAAc;;;;;;;;;;;;;0DAE5F,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;0DACrD,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAIjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;;4DAAmC;4DAAE,eAAe,aAAa,CAAC,cAAc;;;;;;;;;;;;;0DAE/F,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;0DACrD,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAIjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;;4DAAoC,eAAe,UAAU;4DAAC;;;;;;;;;;;;;0DAE7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;0DACrD,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAIjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;;4DAAoC,eAAe,aAAa;4DAAC;;;;;;;;;;;;;0DAEhF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,IAAI;4CAAY,OAAO;4CAAY,MAAM,iNAAA,CAAA,WAAQ;wCAAC;wCACpD;4CAAE,IAAI;4CAAc,OAAO;4CAAc,MAAM,mNAAA,CAAA,YAAS;wCAAC;wCACzD;4CAAE,IAAI;4CAAa,OAAO;4CAAa,MAAM,qNAAA,CAAA,YAAS;wCAAC;wCACvD;4CAAE,IAAI;4CAAgB,OAAO;4CAAgB,MAAM,6MAAA,CAAA,WAAQ;wCAAC;qCAC7D,CAAC,GAAG,CAAC,CAAC;wCACL,MAAM,OAAO,IAAI,IAAI;wCACrB,qBACE,6LAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,AAAC,8DAIX,OAHC,cAAc,IAAI,EAAE,GAChB,kCACA;;8DAGN,6LAAC;oDAAK,WAAU;;;;;;gDACf,IAAI,KAAK;;2CATL,IAAI,EAAE;;;;;oCAYjB;;;;;;;;;;;0CAKJ,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,4BACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;wEAA+B,OAAO;4EAAE,OAAO;wEAAM;;;;;;;;;;;8EAGtE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;wEAAgC,OAAO;4EAAE,OAAO;wEAAM;;;;;;;;;;;8EAGvE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAK,WAAU;sFAAc;;;;;;;;;;;;8EAEhC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;wEAAiC,OAAO;4EAAE,OAAO;wEAAK;;;;;;;;;;;;;;;;;;;;;;;8DAK3E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQnD,cAAc,8BACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,6LAAC;wEAAO,OAAM;kFAAc;;;;;;kFAC5B,6LAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,6LAAC;wEAAO,OAAM;kFAAa;;;;;;;;;;;;0EAG7B,6LAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gEACzC,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,6LAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,6LAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,6LAAC;wEAAO,OAAM;kFAAO;;;;;;;;;;;;;;;;;;kEAIzB,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAMzC,6LAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEACC,KAAK,SAAS,KAAK;gEACnB,KAAK,SAAS,KAAK;gEACnB,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FAAoD,SAAS,KAAK;;;;;;0FAChF,6LAAC;gFAAK,WAAW,AAAC,8CAIjB,OAHC,SAAS,MAAM,KAAK,WAAW,gCAC/B,SAAS,MAAM,KAAK,YAAY,kCAChC;0FAEC,SAAS,MAAM;;;;;;;;;;;;kFAIpB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;gFAAK,WAAU;0FAAW,SAAS,QAAQ;;;;;;;;;;;;kFAG9C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAgB;;;;;;kGAChC,6LAAC;wFAAK,WAAU;;4FAAc;4FAAE,SAAS,KAAK,CAAC,cAAc;;;;;;;;;;;;;0FAE/D,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAgB;;;;;;kGAChC,6LAAC;wFAAK,WAAU;;4FAA6B;4FAAE,SAAS,aAAa,CAAC,cAAc;;;;;;;;;;;;;0FAEtF,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAgB;;;;;;kGAChC,6LAAC;wFAAK,WAAU;;4FAA6B,SAAS,GAAG;4FAAC;;;;;;;;;;;;;0FAE5D,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAK,WAAU;kGAAgB;;;;;;kGAChC,6LAAC;wFAAK,WAAU;;4FAAe,SAAS,aAAa;4FAAC;;;;;;;;;;;;;;;;;;;kFAI1D,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAChB,SAAS,OAAO;oFAAC;oFAAE,SAAS,UAAU;oFAAC;;;;;;;0FAE1C,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAO,WAAU;kGAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;;;;;;kGAEjB,6LAAC;wFAAO,WAAU;kGAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;;;;;;kGAElB,6LAAC;wFAAO,WAAU;kGAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDAvDlB,SAAS,EAAE;;;;;;;;;;;;;;;;oCAkE5B,cAAc,6BACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;oCAIhC,cAAc,gCACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;GA/awB;;QACI,kIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}