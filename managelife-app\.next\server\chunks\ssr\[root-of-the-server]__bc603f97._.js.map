{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { ArrowRight, Building2, Coins, Shield, Users, Zap, TrendingUp } from \"lucide-react\";\n\nexport default function Home() {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      {/* Header */}\n      <header className=\"border-b bg-white/80 backdrop-blur-md sticky top-0 z-50\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <img\n                src=\"https://managelife.io/logo/ML-Logo.svg\"\n                alt=\"ManageLife\"\n                className=\"h-8 w-auto\"\n              />\n              <span className=\"text-xl font-bold gradient-text\">ManageLife</span>\n            </Link>\n            <nav className=\"hidden md:flex items-center space-x-8\">\n              <Link href=\"/marketplace\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                Marketplace\n              </Link>\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                Team\n              </Link>\n              <a href=\"https://managelife.io\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                Solutions\n              </a>\n              <Link href=\"/community\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                Community\n              </Link>\n              <Link href=\"/docs\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                Docs\n              </Link>\n              <a href=\"https://managelife.io\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-600 hover:text-blue-600 transition-colors\">\n                Official Site\n              </a>\n            </nav>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/auth/login\"\n                className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n              >\n                Sign In\n              </Link>\n              <Link\n                href=\"/auth/register\"\n                className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 web3-glow\"\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n        <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n          <div className=\"text-center max-w-4xl mx-auto\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Real-World Ownership,\n              <span className=\"block gradient-text\">Reinvented</span>\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-2xl mx-auto\">\n              MLife bridges institutional-grade real estate with the accessibility and programmability of blockchain. Own real assets, earn real returns, and govern your portfolio from anywhere.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"https://managelife.io/whitepaper\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:shadow-xl transition-all duration-300 web3-glow flex items-center justify-center\"\n              >\n                Download Whitepaper\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </a>\n              <Link\n                href=\"/auth/register\"\n                className=\"border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300\"\n              >\n                Join the Waitlist\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Revolutionary Real Estate Platform\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Experience the future of property investment with our comprehensive tokenization platform\n            </p>\n          </div>\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <div className=\"bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6\">\n                <Coins className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">NFT Tokenization</h3>\n              <p className=\"text-gray-600\">\n                Convert your properties into NFTs (NFTi) and rental agreements into NFTr tokens for seamless blockchain-based management.\n              </p>\n            </div>\n            <div className=\"bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6\">\n                <TrendingUp className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">$MLIFE Rewards</h3>\n              <p className=\"text-gray-600\">\n                Earn $MLIFE tokens for timely rent payments, successful transactions, and community participation.\n              </p>\n            </div>\n            <div className=\"bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6\">\n                <Shield className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Secure & Transparent</h3>\n              <p className=\"text-gray-600\">\n                All transactions are secured by blockchain technology with full transparency and immutable records.\n              </p>\n            </div>\n            <div className=\"bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100\">\n              <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-6\">\n                <Users className=\"w-6 h-6 text-orange-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Multi-Role Platform</h3>\n              <p className=\"text-gray-600\">\n                Whether you're a homeowner, renter, buyer, or portfolio manager, we have tools designed for your needs.\n              </p>\n            </div>\n            <div className=\"bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100\">\n              <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-6\">\n                <Zap className=\"w-6 h-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Instant Transactions</h3>\n              <p className=\"text-gray-600\">\n                Fast and efficient property transactions with smart contracts automating the entire process.\n              </p>\n            </div>\n            <div className=\"bg-white p-8 rounded-xl shadow-lg card-hover border border-gray-100\">\n              <div className=\"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-6\">\n                <Building2 className=\"w-6 h-6 text-indigo-600\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Property Management</h3>\n              <p className=\"text-gray-600\">\n                Comprehensive tools for managing properties, tenants, maintenance requests, and financial analytics.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-blue-600 to-purple-600\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            Ready to Transform Your Real Estate Portfolio?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n            Join thousands of property owners, renters, and investors who are already using ManageLife to revolutionize their real estate experience.\n          </p>\n          <Link\n            href=\"/auth/register\"\n            className=\"bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-all duration-300 inline-flex items-center\"\n          >\n            Get Started Today\n            <ArrowRight className=\"ml-2 w-5 h-5\" />\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <Building2 className=\"w-5 h-5 text-white\" />\n                </div>\n                <span className=\"text-xl font-bold\">ManageLife</span>\n              </div>\n              <p className=\"text-gray-400 mb-4\">\n                Revolutionizing real estate through blockchain technology and tokenization.\n              </p>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Platform</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><Link href=\"/marketplace\" className=\"hover:text-white transition-colors\">Marketplace</Link></li>\n                <li><Link href=\"/dashboard\" className=\"hover:text-white transition-colors\">Dashboard</Link></li>\n                <li><Link href=\"/tokenize\" className=\"hover:text-white transition-colors\">Tokenize Property</Link></li>\n                <li><Link href=\"/rewards\" className=\"hover:text-white transition-colors\">Rewards</Link></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Resources</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><Link href=\"/docs\" className=\"hover:text-white transition-colors\">Documentation</Link></li>\n                <li><a href=\"https://managelife.io/whitepaper\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:text-white transition-colors\">Official Whitepaper</a></li>\n                <li><Link href=\"/blog\" className=\"hover:text-white transition-colors\">Blog</Link></li>\n                <li><a href=\"https://managelife.io\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:text-white transition-colors\">Official Website</a></li>\n                <li><Link href=\"/support\" className=\"hover:text-white transition-colors\">Support</Link></li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">Community</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><a href=\"https://www.facebook.com/ManageLife.IO\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:text-white transition-colors\">Facebook</a></li>\n                <li><a href=\"https://x.com/ManageLife_io\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:text-white transition-colors\">Twitter/X</a></li>\n                <li><a href=\"https://www.instagram.com/managelife.io/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:text-white transition-colors\">Instagram</a></li>\n                <li><a href=\"https://www.linkedin.com/company/managelife-io/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:text-white transition-colors\">LinkedIn</a></li>\n                <li><a href=\"https://managelife.io\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"hover:text-white transition-colors\">Official Website</a></li>\n              </ul>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n            <p>&copy; 2025 ManageLife. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAAsD;;;;;;kDAG1F,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAsD;;;;;;kDAGpF,8OAAC;wCAAE,MAAK;wCAAwB,QAAO;wCAAS,KAAI;wCAAsB,WAAU;kDAAsD;;;;;;kDAG1I,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAsD;;;;;;kDAGxF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAsD;;;;;;kDAGnF,8OAAC;wCAAE,MAAK;wCAAwB,QAAO;wCAAS,KAAI;wCAAsB,WAAU;kDAAsD;;;;;;;;;;;;0CAI5I,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAoD;sDAEhE,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAExC,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;gDACX;8DAEC,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAIpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,WAAU;kEAAqC;;;;;;;;;;;8DAC7E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAAqC;;;;;;;;;;;8DAC3E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAqC;;;;;;;;;;;8DAC1E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAG7E,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAqC;;;;;;;;;;;8DACtE,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAmC,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAAqC;;;;;;;;;;;8DACxI,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAqC;;;;;;;;;;;8DACtE,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAwB,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAAqC;;;;;;;;;;;8DAC7H,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAG7E,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAyC,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAAqC;;;;;;;;;;;8DAC9I,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAA8B,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAAqC;;;;;;;;;;;8DACnI,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAA2C,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAAqC;;;;;;;;;;;8DAChJ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAkD,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAAqC;;;;;;;;;;;8DACvJ,8OAAC;8DAAG,cAAA,8OAAC;wDAAE,MAAK;wDAAwB,QAAO;wDAAS,KAAI;wDAAsB,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAInI,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}