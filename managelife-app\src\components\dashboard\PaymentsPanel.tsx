'use client';

import { useState } from 'react';
import { 
  CreditCard,
  DollarSign,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Download,
  Eye,
  Plus,
  Filter,
  Search,
  Wallet,
  Building,
  Receipt,
  TrendingUp,
  ArrowUpRight
} from 'lucide-react';

interface Payment {
  id: string;
  type: 'rent' | 'deposit' | 'utilities' | 'maintenance' | 'late_fee';
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: 'pending' | 'paid' | 'overdue' | 'partial';
  description: string;
  property: string;
  paymentMethod?: string;
  transactionId?: string;
  lateFee?: number;
  receipt?: string;
}

export default function PaymentsPanel() {
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'paid' | 'overdue'>('all');
  const [filterType, setFilterType] = useState<'all' | 'rent' | 'deposit' | 'utilities' | 'maintenance' | 'late_fee'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock payments data
  const [payments] = useState<Payment[]>([
    {
      id: '1',
      type: 'rent',
      amount: 2800,
      dueDate: '2024-02-01T00:00:00Z',
      status: 'pending',
      description: 'Monthly Rent - February 2024',
      property: '123 Main St, Downtown, NY 10001',
    },
    {
      id: '2',
      type: 'rent',
      amount: 2800,
      dueDate: '2024-01-01T00:00:00Z',
      paidDate: '2023-12-28T10:30:00Z',
      status: 'paid',
      description: 'Monthly Rent - January 2024',
      property: '123 Main St, Downtown, NY 10001',
      paymentMethod: 'Credit Card',
      transactionId: 'TXN-2024-001',
      receipt: 'receipt-jan-2024.pdf',
    },
    {
      id: '3',
      type: 'utilities',
      amount: 150,
      dueDate: '2024-01-15T00:00:00Z',
      paidDate: '2024-01-14T14:20:00Z',
      status: 'paid',
      description: 'Electricity & Gas - January 2024',
      property: '123 Main St, Downtown, NY 10001',
      paymentMethod: 'Bank Transfer',
      transactionId: 'TXN-2024-002',
    },
    {
      id: '4',
      type: 'maintenance',
      amount: 75,
      dueDate: '2024-01-20T00:00:00Z',
      status: 'overdue',
      description: 'Kitchen Faucet Repair',
      property: '123 Main St, Downtown, NY 10001',
      lateFee: 25,
    },
    {
      id: '5',
      type: 'deposit',
      amount: 2800,
      dueDate: '2023-06-01T00:00:00Z',
      paidDate: '2023-05-25T09:15:00Z',
      status: 'paid',
      description: 'Security Deposit',
      property: '123 Main St, Downtown, NY 10001',
      paymentMethod: 'Bank Transfer',
      transactionId: 'TXN-2023-001',
    },
  ]);

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.property.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || payment.status === filterStatus;
    const matchesType = filterType === 'all' || payment.type === filterType;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const totalPaid = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);
  const totalPending = payments.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0);
  const totalOverdue = payments.filter(p => p.status === 'overdue').reduce((sum, p) => sum + p.amount + (p.lateFee || 0), 0);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'partial':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-4 h-4" />;
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'overdue':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'rent':
        return <Building className="w-5 h-5 text-blue-600" />;
      case 'utilities':
        return <TrendingUp className="w-5 h-5 text-green-600" />;
      case 'maintenance':
        return <Receipt className="w-5 h-5 text-orange-600" />;
      case 'deposit':
        return <Wallet className="w-5 h-5 text-purple-600" />;
      case 'late_fee':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      default:
        return <DollarSign className="w-5 h-5 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const isOverdue = (dueDate: string, status: string) => {
    return status !== 'paid' && new Date(dueDate) < new Date();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Payments</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Make Payment
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Paid</p>
              <p className="text-2xl font-bold text-gray-900">${totalPaid.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">${totalPending.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-gray-900">${totalOverdue.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search payments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
            </select>

            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="rent">Rent</option>
              <option value="utilities">Utilities</option>
              <option value="maintenance">Maintenance</option>
              <option value="deposit">Deposit</option>
              <option value="late_fee">Late Fee</option>
            </select>
          </div>
        </div>
      </div>

      {/* Payments List */}
      <div className="space-y-4">
        {filteredPayments.map((payment) => (
          <div key={payment.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 mt-1">
                  {getTypeIcon(payment.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{payment.description}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                      {getStatusIcon(payment.status)}
                      <span className="ml-1 capitalize">{payment.status}</span>
                    </span>
                    {isOverdue(payment.dueDate, payment.status) && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Overdue
                      </span>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{payment.property}</p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Amount</p>
                      <p className="font-medium">${payment.amount.toLocaleString()}</p>
                      {payment.lateFee && (
                        <p className="text-red-600 text-xs">+ ${payment.lateFee} late fee</p>
                      )}
                    </div>
                    <div>
                      <p className="text-gray-500">Due Date</p>
                      <p className="font-medium">{formatDate(payment.dueDate)}</p>
                    </div>
                    {payment.paidDate && (
                      <div>
                        <p className="text-gray-500">Paid Date</p>
                        <p className="font-medium">{formatDate(payment.paidDate)}</p>
                      </div>
                    )}
                    {payment.paymentMethod && (
                      <div>
                        <p className="text-gray-500">Method</p>
                        <p className="font-medium">{payment.paymentMethod}</p>
                      </div>
                    )}
                  </div>
                  
                  {payment.transactionId && (
                    <div className="mt-3 text-xs text-gray-500">
                      Transaction ID: {payment.transactionId}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2 ml-4">
                {payment.status === 'pending' && (
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    Pay Now
                  </button>
                )}
                
                {payment.receipt && (
                  <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <Download className="w-4 h-4" />
                  </button>
                )}
                
                <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <Eye className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredPayments.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No payments found</h3>
          <p className="text-gray-600">
            {searchTerm || filterStatus !== 'all' || filterType !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'No payment history available.'
            }
          </p>
        </div>
      )}
    </div>
  );
}
