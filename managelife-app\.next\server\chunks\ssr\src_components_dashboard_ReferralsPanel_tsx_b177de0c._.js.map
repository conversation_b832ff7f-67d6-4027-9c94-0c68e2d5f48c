{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/ReferralsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  UserPlus,\n  Gift,\n  DollarSign,\n  Share2,\n  Copy,\n  Mail,\n  MessageSquare,\n  Users,\n  TrendingUp,\n  CheckCircle,\n  Clock,\n  Star,\n  Award,\n  Link,\n  QrCode,\n  Facebook,\n  Twitter,\n  Instagram,\n  Send,\n  Target,\n  Zap\n} from 'lucide-react';\n\ninterface Referral {\n  id: string;\n  referredUser: {\n    name: string;\n    email: string;\n    avatar?: string;\n  };\n  status: 'pending' | 'completed' | 'rewarded';\n  referralDate: string;\n  completionDate?: string;\n  reward: {\n    amount: number;\n    type: 'cash' | 'credit' | 'token';\n    status: 'pending' | 'paid' | 'processing';\n  };\n  activity: string;\n}\n\ninterface ReferralReward {\n  id: string;\n  title: string;\n  description: string;\n  amount: number;\n  type: 'cash' | 'credit' | 'token';\n  requirement: string;\n  icon: string;\n  isActive: boolean;\n}\n\nexport default function ReferralsPanel() {\n  const [activeTab, setActiveTab] = useState<'overview' | 'invite' | 'history' | 'rewards'>('overview');\n  const [referralCode] = useState('MLIFE-ABC123');\n  const [referralLink] = useState('https://managelife.com/join?ref=MLIFE-ABC123');\n\n  // Mock referrals data\n  const [referrals] = useState<Referral[]>([\n    {\n      id: '1',\n      referredUser: {\n        name: '<PERSON>',\n        email: '<EMAIL>',\n        avatar: '/api/placeholder/150/150'\n      },\n      status: 'rewarded',\n      referralDate: '2024-01-15T10:00:00Z',\n      completionDate: '2024-01-20T14:30:00Z',\n      reward: {\n        amount: 50,\n        type: 'cash',\n        status: 'paid'\n      },\n      activity: 'Completed first property investment'\n    },\n    {\n      id: '2',\n      referredUser: {\n        name: 'Sarah Johnson',\n        email: '<EMAIL>'\n      },\n      status: 'completed',\n      referralDate: '2024-01-20T16:45:00Z',\n      completionDate: '2024-01-25T09:15:00Z',\n      reward: {\n        amount: 25,\n        type: 'credit',\n        status: 'processing'\n      },\n      activity: 'Signed up and verified account'\n    },\n    {\n      id: '3',\n      referredUser: {\n        name: 'Mike Chen',\n        email: '<EMAIL>'\n      },\n      status: 'pending',\n      referralDate: '2024-01-22T12:20:00Z',\n      reward: {\n        amount: 25,\n        type: 'credit',\n        status: 'pending'\n      },\n      activity: 'Account created, pending verification'\n    }\n  ]);\n\n  // Mock rewards data\n  const [rewards] = useState<ReferralReward[]>([\n    {\n      id: '1',\n      title: 'Friend Sign-up Bonus',\n      description: 'Earn $25 when your friend signs up and verifies their account',\n      amount: 25,\n      type: 'cash',\n      requirement: 'Friend completes account verification',\n      icon: 'user-plus',\n      isActive: true\n    },\n    {\n      id: '2',\n      title: 'First Investment Bonus',\n      description: 'Earn $50 when your friend makes their first property investment',\n      amount: 50,\n      type: 'cash',\n      requirement: 'Friend invests in their first property',\n      icon: 'building',\n      isActive: true\n    },\n    {\n      id: '3',\n      title: 'Token Reward',\n      description: 'Earn 100 $MLife tokens for each successful referral',\n      amount: 100,\n      type: 'token',\n      requirement: 'Friend completes onboarding process',\n      icon: 'coins',\n      isActive: true\n    },\n    {\n      id: '4',\n      title: 'VIP Referral Bonus',\n      description: 'Special bonus for referring premium members',\n      amount: 100,\n      type: 'cash',\n      requirement: 'Friend upgrades to premium membership',\n      icon: 'star',\n      isActive: false\n    }\n  ]);\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      case 'rewarded':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getRewardStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'processing':\n        return 'bg-blue-100 text-blue-800';\n      case 'paid':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  };\n\n  const totalReferrals = referrals.length;\n  const completedReferrals = referrals.filter(r => r.status === 'completed' || r.status === 'rewarded').length;\n  const totalEarnings = referrals\n    .filter(r => r.status === 'rewarded')\n    .reduce((sum, r) => sum + r.reward.amount, 0);\n  const pendingEarnings = referrals\n    .filter(r => r.reward.status === 'processing')\n    .reduce((sum, r) => sum + r.reward.amount, 0);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Referral Program</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Share2 className=\"w-4 h-4 mr-2\" />\n          Invite Friends\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Users className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Referrals</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalReferrals}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CheckCircle className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Successful</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{completedReferrals}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Earned</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${totalEarnings}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <Clock className=\"w-6 h-6 text-orange-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${pendingEarnings}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {[\n              { id: 'overview', label: 'Overview', icon: TrendingUp },\n              { id: 'invite', label: 'Invite Friends', icon: UserPlus },\n              { id: 'history', label: 'Referral History', icon: Clock },\n              { id: 'rewards', label: 'Rewards', icon: Gift },\n            ].map((tab) => {\n              const IconComponent = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <IconComponent className=\"w-4 h-4\" />\n                  <span>{tab.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Overview Tab */}\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Your Referral Performance</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Conversion Rate</span>\n                      <span className=\"font-semibold\">{completedReferrals > 0 ? Math.round((completedReferrals / totalReferrals) * 100) : 0}%</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Average Reward</span>\n                      <span className=\"font-semibold\">${completedReferrals > 0 ? Math.round(totalEarnings / completedReferrals) : 0}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">This Month</span>\n                      <span className=\"font-semibold text-green-600\">+{referrals.filter(r => new Date(r.referralDate).getMonth() === new Date().getMonth()).length} referrals</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n                  <div className=\"space-y-3\">\n                    <button className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\">\n                      <Share2 className=\"w-4 h-4 mr-2\" />\n                      Share Referral Link\n                    </button>\n                    <button className=\"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center\">\n                      <Copy className=\"w-4 h-4 mr-2\" />\n                      Copy Referral Code\n                    </button>\n                    <button className=\"w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center\">\n                      <QrCode className=\"w-4 h-4 mr-2\" />\n                      Generate QR Code\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Referrals</h3>\n                <div className=\"space-y-4\">\n                  {referrals.slice(0, 3).map((referral) => (\n                    <div key={referral.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                      <div className=\"flex items-center space-x-3\">\n                        {referral.referredUser.avatar ? (\n                          <img\n                            src={referral.referredUser.avatar}\n                            alt={referral.referredUser.name}\n                            className=\"w-10 h-10 rounded-full object-cover\"\n                          />\n                        ) : (\n                          <div className=\"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\">\n                            <UserPlus className=\"w-5 h-5 text-gray-400\" />\n                          </div>\n                        )}\n                        <div>\n                          <p className=\"font-medium text-gray-900\">{referral.referredUser.name}</p>\n                          <p className=\"text-sm text-gray-600\">{referral.activity}</p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(referral.status)}`}>\n                          {referral.status}\n                        </span>\n                        <p className=\"text-sm text-gray-600 mt-1\">${referral.reward.amount}</p>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Invite Tab */}\n          {activeTab === 'invite' && (\n            <div className=\"space-y-6\">\n              <div className=\"text-center\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Invite Friends & Earn Rewards</h3>\n                <p className=\"text-gray-600\">Share ManageLife with your friends and earn rewards when they join!</p>\n              </div>\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h4 className=\"font-semibold text-gray-900 mb-4\">Your Referral Code</h4>\n                  <div className=\"flex items-center space-x-2 mb-4\">\n                    <input\n                      type=\"text\"\n                      value={referralCode}\n                      readOnly\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50\"\n                    />\n                    <button\n                      onClick={() => copyToClipboard(referralCode)}\n                      className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                      <Copy className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n\n                  <h4 className=\"font-semibold text-gray-900 mb-4\">Your Referral Link</h4>\n                  <div className=\"flex items-center space-x-2 mb-6\">\n                    <input\n                      type=\"text\"\n                      value={referralLink}\n                      readOnly\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm\"\n                    />\n                    <button\n                      onClick={() => copyToClipboard(referralLink)}\n                      className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                      <Copy className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n\n                  <div className=\"flex space-x-2\">\n                    <button className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\">\n                      <Mail className=\"w-4 h-4 mr-2\" />\n                      Email\n                    </button>\n                    <button className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center\">\n                      <MessageSquare className=\"w-4 h-4 mr-2\" />\n                      SMS\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h4 className=\"font-semibold text-gray-900 mb-4\">Share on Social Media</h4>\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    <button className=\"bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center\">\n                      <Facebook className=\"w-5 h-5 mr-2\" />\n                      Facebook\n                    </button>\n                    <button className=\"bg-blue-400 text-white py-3 px-4 rounded-lg hover:bg-blue-500 transition-colors flex items-center justify-center\">\n                      <Twitter className=\"w-5 h-5 mr-2\" />\n                      Twitter\n                    </button>\n                    <button className=\"bg-pink-600 text-white py-3 px-4 rounded-lg hover:bg-pink-700 transition-colors flex items-center justify-center\">\n                      <Instagram className=\"w-5 h-5 mr-2\" />\n                      Instagram\n                    </button>\n                    <button className=\"bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center\">\n                      <Link className=\"w-5 h-5 mr-2\" />\n                      Copy Link\n                    </button>\n                  </div>\n\n                  <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n                    <h5 className=\"font-medium text-blue-900 mb-2\">Pro Tip!</h5>\n                    <p className=\"text-sm text-blue-800\">\n                      Personal messages work best! Tell your friends why you love ManageLife and how it's helped you.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* History Tab */}\n          {activeTab === 'history' && (\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Referral History</h3>\n              {referrals.map((referral) => (\n                <div key={referral.id} className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start space-x-4\">\n                      {referral.referredUser.avatar ? (\n                        <img\n                          src={referral.referredUser.avatar}\n                          alt={referral.referredUser.name}\n                          className=\"w-12 h-12 rounded-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center\">\n                          <UserPlus className=\"w-6 h-6 text-gray-400\" />\n                        </div>\n                      )}\n                      <div>\n                        <h4 className=\"font-semibold text-gray-900\">{referral.referredUser.name}</h4>\n                        <p className=\"text-sm text-gray-600\">{referral.referredUser.email}</p>\n                        <p className=\"text-sm text-gray-600 mt-1\">{referral.activity}</p>\n                        <div className=\"flex items-center space-x-4 mt-2 text-sm text-gray-500\">\n                          <span>Referred: {formatDate(referral.referralDate)}</span>\n                          {referral.completionDate && (\n                            <span>Completed: {formatDate(referral.completionDate)}</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(referral.status)}`}>\n                        {referral.status}\n                      </span>\n                      <div className=\"mt-2\">\n                        <p className=\"font-semibold text-gray-900\">${referral.reward.amount}</p>\n                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRewardStatusColor(referral.reward.status)}`}>\n                          {referral.reward.status}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {/* Rewards Tab */}\n          {activeTab === 'rewards' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Available Rewards</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {rewards.map((reward) => (\n                  <div key={reward.id} className={`border rounded-lg p-6 ${reward.isActive ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-gray-50'}`}>\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${reward.isActive ? 'bg-blue-100' : 'bg-gray-200'}`}>\n                          {reward.icon === 'user-plus' && <UserPlus className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}\n                          {reward.icon === 'building' && <Target className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}\n                          {reward.icon === 'coins' && <Zap className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}\n                          {reward.icon === 'star' && <Star className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}\n                        </div>\n                        <div>\n                          <h4 className={`font-semibold ${reward.isActive ? 'text-gray-900' : 'text-gray-500'}`}>{reward.title}</h4>\n                          <p className={`text-sm ${reward.isActive ? 'text-gray-600' : 'text-gray-400'}`}>{reward.description}</p>\n                        </div>\n                      </div>\n                      {!reward.isActive && (\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600\">\n                          Coming Soon\n                        </span>\n                      )}\n                    </div>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex justify-between\">\n                        <span className={`text-sm ${reward.isActive ? 'text-gray-600' : 'text-gray-400'}`}>Reward Amount</span>\n                        <span className={`font-semibold ${reward.isActive ? 'text-gray-900' : 'text-gray-500'}`}>\n                          {reward.type === 'token' ? `${reward.amount} $MLife` : `$${reward.amount}`}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className={`text-sm ${reward.isActive ? 'text-gray-600' : 'text-gray-400'}`}>Requirement</span>\n                        <span className={`text-sm ${reward.isActive ? 'text-gray-900' : 'text-gray-500'}`}>{reward.requirement}</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAwDe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiD;IAC1F,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAChC,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEhC,sBAAsB;IACtB,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvC;YACE,IAAI;YACJ,cAAc;gBACZ,MAAM;gBACN,OAAO;gBACP,QAAQ;YACV;YACA,QAAQ;YACR,cAAc;YACd,gBAAgB;YAChB,QAAQ;gBACN,QAAQ;gBACR,MAAM;gBACN,QAAQ;YACV;YACA,UAAU;QACZ;QACA;YACE,IAAI;YACJ,cAAc;gBACZ,MAAM;gBACN,OAAO;YACT;YACA,QAAQ;YACR,cAAc;YACd,gBAAgB;YAChB,QAAQ;gBACN,QAAQ;gBACR,MAAM;gBACN,QAAQ;YACV;YACA,UAAU;QACZ;QACA;YACE,IAAI;YACJ,cAAc;gBACZ,MAAM;gBACN,OAAO;YACT;YACA,QAAQ;YACR,cAAc;YACd,QAAQ;gBACN,QAAQ;gBACR,MAAM;gBACN,QAAQ;YACV;YACA,UAAU;QACZ;KACD;IAED,oBAAoB;IACpB,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QAC3C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;QACZ;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,0CAA0C;IAC5C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,UAAU,MAAM;IACvC,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,MAAM,KAAK,YAAY,MAAM;IAC5G,MAAM,gBAAgB,UACnB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;IAC7C,MAAM,kBAAkB,UACrB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,MAAM,KAAK,cAChC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDAAmC;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDAAmC;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM,kNAAA,CAAA,aAAU;gCAAC;gCACtD;oCAAE,IAAI;oCAAU,OAAO;oCAAkB,MAAM,8MAAA,CAAA,WAAQ;gCAAC;gCACxD;oCAAE,IAAI;oCAAW,OAAO;oCAAoB,MAAM,oMAAA,CAAA,QAAK;gCAAC;gCACxD;oCAAE,IAAI;oCAAW,OAAO;oCAAW,MAAM,kMAAA,CAAA,OAAI;gCAAC;6BAC/C,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,gBAAgB,IAAI,IAAI;gCAC9B,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,uFAAuF,EACjG,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;;sDAEF,8OAAC;4CAAc,WAAU;;;;;;sDACzB,8OAAC;sDAAM,IAAI,KAAK;;;;;;;mCATX,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;;4BAEZ,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAAiB,qBAAqB,IAAI,KAAK,KAAK,CAAC,AAAC,qBAAqB,iBAAkB,OAAO;4EAAE;;;;;;;;;;;;;0EAExH,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAAgB;4EAAE,qBAAqB,IAAI,KAAK,KAAK,CAAC,gBAAgB,sBAAsB;;;;;;;;;;;;;0EAE9G,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAA+B;4EAAE,UAAU,MAAM,CAAC,CAAA,IAAK,IAAI,KAAK,EAAE,YAAY,EAAE,QAAQ,OAAO,IAAI,OAAO,QAAQ,IAAI,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;0DAKnJ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGrC,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC1B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC;gEAAI,WAAU;;oEACZ,SAAS,YAAY,CAAC,MAAM,iBAC3B,8OAAC;wEACC,KAAK,SAAS,YAAY,CAAC,MAAM;wEACjC,KAAK,SAAS,YAAY,CAAC,IAAI;wEAC/B,WAAU;;;;;6FAGZ,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;kFAGxB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAA6B,SAAS,YAAY,CAAC,IAAI;;;;;;0FACpE,8OAAC;gFAAE,WAAU;0FAAyB,SAAS,QAAQ;;;;;;;;;;;;;;;;;;0EAG3D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,SAAS,MAAM,GAAG;kFAC1H,SAAS,MAAM;;;;;;kFAElB,8OAAC;wEAAE,WAAU;;4EAA6B;4EAAE,SAAS,MAAM,CAAC,MAAM;;;;;;;;;;;;;;uDAtB5D,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;4BAgC9B,cAAc,0BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEACC,SAAS,IAAM,gBAAgB;gEAC/B,WAAU;0EAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAIpB,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEACC,SAAS,IAAM,gBAAgB;gEAC/B,WAAU;0EAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAIpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,wNAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;0DAMhD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,wMAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGtC,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,4MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGxC,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;kEAKrC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAU9C,cAAc,2BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;oCACnD,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;4CAAsB,WAAU;sDAC/B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,SAAS,YAAY,CAAC,MAAM,iBAC3B,8OAAC;gEACC,KAAK,SAAS,YAAY,CAAC,MAAM;gEACjC,KAAK,SAAS,YAAY,CAAC,IAAI;gEAC/B,WAAU;;;;;qFAGZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAGxB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA+B,SAAS,YAAY,CAAC,IAAI;;;;;;kFACvE,8OAAC;wEAAE,WAAU;kFAAyB,SAAS,YAAY,CAAC,KAAK;;;;;;kFACjE,8OAAC;wEAAE,WAAU;kFAA8B,SAAS,QAAQ;;;;;;kFAC5D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;oFAAK;oFAAW,WAAW,SAAS,YAAY;;;;;;;4EAChD,SAAS,cAAc,kBACtB,8OAAC;;oFAAK;oFAAY,WAAW,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;kEAK5D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,SAAS,MAAM,GAAG;0EAC1H,SAAS,MAAM;;;;;;0EAElB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;4EAA8B;4EAAE,SAAS,MAAM,CAAC,MAAM;;;;;;;kFACnE,8OAAC;wEAAK,WAAW,CAAC,sEAAsE,EAAE,qBAAqB,SAAS,MAAM,CAAC,MAAM,GAAG;kFACrI,SAAS,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;2CAjCvB,SAAS,EAAE;;;;;;;;;;;4BA4C1B,cAAc,2BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;gDAAoB,WAAW,CAAC,sBAAsB,EAAE,OAAO,QAAQ,GAAG,+BAA+B,8BAA8B;;kEACtI,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAW,CAAC,sDAAsD,EAAE,OAAO,QAAQ,GAAG,gBAAgB,eAAe;;4EACvH,OAAO,IAAI,KAAK,6BAAe,8OAAC,8MAAA,CAAA,WAAQ;gFAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;;;;;;4EACpH,OAAO,IAAI,KAAK,4BAAc,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;;;;;;4EACjH,OAAO,IAAI,KAAK,yBAAW,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;;;;;;4EAC3G,OAAO,IAAI,KAAK,wBAAU,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;;;;;;;;;;;;kFAE9G,8OAAC;;0FACC,8OAAC;gFAAG,WAAW,CAAC,cAAc,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;0FAAG,OAAO,KAAK;;;;;;0FACpG,8OAAC;gFAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;0FAAG,OAAO,WAAW;;;;;;;;;;;;;;;;;;4DAGtG,CAAC,OAAO,QAAQ,kBACf,8OAAC;gEAAK,WAAU;0EAAoG;;;;;;;;;;;;kEAKxH,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;kFAAE;;;;;;kFACnF,8OAAC;wEAAK,WAAW,CAAC,cAAc,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;kFACpF,OAAO,IAAI,KAAK,UAAU,GAAG,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE;;;;;;;;;;;;0EAG9E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;kFAAE;;;;;;kFACnF,8OAAC;wEAAK,WAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,kBAAkB,iBAAiB;kFAAG,OAAO,WAAW;;;;;;;;;;;;;;;;;;;+CA7BlG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCrC", "debugId": null}}]}