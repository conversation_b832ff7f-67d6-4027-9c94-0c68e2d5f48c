'use client';

import { useState } from 'react';
import { 
  Search,
  Filter,
  Heart,
  MapPin,
  Bed,
  Bath,
  Square,
  DollarSign,
  TrendingUp,
  Eye,
  Share2,
  Star,
  Building2,
  Calendar,
  Users,
  Coins,
  Grid3X3,
  List,
  SlidersHorizontal
} from 'lucide-react';

interface Property {
  id: string;
  title: string;
  address: string;
  price: number;
  type: 'apartment' | 'house' | 'condo' | 'townhouse';
  bedrooms: number;
  bathrooms: number;
  sqft: number;
  yearBuilt: number;
  description: string;
  features: string[];
  images: string[];
  agent: {
    name: string;
    phone: string;
    email: string;
  };
  listingDate: string;
  status: 'available' | 'pending' | 'sold';
  pricePerSqft: number;
  monthlyRent?: number;
  roi?: number;
  neighborhood: string;
  walkScore?: number;
  isFavorited: boolean;
  viewCount: number;
}

export default function MarketplacePanel() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'apartment' | 'house' | 'condo' | 'townhouse'>('all');
  const [priceRange, setPriceRange] = useState<'all' | '0-500k' | '500k-1m' | '1m+'>('all');
  const [sortBy, setSortBy] = useState<'price-low' | 'price-high' | 'newest' | 'popular'>('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  // Mock properties data
  const [properties, setProperties] = useState<Property[]>([
    {
      id: '1',
      title: 'Modern Downtown Loft',
      address: '456 Broadway, Downtown, NY 10013',
      price: 850000,
      type: 'apartment',
      bedrooms: 2,
      bathrooms: 2,
      sqft: 1400,
      yearBuilt: 2018,
      description: 'Stunning modern loft with floor-to-ceiling windows and city views.',
      features: ['Hardwood Floors', 'Stainless Steel Appliances', 'In-Unit Laundry', 'Balcony'],
      images: ['/api/placeholder/400/300'],
      agent: {
        name: 'Sarah Johnson',
        phone: '(*************',
        email: '<EMAIL>'
      },
      listingDate: '2024-01-15T10:00:00Z',
      status: 'available',
      pricePerSqft: 607,
      monthlyRent: 4200,
      roi: 5.9,
      neighborhood: 'SoHo',
      walkScore: 95,
      isFavorited: false,
      viewCount: 234
    },
    {
      id: '2',
      title: 'Charming Brooklyn Townhouse',
      address: '789 Park Slope Ave, Brooklyn, NY 11215',
      price: 1200000,
      type: 'townhouse',
      bedrooms: 4,
      bathrooms: 3,
      sqft: 2200,
      yearBuilt: 1920,
      description: 'Historic townhouse with original details and modern updates.',
      features: ['Original Hardwood', 'Renovated Kitchen', 'Private Garden', 'Fireplace'],
      images: ['/api/placeholder/400/300'],
      agent: {
        name: 'Mike Chen',
        phone: '(*************',
        email: '<EMAIL>'
      },
      listingDate: '2024-01-20T14:30:00Z',
      status: 'available',
      pricePerSqft: 545,
      monthlyRent: 5800,
      roi: 5.8,
      neighborhood: 'Park Slope',
      walkScore: 88,
      isFavorited: true,
      viewCount: 156
    },
    {
      id: '3',
      title: 'Luxury Upper East Side Condo',
      address: '321 E 72nd St, Upper East Side, NY 10021',
      price: 2500000,
      type: 'condo',
      bedrooms: 3,
      bathrooms: 2,
      sqft: 1800,
      yearBuilt: 2015,
      description: 'Elegant condo with Central Park views and premium amenities.',
      features: ['Central Park Views', 'Doorman', 'Gym', 'Roof Deck'],
      images: ['/api/placeholder/400/300'],
      agent: {
        name: 'Emily Rodriguez',
        phone: '(*************',
        email: '<EMAIL>'
      },
      listingDate: '2024-01-18T09:15:00Z',
      status: 'pending',
      pricePerSqft: 1389,
      monthlyRent: 8500,
      roi: 4.1,
      neighborhood: 'Upper East Side',
      walkScore: 92,
      isFavorited: false,
      viewCount: 89
    },
    {
      id: '4',
      title: 'Cozy Queens Family Home',
      address: '654 Maple St, Astoria, NY 11106',
      price: 650000,
      type: 'house',
      bedrooms: 3,
      bathrooms: 2,
      sqft: 1600,
      yearBuilt: 1950,
      description: 'Perfect family home with yard and garage in quiet neighborhood.',
      features: ['Private Yard', 'Garage', 'Updated Kitchen', 'Basement'],
      images: ['/api/placeholder/400/300'],
      agent: {
        name: 'David Kim',
        phone: '(*************',
        email: '<EMAIL>'
      },
      listingDate: '2024-01-22T16:45:00Z',
      status: 'available',
      pricePerSqft: 406,
      monthlyRent: 3200,
      roi: 5.9,
      neighborhood: 'Astoria',
      walkScore: 78,
      isFavorited: true,
      viewCount: 67
    }
  ]);

  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.neighborhood.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || property.type === filterType;
    const matchesPrice = priceRange === 'all' || 
                        (priceRange === '0-500k' && property.price <= 500000) ||
                        (priceRange === '500k-1m' && property.price > 500000 && property.price <= 1000000) ||
                        (priceRange === '1m+' && property.price > 1000000);
    
    return matchesSearch && matchesType && matchesPrice;
  });

  // Sort properties
  const sortedProperties = [...filteredProperties].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'newest':
        return new Date(b.listingDate).getTime() - new Date(a.listingDate).getTime();
      case 'popular':
        return b.viewCount - a.viewCount;
      default:
        return 0;
    }
  });

  const toggleFavorite = (propertyId: string) => {
    setProperties(prev =>
      prev.map(property =>
        property.id === propertyId
          ? { ...property, isFavorited: !property.isFavorited }
          : property
      )
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'sold':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (price: number) => {
    if (price >= 1000000) {
      return `$${(price / 1000000).toFixed(1)}M`;
    }
    return `$${(price / 1000).toFixed(0)}K`;
  };

  const totalListings = properties.length;
  const availableListings = properties.filter(p => p.status === 'available').length;
  const averagePrice = properties.reduce((sum, p) => sum + p.price, 0) / properties.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Property Marketplace</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <SlidersHorizontal className="w-5 h-5" />
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <Grid3X3 className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Listings</p>
              <p className="text-2xl font-bold text-gray-900">{totalListings}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Available</p>
              <p className="text-2xl font-bold text-gray-900">{availableListings}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg. Price</p>
              <p className="text-2xl font-bold text-gray-900">{formatPrice(averagePrice)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by location, neighborhood..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="popular">Most Popular</option>
            </select>
          </div>

          {showFilters && (
            <div className="flex flex-wrap gap-4 pt-4 border-t border-gray-200">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="apartment">Apartment</option>
                <option value="house">House</option>
                <option value="condo">Condo</option>
                <option value="townhouse">Townhouse</option>
              </select>

              <select
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Prices</option>
                <option value="0-500k">Under $500K</option>
                <option value="500k-1m">$500K - $1M</option>
                <option value="1m+">Over $1M</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Properties Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedProperties.map((property) => (
            <div key={property.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <img
                  src={property.images[0]}
                  alt={property.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 left-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(property.status)}`}>
                    {property.status}
                  </span>
                </div>
                <div className="absolute top-4 right-4 flex space-x-2">
                  <button
                    onClick={() => toggleFavorite(property.id)}
                    className={`p-2 rounded-full transition-colors ${
                      property.isFavorited 
                        ? 'bg-red-100 text-red-600' 
                        : 'bg-white bg-opacity-90 text-gray-600 hover:bg-opacity-100'
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${property.isFavorited ? 'fill-current' : ''}`} />
                  </button>
                  <button className="bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all">
                    <Share2 className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{property.title}</h3>
                    <p className="text-sm text-gray-600 flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      {property.neighborhood}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-gray-900">{formatPrice(property.price)}</p>
                    <p className="text-sm text-gray-600">${property.pricePerSqft}/sqft</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                  <span className="flex items-center">
                    <Bed className="w-4 h-4 mr-1" />
                    {property.bedrooms} bed
                  </span>
                  <span className="flex items-center">
                    <Bath className="w-4 h-4 mr-1" />
                    {property.bathrooms} bath
                  </span>
                  <span className="flex items-center">
                    <Square className="w-4 h-4 mr-1" />
                    {property.sqft.toLocaleString()} sqft
                  </span>
                </div>

                {property.roi && (
                  <div className="bg-green-50 rounded-lg p-3 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">Investment ROI</span>
                      <span className="text-sm font-bold text-green-600">{property.roi}%</span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      Est. monthly rent: ${property.monthlyRent?.toLocaleString()}
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <Eye className="w-4 h-4" />
                    <span>{property.viewCount} views</span>
                  </div>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    View Details
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="space-y-4 p-6">
            {sortedProperties.map((property) => (
              <div key={property.id} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow">
                <img
                  src={property.images[0]}
                  alt={property.title}
                  className="w-24 h-24 object-cover rounded-lg flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{property.title}</h3>
                      <p className="text-sm text-gray-600 flex items-center mt-1">
                        <MapPin className="w-4 h-4 mr-1" />
                        {property.address}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mt-2">
                        <span>{property.bedrooms} bed</span>
                        <span>{property.bathrooms} bath</span>
                        <span>{property.sqft.toLocaleString()} sqft</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xl font-bold text-gray-900">{formatPrice(property.price)}</p>
                      <p className="text-sm text-gray-600">${property.pricePerSqft}/sqft</p>
                      {property.roi && (
                        <p className="text-sm text-green-600 font-medium mt-1">{property.roi}% ROI</p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <button
                    onClick={() => toggleFavorite(property.id)}
                    className={`p-2 rounded-full transition-colors ${
                      property.isFavorited 
                        ? 'bg-red-100 text-red-600' 
                        : 'text-gray-400 hover:text-red-600 hover:bg-red-50'
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${property.isFavorited ? 'fill-current' : ''}`} />
                  </button>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {sortedProperties.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
          <p className="text-gray-600">
            Try adjusting your search criteria or filters to find more properties.
          </p>
        </div>
      )}
    </div>
  );
}
