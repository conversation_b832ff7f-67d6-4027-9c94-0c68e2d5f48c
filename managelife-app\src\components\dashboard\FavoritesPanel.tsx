'use client';

import { useState } from 'react';
import { 
  Heart,
  MapPin,
  Bed,
  Bath,
  Square,
  DollarSign,
  TrendingUp,
  Eye,
  Share2,
  X,
  Calendar,
  Filter,
  Search,
  Grid3X3,
  List,
  AlertCircle,
  ExternalLink
} from 'lucide-react';

interface FavoriteProperty {
  id: string;
  title: string;
  address: string;
  price: number;
  type: 'apartment' | 'house' | 'condo' | 'townhouse';
  bedrooms: number;
  bathrooms: number;
  sqft: number;
  pricePerSqft: number;
  monthlyRent?: number;
  roi?: number;
  neighborhood: string;
  images: string[];
  addedDate: string;
  lastViewed: string;
  priceChange?: {
    amount: number;
    direction: 'up' | 'down';
    date: string;
  };
  status: 'available' | 'pending' | 'sold' | 'off-market';
  notes?: string;
}

export default function FavoritesPanel() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'apartment' | 'house' | 'condo' | 'townhouse'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'available' | 'pending' | 'sold' | 'off-market'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'price-low' | 'price-high' | 'recently-viewed'>('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock favorites data
  const [favorites, setFavorites] = useState<FavoriteProperty[]>([
    {
      id: '1',
      title: 'Modern Downtown Loft',
      address: '456 Broadway, Downtown, NY 10013',
      price: 850000,
      type: 'apartment',
      bedrooms: 2,
      bathrooms: 2,
      sqft: 1400,
      pricePerSqft: 607,
      monthlyRent: 4200,
      roi: 5.9,
      neighborhood: 'SoHo',
      images: ['/api/placeholder/400/300'],
      addedDate: '2024-01-15T10:00:00Z',
      lastViewed: '2024-01-23T14:30:00Z',
      priceChange: {
        amount: 25000,
        direction: 'down',
        date: '2024-01-20T00:00:00Z'
      },
      status: 'available',
      notes: 'Great investment opportunity in prime location'
    },
    {
      id: '2',
      title: 'Charming Brooklyn Townhouse',
      address: '789 Park Slope Ave, Brooklyn, NY 11215',
      price: 1200000,
      type: 'townhouse',
      bedrooms: 4,
      bathrooms: 3,
      sqft: 2200,
      pricePerSqft: 545,
      monthlyRent: 5800,
      roi: 5.8,
      neighborhood: 'Park Slope',
      images: ['/api/placeholder/400/300'],
      addedDate: '2024-01-10T16:45:00Z',
      lastViewed: '2024-01-22T09:15:00Z',
      status: 'available'
    },
    {
      id: '3',
      title: 'Cozy Queens Family Home',
      address: '654 Maple St, Astoria, NY 11106',
      price: 650000,
      type: 'house',
      bedrooms: 3,
      bathrooms: 2,
      sqft: 1600,
      pricePerSqft: 406,
      monthlyRent: 3200,
      roi: 5.9,
      neighborhood: 'Astoria',
      images: ['/api/placeholder/400/300'],
      addedDate: '2024-01-08T12:20:00Z',
      lastViewed: '2024-01-21T18:45:00Z',
      priceChange: {
        amount: 15000,
        direction: 'up',
        date: '2024-01-18T00:00:00Z'
      },
      status: 'pending',
      notes: 'Owner motivated to sell quickly'
    },
    {
      id: '4',
      title: 'Luxury Manhattan Penthouse',
      address: '123 5th Ave, Midtown, NY 10016',
      price: 3500000,
      type: 'condo',
      bedrooms: 4,
      bathrooms: 3,
      sqft: 2800,
      pricePerSqft: 1250,
      neighborhood: 'Midtown',
      images: ['/api/placeholder/400/300'],
      addedDate: '2024-01-05T08:30:00Z',
      lastViewed: '2024-01-19T11:20:00Z',
      status: 'sold'
    }
  ]);

  const filteredFavorites = favorites.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.neighborhood.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || property.type === filterType;
    const matchesStatus = filterStatus === 'all' || property.status === filterStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // Sort favorites
  const sortedFavorites = [...filteredFavorites].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'newest':
        return new Date(b.addedDate).getTime() - new Date(a.addedDate).getTime();
      case 'recently-viewed':
        return new Date(b.lastViewed).getTime() - new Date(a.lastViewed).getTime();
      default:
        return 0;
    }
  });

  const removeFavorite = (propertyId: string) => {
    setFavorites(prev => prev.filter(property => property.id !== propertyId));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'sold':
        return 'bg-gray-100 text-gray-800';
      case 'off-market':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (price: number) => {
    if (price >= 1000000) {
      return `$${(price / 1000000).toFixed(1)}M`;
    }
    return `$${(price / 1000).toFixed(0)}K`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const totalFavorites = favorites.length;
  const availableFavorites = favorites.filter(p => p.status === 'available').length;
  const recentPriceChanges = favorites.filter(p => p.priceChange).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Favorite Properties</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <Grid3X3 className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Heart className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Favorites</p>
              <p className="text-2xl font-bold text-gray-900">{totalFavorites}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Available</p>
              <p className="text-2xl font-bold text-gray-900">{availableFavorites}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <AlertCircle className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Price Changes</p>
              <p className="text-2xl font-bold text-gray-900">{recentPriceChanges}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search favorites..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Types</option>
            <option value="apartment">Apartment</option>
            <option value="house">House</option>
            <option value="condo">Condo</option>
            <option value="townhouse">Townhouse</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="available">Available</option>
            <option value="pending">Pending</option>
            <option value="sold">Sold</option>
            <option value="off-market">Off Market</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="newest">Recently Added</option>
            <option value="recently-viewed">Recently Viewed</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
          </select>
        </div>
      </div>

      {/* Favorites Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedFavorites.map((property) => (
            <div key={property.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <img
                  src={property.images[0]}
                  alt={property.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 left-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(property.status)}`}>
                    {property.status}
                  </span>
                </div>
                <div className="absolute top-4 right-4 flex space-x-2">
                  <button
                    onClick={() => removeFavorite(property.id)}
                    className="bg-white bg-opacity-90 p-2 rounded-full hover:bg-red-50 hover:text-red-600 transition-all"
                  >
                    <X className="w-4 h-4" />
                  </button>
                  <button className="bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all">
                    <Share2 className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
                {property.priceChange && (
                  <div className="absolute bottom-4 left-4">
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      property.priceChange.direction === 'down' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      <TrendingUp className={`w-3 h-3 mr-1 ${
                        property.priceChange.direction === 'down' ? 'rotate-180' : ''
                      }`} />
                      {property.priceChange.direction === 'down' ? '-' : '+'}${property.priceChange.amount.toLocaleString()}
                    </div>
                  </div>
                )}
              </div>

              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{property.title}</h3>
                    <p className="text-sm text-gray-600 flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      {property.neighborhood}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-gray-900">{formatPrice(property.price)}</p>
                    <p className="text-sm text-gray-600">${property.pricePerSqft}/sqft</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                  <span className="flex items-center">
                    <Bed className="w-4 h-4 mr-1" />
                    {property.bedrooms} bed
                  </span>
                  <span className="flex items-center">
                    <Bath className="w-4 h-4 mr-1" />
                    {property.bathrooms} bath
                  </span>
                  <span className="flex items-center">
                    <Square className="w-4 h-4 mr-1" />
                    {property.sqft.toLocaleString()} sqft
                  </span>
                </div>

                {property.notes && (
                  <div className="bg-blue-50 rounded-lg p-3 mb-4">
                    <p className="text-sm text-blue-800">{property.notes}</p>
                  </div>
                )}

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="text-xs text-gray-500">
                    <p>Added {formatDate(property.addedDate)}</p>
                    <p>Viewed {formatDate(property.lastViewed)}</p>
                  </div>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center">
                    <ExternalLink className="w-4 h-4 mr-1" />
                    View
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="space-y-4 p-6">
            {sortedFavorites.map((property) => (
              <div key={property.id} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow">
                <img
                  src={property.images[0]}
                  alt={property.title}
                  className="w-24 h-24 object-cover rounded-lg flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{property.title}</h3>
                      <p className="text-sm text-gray-600 flex items-center mt-1">
                        <MapPin className="w-4 h-4 mr-1" />
                        {property.address}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mt-2">
                        <span>{property.bedrooms} bed</span>
                        <span>{property.bathrooms} bath</span>
                        <span>{property.sqft.toLocaleString()} sqft</span>
                      </div>
                      {property.priceChange && (
                        <div className="mt-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            property.priceChange.direction === 'down' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            Price {property.priceChange.direction === 'down' ? 'decreased' : 'increased'} by ${property.priceChange.amount.toLocaleString()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-xl font-bold text-gray-900">{formatPrice(property.price)}</p>
                      <p className="text-sm text-gray-600">${property.pricePerSqft}/sqft</p>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${getStatusColor(property.status)}`}>
                        {property.status}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <button
                    onClick={() => removeFavorite(property.id)}
                    className="text-gray-400 hover:text-red-600 p-1"
                  >
                    <X className="w-4 h-4" />
                  </button>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {sortedFavorites.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Heart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No favorite properties found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterType !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'Start browsing properties and add them to your favorites to see them here.'
            }
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Browse Properties
          </button>
        </div>
      )}
    </div>
  );
}
