'use client';

import { useState } from 'react';
import { 
  Users,
  MessageSquare,
  Heart,
  Share2,
  Plus,
  Search,
  Filter,
  TrendingUp,
  Clock,
  MapPin,
  User,
  ThumbsUp,
  MessageCircle,
  Eye,
  Pin,
  Flag,
  MoreHorizontal,
  Image,
  Video,
  FileText,
  Calendar,
  Star,
  Award
} from 'lucide-react';

interface CommunityPost {
  id: string;
  author: {
    name: string;
    avatar?: string;
    role: 'homeowner' | 'renter' | 'buyer' | 'manager' | 'member';
    verified: boolean;
  };
  content: string;
  images?: string[];
  type: 'discussion' | 'question' | 'announcement' | 'event' | 'tip';
  category: 'general' | 'maintenance' | 'neighborhood' | 'events' | 'marketplace';
  timestamp: string;
  likes: number;
  comments: number;
  views: number;
  isLiked: boolean;
  isPinned: boolean;
  tags: string[];
}

interface CommunityMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'homeowner' | 'renter' | 'buyer' | 'manager' | 'member';
  joinDate: string;
  posts: number;
  reputation: number;
  isOnline: boolean;
  verified: boolean;
}

export default function CommunityPanel() {
  const [activeFilter, setActiveFilter] = useState<'all' | 'discussion' | 'question' | 'announcement' | 'event' | 'tip'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'popular' | 'trending'>('newest');

  // Mock posts data
  const [posts] = useState<CommunityPost[]>([
    {
      id: '1',
      author: {
        name: 'Sarah Johnson',
        avatar: '/api/placeholder/150/150',
        role: 'homeowner',
        verified: true
      },
      content: 'Just wanted to share some tips for new homeowners about winter maintenance. Make sure to check your heating system before the cold season hits! Also, don\'t forget to winterize your outdoor faucets.',
      type: 'tip',
      category: 'maintenance',
      timestamp: '2024-01-23T14:30:00Z',
      likes: 24,
      comments: 8,
      views: 156,
      isLiked: false,
      isPinned: true,
      tags: ['maintenance', 'winter', 'tips']
    },
    {
      id: '2',
      author: {
        name: 'Mike Chen',
        role: 'renter',
        verified: false
      },
      content: 'Has anyone else noticed the construction noise on Elm Street? It starts really early in the morning. Does anyone know what they\'re building?',
      type: 'question',
      category: 'neighborhood',
      timestamp: '2024-01-23T10:15:00Z',
      likes: 12,
      comments: 15,
      views: 89,
      isLiked: true,
      isPinned: false,
      tags: ['neighborhood', 'construction']
    },
    {
      id: '3',
      author: {
        name: 'Community Manager',
        avatar: '/api/placeholder/150/150',
        role: 'manager',
        verified: true
      },
      content: 'Reminder: Our monthly community meeting is this Saturday at 2 PM in the community center. We\'ll be discussing the new playground project and upcoming events. Light refreshments will be provided!',
      type: 'announcement',
      category: 'events',
      timestamp: '2024-01-22T16:45:00Z',
      likes: 45,
      comments: 12,
      views: 234,
      isLiked: false,
      isPinned: true,
      tags: ['meeting', 'community', 'announcement']
    },
    {
      id: '4',
      author: {
        name: 'Emily Rodriguez',
        role: 'buyer',
        verified: false
      },
      content: 'Looking for recommendations for a good local plumber. Had a small leak in my bathroom and want to get it fixed properly. Any suggestions?',
      type: 'question',
      category: 'maintenance',
      timestamp: '2024-01-22T09:20:00Z',
      likes: 8,
      comments: 6,
      views: 67,
      isLiked: false,
      isPinned: false,
      tags: ['plumber', 'recommendations', 'maintenance']
    },
    {
      id: '5',
      author: {
        name: 'David Kim',
        role: 'homeowner',
        verified: true
      },
      content: 'Great turnout at yesterday\'s block party! Thanks to everyone who came and brought food. The kids had a blast with the bounce house. Looking forward to the next one!',
      images: ['/api/placeholder/400/300', '/api/placeholder/400/300'],
      type: 'discussion',
      category: 'events',
      timestamp: '2024-01-21T20:30:00Z',
      likes: 67,
      comments: 23,
      views: 189,
      isLiked: true,
      isPinned: false,
      tags: ['block-party', 'community', 'fun']
    }
  ]);

  // Mock members data
  const [topMembers] = useState<CommunityMember[]>([
    {
      id: '1',
      name: 'Sarah Johnson',
      avatar: '/api/placeholder/150/150',
      role: 'homeowner',
      joinDate: '2023-03-15T00:00:00Z',
      posts: 45,
      reputation: 892,
      isOnline: true,
      verified: true
    },
    {
      id: '2',
      name: 'Community Manager',
      avatar: '/api/placeholder/150/150',
      role: 'manager',
      joinDate: '2022-01-01T00:00:00Z',
      posts: 156,
      reputation: 2340,
      isOnline: true,
      verified: true
    },
    {
      id: '3',
      name: 'Mike Chen',
      role: 'renter',
      joinDate: '2023-08-20T00:00:00Z',
      posts: 23,
      reputation: 456,
      isOnline: false,
      verified: false
    },
    {
      id: '4',
      name: 'David Kim',
      role: 'homeowner',
      joinDate: '2023-05-10T00:00:00Z',
      posts: 34,
      reputation: 678,
      isOnline: true,
      verified: true
    }
  ]);

  const filteredPosts = posts.filter(post => {
    const matchesFilter = activeFilter === 'all' || post.type === activeFilter;
    const matchesSearch = post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesFilter && matchesSearch;
  });

  // Sort posts
  const sortedPosts = [...filteredPosts].sort((a, b) => {
    // Pinned posts always come first
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    
    switch (sortBy) {
      case 'newest':
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      case 'popular':
        return b.likes - a.likes;
      case 'trending':
        return (b.likes + b.comments) - (a.likes + a.comments);
      default:
        return 0;
    }
  });

  const getPostTypeColor = (type: string) => {
    switch (type) {
      case 'discussion':
        return 'bg-blue-100 text-blue-800';
      case 'question':
        return 'bg-purple-100 text-purple-800';
      case 'announcement':
        return 'bg-red-100 text-red-800';
      case 'event':
        return 'bg-green-100 text-green-800';
      case 'tip':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'homeowner':
        return 'text-blue-600';
      case 'renter':
        return 'text-green-600';
      case 'buyer':
        return 'text-purple-600';
      case 'manager':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return time.toLocaleDateString();
  };

  const totalPosts = posts.length;
  const totalMembers = 156;
  const activeMembers = topMembers.filter(m => m.isOnline).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Community</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          New Post
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Posts</p>
              <p className="text-2xl font-bold text-gray-900">{totalPosts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Members</p>
              <p className="text-2xl font-bold text-gray-900">{totalMembers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Now</p>
              <p className="text-2xl font-bold text-gray-900">{activeMembers}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Filters and Search */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search posts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="newest">Newest</option>
                <option value="popular">Most Popular</option>
                <option value="trending">Trending</option>
              </select>
            </div>

            <div className="flex flex-wrap gap-2">
              {['all', 'discussion', 'question', 'announcement', 'event', 'tip'].map((filter) => (
                <button
                  key={filter}
                  onClick={() => setActiveFilter(filter as any)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    activeFilter === filter
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {filter.charAt(0).toUpperCase() + filter.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Posts */}
          <div className="space-y-4">
            {sortedPosts.map((post) => (
              <div key={post.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {post.author.avatar ? (
                      <img
                        src={post.author.avatar}
                        alt={post.author.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <h3 className={`font-semibold ${getRoleColor(post.author.role)}`}>
                          {post.author.name}
                        </h3>
                        {post.author.verified && (
                          <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPostTypeColor(post.type)}`}>
                          {post.type}
                        </span>
                        {post.isPinned && (
                          <Pin className="w-4 h-4 text-orange-500" />
                        )}
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        <span>{formatTimeAgo(post.timestamp)}</span>
                        <button className="text-gray-400 hover:text-gray-600">
                          <MoreHorizontal className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <p className="text-gray-900 mb-3">{post.content}</p>

                    {post.images && post.images.length > 0 && (
                      <div className="grid grid-cols-2 gap-2 mb-3">
                        {post.images.map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`Post image ${index + 1}`}
                            className="rounded-lg object-cover h-32 w-full"
                          />
                        ))}
                      </div>
                    )}

                    <div className="flex flex-wrap gap-2 mb-3">
                      {post.tags.map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                      <div className="flex items-center space-x-6">
                        <button className={`flex items-center space-x-1 text-sm transition-colors ${
                          post.isLiked ? 'text-red-600' : 'text-gray-500 hover:text-red-600'
                        }`}>
                          <Heart className={`w-4 h-4 ${post.isLiked ? 'fill-current' : ''}`} />
                          <span>{post.likes}</span>
                        </button>
                        <button className="flex items-center space-x-1 text-sm text-gray-500 hover:text-blue-600 transition-colors">
                          <MessageCircle className="w-4 h-4" />
                          <span>{post.comments}</span>
                        </button>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Eye className="w-4 h-4" />
                          <span>{post.views}</span>
                        </div>
                      </div>
                      <button className="text-gray-500 hover:text-gray-700 transition-colors">
                        <Share2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {sortedPosts.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No posts found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || activeFilter !== 'all'
                  ? 'Try adjusting your search or filters.'
                  : 'Be the first to start a conversation in the community!'
                }
              </p>
              <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Create First Post
              </button>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Top Members */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Members</h3>
            <div className="space-y-4">
              {topMembers.map((member) => (
                <div key={member.id} className="flex items-center space-x-3">
                  <div className="relative">
                    {member.avatar ? (
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-gray-400" />
                      </div>
                    )}
                    {member.isOnline && (
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-1">
                      <p className={`text-sm font-medium ${getRoleColor(member.role)} truncate`}>
                        {member.name}
                      </p>
                      {member.verified && (
                        <div className="w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center">
                          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{member.posts} posts</span>
                      <span>•</span>
                      <div className="flex items-center">
                        <Star className="w-3 h-3 text-yellow-400 mr-1" />
                        <span>{member.reputation}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Community Guidelines */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Community Guidelines</h3>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p>Be respectful and kind to all community members</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p>Keep discussions relevant to the community</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p>No spam, self-promotion, or commercial posts</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <p>Report inappropriate content to moderators</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
