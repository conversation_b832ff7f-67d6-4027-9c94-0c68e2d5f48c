'use client';

import { useState } from 'react';
import { 
  HandCoins,
  DollarSign,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Edit,
  MessageSquare,
  TrendingUp,
  TrendingDown,
  MapPin,
  Bed,
  Bath,
  Square,
  Filter,
  Search,
  Plus,
  FileText,
  Phone,
  Mail
} from 'lucide-react';

interface Offer {
  id: string;
  propertyId: string;
  propertyTitle: string;
  propertyAddress: string;
  propertyImage: string;
  propertyDetails: {
    bedrooms: number;
    bathrooms: number;
    sqft: number;
    listingPrice: number;
  };
  offerAmount: number;
  offerDate: string;
  expirationDate: string;
  status: 'pending' | 'accepted' | 'rejected' | 'countered' | 'withdrawn' | 'expired';
  counterOffer?: {
    amount: number;
    date: string;
    message?: string;
  };
  contingencies: string[];
  financing: {
    type: 'cash' | 'conventional' | 'fha' | 'va';
    downPayment: number;
    preApproved: boolean;
  };
  closingDate: string;
  agent: {
    name: string;
    phone: string;
    email: string;
  };
  notes?: string;
  lastActivity: string;
}

export default function OffersPanel() {
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'accepted' | 'rejected' | 'countered' | 'withdrawn' | 'expired'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'amount-high' | 'amount-low' | 'expiring-soon'>('newest');

  // Mock offers data
  const [offers] = useState<Offer[]>([
    {
      id: '1',
      propertyId: 'prop-1',
      propertyTitle: 'Modern Downtown Loft',
      propertyAddress: '456 Broadway, Downtown, NY 10013',
      propertyImage: '/api/placeholder/300/200',
      propertyDetails: {
        bedrooms: 2,
        bathrooms: 2,
        sqft: 1400,
        listingPrice: 850000
      },
      offerAmount: 825000,
      offerDate: '2024-01-20T10:00:00Z',
      expirationDate: '2024-01-27T23:59:59Z',
      status: 'countered',
      counterOffer: {
        amount: 840000,
        date: '2024-01-22T14:30:00Z',
        message: 'We can meet in the middle at $840K. Property is in excellent condition.'
      },
      contingencies: ['Inspection', 'Financing', 'Appraisal'],
      financing: {
        type: 'conventional',
        downPayment: 170000,
        preApproved: true
      },
      closingDate: '2024-03-15T00:00:00Z',
      agent: {
        name: 'Sarah Johnson',
        phone: '(*************',
        email: '<EMAIL>'
      },
      notes: 'Seller is motivated. Property has been on market for 45 days.',
      lastActivity: '2024-01-22T14:30:00Z'
    },
    {
      id: '2',
      propertyId: 'prop-2',
      propertyTitle: 'Charming Brooklyn Townhouse',
      propertyAddress: '789 Park Slope Ave, Brooklyn, NY 11215',
      propertyImage: '/api/placeholder/300/200',
      propertyDetails: {
        bedrooms: 4,
        bathrooms: 3,
        sqft: 2200,
        listingPrice: 1200000
      },
      offerAmount: 1150000,
      offerDate: '2024-01-18T16:45:00Z',
      expirationDate: '2024-01-25T23:59:59Z',
      status: 'pending',
      contingencies: ['Inspection', 'Financing'],
      financing: {
        type: 'conventional',
        downPayment: 230000,
        preApproved: true
      },
      closingDate: '2024-03-01T00:00:00Z',
      agent: {
        name: 'Mike Chen',
        phone: '(*************',
        email: '<EMAIL>'
      },
      lastActivity: '2024-01-18T16:45:00Z'
    },
    {
      id: '3',
      propertyId: 'prop-3',
      propertyTitle: 'Cozy Queens Family Home',
      propertyAddress: '654 Maple St, Astoria, NY 11106',
      propertyImage: '/api/placeholder/300/200',
      propertyDetails: {
        bedrooms: 3,
        bathrooms: 2,
        sqft: 1600,
        listingPrice: 650000
      },
      offerAmount: 635000,
      offerDate: '2024-01-15T12:20:00Z',
      expirationDate: '2024-01-22T23:59:59Z',
      status: 'accepted',
      contingencies: ['Inspection'],
      financing: {
        type: 'fha',
        downPayment: 22225,
        preApproved: true
      },
      closingDate: '2024-02-28T00:00:00Z',
      agent: {
        name: 'David Kim',
        phone: '(*************',
        email: '<EMAIL>'
      },
      notes: 'Great deal! Under asking price.',
      lastActivity: '2024-01-16T09:30:00Z'
    },
    {
      id: '4',
      propertyId: 'prop-4',
      propertyTitle: 'Manhattan Studio Apartment',
      propertyAddress: '123 E 42nd St, Midtown, NY 10017',
      propertyImage: '/api/placeholder/300/200',
      propertyDetails: {
        bedrooms: 0,
        bathrooms: 1,
        sqft: 500,
        listingPrice: 450000
      },
      offerAmount: 420000,
      offerDate: '2024-01-10T14:15:00Z',
      expirationDate: '2024-01-17T23:59:59Z',
      status: 'rejected',
      contingencies: ['Financing'],
      financing: {
        type: 'cash',
        downPayment: 420000,
        preApproved: true
      },
      closingDate: '2024-02-15T00:00:00Z',
      agent: {
        name: 'Lisa Wang',
        phone: '(*************',
        email: '<EMAIL>'
      },
      notes: 'Offer was too low. Seller not negotiating.',
      lastActivity: '2024-01-12T11:00:00Z'
    }
  ]);

  const filteredOffers = offers.filter(offer => {
    const matchesSearch = offer.propertyTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         offer.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || offer.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  // Sort offers
  const sortedOffers = [...filteredOffers].sort((a, b) => {
    switch (sortBy) {
      case 'amount-high':
        return b.offerAmount - a.offerAmount;
      case 'amount-low':
        return a.offerAmount - b.offerAmount;
      case 'newest':
        return new Date(b.offerDate).getTime() - new Date(a.offerDate).getTime();
      case 'expiring-soon':
        return new Date(a.expirationDate).getTime() - new Date(b.expirationDate).getTime();
      default:
        return 0;
    }
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'countered':
        return 'bg-blue-100 text-blue-800';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800';
      case 'expired':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'accepted':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
        return <XCircle className="w-4 h-4" />;
      case 'countered':
        return <MessageSquare className="w-4 h-4" />;
      case 'withdrawn':
        return <XCircle className="w-4 h-4" />;
      case 'expired':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const formatPrice = (price: number) => {
    return `$${price.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDaysUntilExpiration = (expirationDate: string) => {
    const now = new Date();
    const expiry = new Date(expirationDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const totalOffers = offers.length;
  const pendingOffers = offers.filter(o => o.status === 'pending').length;
  const acceptedOffers = offers.filter(o => o.status === 'accepted').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">My Offers</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Make Offer
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <HandCoins className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Offers</p>
              <p className="text-2xl font-bold text-gray-900">{totalOffers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{pendingOffers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Accepted</p>
              <p className="text-2xl font-bold text-gray-900">{acceptedOffers}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search offers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="accepted">Accepted</option>
            <option value="rejected">Rejected</option>
            <option value="countered">Countered</option>
            <option value="withdrawn">Withdrawn</option>
            <option value="expired">Expired</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="newest">Newest First</option>
            <option value="amount-high">Highest Amount</option>
            <option value="amount-low">Lowest Amount</option>
            <option value="expiring-soon">Expiring Soon</option>
          </select>
        </div>
      </div>

      {/* Offers List */}
      <div className="space-y-4">
        {sortedOffers.map((offer) => {
          const daysUntilExpiration = getDaysUntilExpiration(offer.expirationDate);
          const isExpiringSoon = daysUntilExpiration <= 2 && daysUntilExpiration > 0;
          const isExpired = daysUntilExpiration <= 0;

          return (
            <div key={offer.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start space-x-4">
                <img
                  src={offer.propertyImage}
                  alt={offer.propertyTitle}
                  className="w-24 h-24 object-cover rounded-lg flex-shrink-0"
                />
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">{offer.propertyTitle}</h3>
                      <p className="text-sm text-gray-600 flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {offer.propertyAddress}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(offer.status)}`}>
                        {getStatusIcon(offer.status)}
                        <span className="ml-1 capitalize">{offer.status}</span>
                      </span>
                      {isExpiringSoon && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Expires in {daysUntilExpiration} day{daysUntilExpiration !== 1 ? 's' : ''}
                        </span>
                      )}
                      {isExpired && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Expired
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-500">Listing Price</p>
                      <p className="font-semibold">{formatPrice(offer.propertyDetails.listingPrice)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Your Offer</p>
                      <p className="font-semibold text-blue-600">{formatPrice(offer.offerAmount)}</p>
                    </div>
                    {offer.counterOffer && (
                      <div>
                        <p className="text-sm text-gray-500">Counter Offer</p>
                        <p className="font-semibold text-orange-600">{formatPrice(offer.counterOffer.amount)}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm text-gray-500">Financing</p>
                      <p className="font-semibold capitalize">{offer.financing.type}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6 text-sm text-gray-600 mb-4">
                    <span className="flex items-center">
                      <Bed className="w-4 h-4 mr-1" />
                      {offer.propertyDetails.bedrooms} bed
                    </span>
                    <span className="flex items-center">
                      <Bath className="w-4 h-4 mr-1" />
                      {offer.propertyDetails.bathrooms} bath
                    </span>
                    <span className="flex items-center">
                      <Square className="w-4 h-4 mr-1" />
                      {offer.propertyDetails.sqft.toLocaleString()} sqft
                    </span>
                    <span className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      Close: {formatDate(offer.closingDate)}
                    </span>
                  </div>

                  {offer.counterOffer?.message && (
                    <div className="bg-blue-50 rounded-lg p-3 mb-4">
                      <p className="text-sm font-medium text-blue-900 mb-1">Counter Offer Message:</p>
                      <p className="text-sm text-blue-800">{offer.counterOffer.message}</p>
                    </div>
                  )}

                  {offer.notes && (
                    <div className="bg-gray-50 rounded-lg p-3 mb-4">
                      <p className="text-sm text-gray-700">{offer.notes}</p>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>Offered: {formatDate(offer.offerDate)}</span>
                      <span>Expires: {formatDate(offer.expirationDate)}</span>
                    </div>
                    <div className="flex space-x-2">
                      <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      {(offer.status === 'pending' || offer.status === 'countered') && (
                        <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                          <Edit className="w-4 h-4" />
                        </button>
                      )}
                      <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <Phone className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {sortedOffers.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <HandCoins className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No offers found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterStatus !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'You haven\'t made any offers yet. Start browsing properties to make your first offer.'
            }
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Browse Properties
          </button>
        </div>
      )}
    </div>
  );
}
