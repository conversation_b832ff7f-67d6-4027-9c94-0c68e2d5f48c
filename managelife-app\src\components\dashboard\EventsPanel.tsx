'use client';

import { useState } from 'react';
import { 
  Calendar,
  Clock,
  MapPin,
  Users,
  Plus,
  Search,
  Filter,
  Eye,
  Share2,
  Heart,
  MessageSquare,
  User,
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  CalendarDays,
  Ticket,
  Gift,
  Music,
  Coffee,
  Home,
  Building2,
  PartyPopper
} from 'lucide-react';

interface CommunityEvent {
  id: string;
  title: string;
  description: string;
  organizer: {
    name: string;
    avatar?: string;
    role: 'homeowner' | 'renter' | 'buyer' | 'manager' | 'member';
  };
  date: string;
  time: string;
  location: string;
  category: 'social' | 'educational' | 'maintenance' | 'meeting' | 'celebration';
  attendees: number;
  maxAttendees?: number;
  isAttending: boolean;
  isFree: boolean;
  price?: number;
  images?: string[];
  tags: string[];
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  rsvpDeadline?: string;
}

export default function EventsPanel() {
  const [activeFilter, setActiveFilter] = useState<'all' | 'upcoming' | 'ongoing' | 'completed'>('upcoming');
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'social' | 'educational' | 'maintenance' | 'meeting' | 'celebration'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock events data
  const [events] = useState<CommunityEvent[]>([
    {
      id: '1',
      title: 'Monthly Community Meeting',
      description: 'Join us for our monthly community meeting to discuss upcoming projects, budget updates, and community concerns. Light refreshments will be provided.',
      organizer: {
        name: 'Community Manager',
        avatar: '/api/placeholder/150/150',
        role: 'manager'
      },
      date: '2024-02-03T00:00:00Z',
      time: '14:00',
      location: 'Community Center - Main Hall',
      category: 'meeting',
      attendees: 23,
      maxAttendees: 50,
      isAttending: true,
      isFree: true,
      images: ['/api/placeholder/400/300'],
      tags: ['meeting', 'community', 'monthly'],
      status: 'upcoming',
      rsvpDeadline: '2024-02-01T23:59:59Z'
    },
    {
      id: '2',
      title: 'Winter Block Party',
      description: 'Come celebrate winter with hot cocoa, live music, and fun activities for the whole family! Bring your friends and neighbors for a cozy evening.',
      organizer: {
        name: 'Sarah Johnson',
        avatar: '/api/placeholder/150/150',
        role: 'homeowner'
      },
      date: '2024-02-10T00:00:00Z',
      time: '17:00',
      location: 'Central Park Area',
      category: 'celebration',
      attendees: 45,
      maxAttendees: 100,
      isAttending: false,
      isFree: true,
      images: ['/api/placeholder/400/300', '/api/placeholder/400/300'],
      tags: ['party', 'winter', 'family', 'music'],
      status: 'upcoming'
    },
    {
      id: '3',
      title: 'Home Maintenance Workshop',
      description: 'Learn essential home maintenance skills from local experts. Topics include plumbing basics, electrical safety, and seasonal maintenance tips.',
      organizer: {
        name: 'Mike Chen',
        role: 'homeowner'
      },
      date: '2024-02-15T00:00:00Z',
      time: '10:00',
      location: 'Community Workshop Room',
      category: 'educational',
      attendees: 12,
      maxAttendees: 20,
      isAttending: true,
      isFree: false,
      price: 15,
      tags: ['workshop', 'maintenance', 'education'],
      status: 'upcoming',
      rsvpDeadline: '2024-02-13T23:59:59Z'
    },
    {
      id: '4',
      title: 'Coffee & Chat Morning',
      description: 'Casual morning meetup for neighbors to connect over coffee and pastries. Great opportunity to meet new people in the community.',
      organizer: {
        name: 'Emily Rodriguez',
        role: 'renter'
      },
      date: '2024-01-28T00:00:00Z',
      time: '09:00',
      location: 'Community Garden Pavilion',
      category: 'social',
      attendees: 18,
      isAttending: false,
      isFree: true,
      tags: ['coffee', 'social', 'morning'],
      status: 'completed'
    },
    {
      id: '5',
      title: 'Spring Garden Planning',
      description: 'Plan and prepare for the community garden spring planting. We\'ll discuss plot assignments, plant selections, and maintenance schedules.',
      organizer: {
        name: 'David Kim',
        role: 'homeowner'
      },
      date: '2024-02-20T00:00:00Z',
      time: '15:30',
      location: 'Community Garden',
      category: 'meeting',
      attendees: 8,
      maxAttendees: 25,
      isAttending: false,
      isFree: true,
      tags: ['garden', 'planning', 'spring'],
      status: 'upcoming'
    }
  ]);

  const filteredEvents = events.filter(event => {
    const matchesStatus = activeFilter === 'all' || event.status === activeFilter;
    const matchesCategory = categoryFilter === 'all' || event.category === categoryFilter;
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesStatus && matchesCategory && matchesSearch;
  });

  // Sort events by date
  const sortedEvents = [...filteredEvents].sort((a, b) => {
    return new Date(a.date).getTime() - new Date(b.date).getTime();
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'social':
        return 'bg-blue-100 text-blue-800';
      case 'educational':
        return 'bg-purple-100 text-purple-800';
      case 'maintenance':
        return 'bg-orange-100 text-orange-800';
      case 'meeting':
        return 'bg-gray-100 text-gray-800';
      case 'celebration':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'social':
        return <Coffee className="w-4 h-4" />;
      case 'educational':
        return <Star className="w-4 h-4" />;
      case 'maintenance':
        return <Building2 className="w-4 h-4" />;
      case 'meeting':
        return <Users className="w-4 h-4" />;
      case 'celebration':
        return <PartyPopper className="w-4 h-4" />;
      default:
        return <Calendar className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-green-100 text-green-800';
      case 'ongoing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const totalEvents = events.length;
  const upcomingEvents = events.filter(e => e.status === 'upcoming').length;
  const myEvents = events.filter(e => e.isAttending).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Community Events</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Create Event
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Events</p>
              <p className="text-2xl font-bold text-gray-900">{totalEvents}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CalendarDays className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Upcoming</p>
              <p className="text-2xl font-bold text-gray-900">{upcomingEvents}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Ticket className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">My Events</p>
              <p className="text-2xl font-bold text-gray-900">{myEvents}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Categories</option>
            <option value="social">Social</option>
            <option value="educational">Educational</option>
            <option value="maintenance">Maintenance</option>
            <option value="meeting">Meeting</option>
            <option value="celebration">Celebration</option>
          </select>
        </div>

        <div className="flex flex-wrap gap-2">
          {['all', 'upcoming', 'ongoing', 'completed'].map((filter) => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter as any)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                activeFilter === filter
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {filter.charAt(0).toUpperCase() + filter.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Events Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedEvents.map((event) => (
          <div key={event.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            {event.images && event.images.length > 0 && (
              <div className="relative h-48">
                <img
                  src={event.images[0]}
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 left-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                    {event.status}
                  </span>
                </div>
                <div className="absolute top-4 right-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(event.category)}`}>
                    {getCategoryIcon(event.category)}
                    <span className="ml-1">{event.category}</span>
                  </span>
                </div>
              </div>
            )}

            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{event.title}</h3>
                {event.isAttending && (
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 ml-2" />
                )}
              </div>

              <p className="text-gray-600 text-sm mb-4 line-clamp-3">{event.description}</p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span>{formatDate(event.date)}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="w-4 h-4 mr-2" />
                  <span>{formatTime(event.time)}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="w-4 h-4 mr-2" />
                  <span>{event.location}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="w-4 h-4 mr-2" />
                  <span>
                    {event.attendees} attending
                    {event.maxAttendees && ` / ${event.maxAttendees} max`}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  {event.organizer.avatar ? (
                    <img
                      src={event.organizer.avatar}
                      alt={event.organizer.name}
                      className="w-6 h-6 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="w-3 h-3 text-gray-400" />
                    </div>
                  )}
                  <span className="text-sm text-gray-600">by {event.organizer.name}</span>
                </div>
                <div className="text-sm font-medium">
                  {event.isFree ? (
                    <span className="text-green-600">Free</span>
                  ) : (
                    <span className="text-gray-900">${event.price}</span>
                  )}
                </div>
              </div>

              <div className="flex flex-wrap gap-1 mb-4">
                {event.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600"
                  >
                    #{tag}
                  </span>
                ))}
                {event.tags.length > 3 && (
                  <span className="text-xs text-gray-500">+{event.tags.length - 3} more</span>
                )}
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <button className="text-gray-600 hover:text-gray-700 p-1 rounded">
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="text-gray-600 hover:text-gray-700 p-1 rounded">
                    <Share2 className="w-4 h-4" />
                  </button>
                </div>
                {event.status === 'upcoming' && (
                  <button className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    event.isAttending
                      ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}>
                    {event.isAttending ? 'Cancel RSVP' : 'RSVP'}
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {sortedEvents.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || categoryFilter !== 'all' || activeFilter !== 'upcoming'
              ? 'Try adjusting your search or filters.'
              : 'No upcoming events scheduled. Be the first to create one!'
            }
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Create Event
          </button>
        </div>
      )}
    </div>
  );
}
