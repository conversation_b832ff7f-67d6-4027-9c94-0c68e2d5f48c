{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/SettingsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  User,\n  Shield,\n  Bell,\n  Globe,\n  Palette,\n  Database,\n  Key,\n  Smartphone,\n  Mail,\n  Lock,\n  Eye,\n  EyeOff,\n  Save,\n  X,\n  Check,\n  AlertTriangle,\n  Trash2,\n  Download,\n  Upload,\n  Settings\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface SettingsData {\n  // Profile Settings\n  displayName: string;\n  bio: string;\n  location: string;\n  website: string;\n  \n  // Privacy Settings\n  profileVisibility: 'public' | 'private' | 'friends';\n  showEmail: boolean;\n  showWallet: boolean;\n  showActivity: boolean;\n  \n  // Notification Settings\n  emailNotifications: boolean;\n  pushNotifications: boolean;\n  rewardNotifications: boolean;\n  communityNotifications: boolean;\n  marketingEmails: boolean;\n  \n  // App Preferences\n  language: string;\n  currency: string;\n  timezone: string;\n  theme: string;\n  \n  // Security Settings\n  twoFactorEnabled: boolean;\n  loginAlerts: boolean;\n  sessionTimeout: number;\n}\n\nexport default function SettingsPanel() {\n  const { user } = useAuth();\n  const [activeSection, setActiveSection] = useState<'profile' | 'privacy' | 'notifications' | 'preferences' | 'security' | 'data'>('profile');\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n\n  const [settings, setSettings] = useState<SettingsData>({\n    displayName: user?.name || '',\n    bio: '',\n    location: '',\n    website: '',\n    profileVisibility: 'public',\n    showEmail: false,\n    showWallet: false,\n    showActivity: true,\n    emailNotifications: true,\n    pushNotifications: true,\n    rewardNotifications: true,\n    communityNotifications: false,\n    marketingEmails: false,\n    language: 'en',\n    currency: 'usd',\n    timezone: 'UTC',\n    theme: 'light',\n    twoFactorEnabled: false,\n    loginAlerts: true,\n    sessionTimeout: 30,\n  });\n\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n\n  const handleSaveSettings = async () => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setSaveMessage({ type: 'success', text: 'Settings saved successfully!' });\n    } catch (error: any) {\n      setSaveMessage({ type: 'error', text: error.message || 'Failed to save settings' });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const sections = [\n    { id: 'profile', label: 'Profile', icon: User },\n    { id: 'privacy', label: 'Privacy', icon: Shield },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'preferences', label: 'Preferences', icon: Globe },\n    { id: 'security', label: 'Security', icon: Lock },\n    { id: 'data', label: 'Data & Privacy', icon: Database },\n  ];\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n      <div className=\"border-b border-gray-200\">\n        <div className=\"flex space-x-8 px-6\">\n          {sections.map((section) => {\n            const Icon = section.icon;\n            return (\n              <button\n                key={section.id}\n                onClick={() => setActiveSection(section.id as any)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                  activeSection === section.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"w-4 h-4 mr-2\" />\n                {section.label}\n              </button>\n            );\n          })}\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        {/* Save Message */}\n        {saveMessage && (\n          <div className={`mb-6 p-4 rounded-lg flex items-center ${\n            saveMessage.type === 'success' \n              ? 'bg-green-50 border border-green-200 text-green-700' \n              : 'bg-red-50 border border-red-200 text-red-700'\n          }`}>\n            {saveMessage.type === 'success' ? (\n              <Check className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n            ) : (\n              <AlertTriangle className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n            )}\n            <p>{saveMessage.text}</p>\n            <button\n              onClick={() => setSaveMessage(null)}\n              className=\"ml-auto text-gray-400 hover:text-gray-600\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          </div>\n        )}\n\n        {/* Profile Settings */}\n        {activeSection === 'profile' && (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Profile Information</h3>\n            \n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Display Name\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.displayName}\n                  onChange={(e) => setSettings(prev => ({ ...prev, displayName: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Location\n                </label>\n                <input\n                  type=\"text\"\n                  value={settings.location}\n                  onChange={(e) => setSettings(prev => ({ ...prev, location: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"City, Country\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Bio\n              </label>\n              <textarea\n                value={settings.bio}\n                onChange={(e) => setSettings(prev => ({ ...prev, bio: e.target.value }))}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Tell us about yourself...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Website\n              </label>\n              <input\n                type=\"url\"\n                value={settings.website}\n                onChange={(e) => setSettings(prev => ({ ...prev, website: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"https://yourwebsite.com\"\n              />\n            </div>\n          </div>\n        )}\n\n        {/* Notifications Settings */}\n        {activeSection === 'notifications' && (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Notification Preferences</h3>\n            \n            <div className=\"space-y-4\">\n              {[\n                { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },\n                { key: 'pushNotifications', label: 'Push Notifications', description: 'Receive push notifications in your browser' },\n                { key: 'rewardNotifications', label: 'Reward Notifications', description: 'Get notified when you earn rewards' },\n                { key: 'communityNotifications', label: 'Community Updates', description: 'Updates from the ManageLife community' },\n                { key: 'marketingEmails', label: 'Marketing Emails', description: 'Promotional emails and product updates' },\n              ].map((item) => (\n                <div key={item.key} className=\"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0\">\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">{item.label}</h4>\n                    <p className=\"text-sm text-gray-600\">{item.description}</p>\n                  </div>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings[item.key as keyof SettingsData] as boolean}\n                      onChange={(e) => setSettings(prev => ({ ...prev, [item.key]: e.target.checked }))}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Other sections would go here... */}\n        {activeSection !== 'profile' && activeSection !== 'notifications' && (\n          <div className=\"text-center py-12\">\n            <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Settings className=\"w-8 h-8 text-gray-400\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {sections.find(s => s.id === activeSection)?.label} Settings\n            </h3>\n            <p className=\"text-gray-600\">This section is coming soon.</p>\n          </div>\n        )}\n\n        {/* Save Button */}\n        {(activeSection === 'profile' || activeSection === 'notifications') && (\n          <div className=\"mt-8 pt-6 border-t border-gray-200\">\n            <button\n              onClick={handleSaveSettings}\n              disabled={isSaving}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n            >\n              {isSaving ? (\n                <>\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                  Saving...\n                </>\n              ) : (\n                <>\n                  <Save className=\"w-4 h-4 mr-2\" />\n                  Save Changes\n                </>\n              )}\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;;;AAzBA;;;;AA2De,SAAS;QA+MT;;IA9Mb,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiF;IAClI,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IACnG,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,aAAa,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI;QAC3B,KAAK;QACL,UAAU;QACV,SAAS;QACT,mBAAmB;QACnB,WAAW;QACX,YAAY;QACZ,cAAc;QACd,oBAAoB;QACpB,mBAAmB;QACnB,qBAAqB;QACrB,wBAAwB;QACxB,iBAAiB;QACjB,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IAEA,MAAM,qBAAqB;QACzB,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,eAAe;gBAAE,MAAM;gBAAW,MAAM;YAA+B;QACzE,EAAE,OAAO,OAAY;YACnB,eAAe;gBAAE,MAAM;gBAAS,MAAM,MAAM,OAAO,IAAI;YAA0B;QACnF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC9C;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,yMAAA,CAAA,SAAM;QAAC;QAChD;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM,uMAAA,CAAA,QAAK;QAAC;QACvD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,qMAAA,CAAA,OAAI;QAAC;QAChD;YAAE,IAAI;YAAQ,OAAO;YAAkB,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,6LAAC;4BAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;4BAC1C,WAAW,AAAC,8DAIX,OAHC,kBAAkB,QAAQ,EAAE,GACxB,kCACA;;8CAGN,6LAAC;oCAAK,WAAU;;;;;;gCACf,QAAQ,KAAK;;2BATT,QAAQ,EAAE;;;;;oBAYrB;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC;wBAAI,WAAW,AAAC,yCAIhB,OAHC,YAAY,IAAI,KAAK,YACjB,uDACA;;4BAEH,YAAY,IAAI,KAAK,0BACpB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;qDAEjB,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CAE3B,6LAAC;0CAAG,YAAY,IAAI;;;;;;0CACpB,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMlB,kBAAkB,2BACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC9E,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC3E,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO,SAAS,GAAG;wCACnB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,KAAK,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACtE,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;oBAOnB,kBAAkB,iCACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,KAAK;wCAAsB,OAAO;wCAAuB,aAAa;oCAAkC;oCAC1G;wCAAE,KAAK;wCAAqB,OAAO;wCAAsB,aAAa;oCAA6C;oCACnH;wCAAE,KAAK;wCAAuB,OAAO;wCAAwB,aAAa;oCAAqC;oCAC/G;wCAAE,KAAK;wCAA0B,OAAO;wCAAqB,aAAa;oCAAwC;oCAClH;wCAAE,KAAK;wCAAmB,OAAO;wCAAoB,aAAa;oCAAyC;iCAC5G,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B,KAAK,KAAK;;;;;;kEACrD,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;0DAExD,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,QAAQ,CAAC,KAAK,GAAG,CAAuB;wDACjD,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;gEAAC,CAAC;wDAC/E,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAZT,KAAK,GAAG;;;;;;;;;;;;;;;;oBAqBzB,kBAAkB,aAAa,kBAAkB,iCAChD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAG,WAAU;;qCACX,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,4BAA5B,qCAAA,eAA4C,KAAK;oCAAC;;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAKhC,CAAC,kBAAkB,aAAa,kBAAkB,eAAe,mBAChE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,yBACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAA0F;;6DAI3G;;kDACE,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GA9OwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/PortfolioPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  TrendingUp, \n  TrendingDown,\n  DollarSign,\n  Building2,\n  Pie<PERSON>hart,\n  BarChart3,\n  Calendar,\n  Filter,\n  Download,\n  Plus,\n  Eye,\n  Edit,\n  Trash2,\n  MapPin,\n  Users,\n  Coins\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface Property {\n  id: string;\n  title: string;\n  location: string;\n  type: 'residential' | 'commercial' | 'industrial';\n  value: number;\n  purchasePrice: number;\n  purchaseDate: string;\n  monthlyIncome: number;\n  occupancyRate: number;\n  roi: number;\n  status: 'active' | 'pending' | 'sold';\n  image: string;\n  tenants: number;\n  maxTenants: number;\n}\n\ninterface PortfolioStats {\n  totalValue: number;\n  totalInvestment: number;\n  totalReturn: number;\n  monthlyIncome: number;\n  averageROI: number;\n  propertiesCount: number;\n  occupancyRate: number;\n}\n\nexport default function PortfolioPanel() {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'analytics'>('overview');\n  const [filterType, setFilterType] = useState<'all' | 'residential' | 'commercial' | 'industrial'>('all');\n\n  // Mock portfolio data\n  const portfolioStats: PortfolioStats = {\n    totalValue: 2450000,\n    totalInvestment: 1850000,\n    totalReturn: 600000,\n    monthlyIncome: 18500,\n    averageROI: 12.5,\n    propertiesCount: 8,\n    occupancyRate: 92.3,\n  };\n\n  const properties: Property[] = [\n    {\n      id: '1',\n      title: 'Luxury Downtown Apartment',\n      location: 'Manhattan, NY',\n      type: 'residential',\n      value: 850000,\n      purchasePrice: 720000,\n      purchaseDate: '2023-03-15',\n      monthlyIncome: 6500,\n      occupancyRate: 100,\n      roi: 15.2,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 1,\n      maxTenants: 1,\n    },\n    {\n      id: '2',\n      title: 'Modern Office Complex',\n      location: 'Austin, TX',\n      type: 'commercial',\n      value: 1200000,\n      purchasePrice: 950000,\n      purchaseDate: '2022-11-20',\n      monthlyIncome: 8200,\n      occupancyRate: 85,\n      roi: 18.7,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 12,\n      maxTenants: 15,\n    },\n    {\n      id: '3',\n      title: 'Suburban Family Home',\n      location: 'Phoenix, AZ',\n      type: 'residential',\n      value: 400000,\n      purchasePrice: 350000,\n      purchaseDate: '2023-07-10',\n      monthlyIncome: 2800,\n      occupancyRate: 100,\n      roi: 11.4,\n      status: 'active',\n      image: '/api/placeholder/300/200',\n      tenants: 1,\n      maxTenants: 1,\n    },\n  ];\n\n  const filteredProperties = properties.filter(property => \n    filterType === 'all' || property.type === filterType\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Portfolio Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Portfolio Value</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.totalValue.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-blue-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+12.5%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Monthly Income</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.monthlyIncome.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Coins className=\"w-6 h-6 text-green-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+8.2%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Average ROI</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{portfolioStats.averageROI}%</p>\n            </div>\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-purple-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+2.1%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last quarter</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Occupancy Rate</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{portfolioStats.occupancyRate}%</p>\n            </div>\n            <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <Building2 className=\"w-6 h-6 text-orange-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingDown className=\"w-4 h-4 text-red-500 mr-1\" />\n            <span className=\"text-sm text-red-600 font-medium\">-1.2%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {[\n              { id: 'overview', label: 'Overview', icon: PieChart },\n              { id: 'properties', label: 'Properties', icon: Building2 },\n              { id: 'analytics', label: 'Analytics', icon: BarChart3 },\n            ].map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4 mr-2\" />\n                  {tab.label}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'overview' && (\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Portfolio Distribution</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-600\">Residential</span>\n                    <span className=\"font-medium\">65%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div className=\"bg-blue-600 h-2 rounded-full\" style={{ width: '65%' }}></div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-600\">Commercial</span>\n                    <span className=\"font-medium\">30%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div className=\"bg-green-600 h-2 rounded-full\" style={{ width: '30%' }}></div>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-gray-600\">Industrial</span>\n                    <span className=\"font-medium\">5%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div className=\"bg-purple-600 h-2 rounded-full\" style={{ width: '5%' }}></div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activity</h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm text-gray-600\">Rent payment received - $6,500</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span className=\"text-sm text-gray-600\">Property valuation updated</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                    <span className=\"text-sm text-gray-600\">Maintenance request submitted</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'properties' && (\n            <div className=\"space-y-6\">\n              {/* Filters */}\n              <div className=\"flex items-center justify-between\">\n                <select\n                  value={filterType}\n                  onChange={(e) => setFilterType(e.target.value as any)}\n                  className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"all\">All Types</option>\n                  <option value=\"residential\">Residential</option>\n                  <option value=\"commercial\">Commercial</option>\n                  <option value=\"industrial\">Industrial</option>\n                </select>\n                \n                <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  Add Property\n                </button>\n              </div>\n\n              {/* Properties Grid */}\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {filteredProperties.map((property) => (\n                  <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n                    <img\n                      src={property.image}\n                      alt={property.title}\n                      className=\"w-full h-48 object-cover\"\n                    />\n                    <div className=\"p-6\">\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-1\">{property.title}</h3>\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                          property.status === 'active' ? 'bg-green-100 text-green-800' :\n                          property.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                          'bg-gray-100 text-gray-800'\n                        }`}>\n                          {property.status}\n                        </span>\n                      </div>\n                      \n                      <div className=\"flex items-center text-gray-600 mb-3\">\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        <span className=\"text-sm\">{property.location}</span>\n                      </div>\n                      \n                      <div className=\"space-y-2 mb-4\">\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">Current Value</span>\n                          <span className=\"font-medium\">${property.value.toLocaleString()}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">Monthly Income</span>\n                          <span className=\"font-medium text-green-600\">${property.monthlyIncome.toLocaleString()}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">ROI</span>\n                          <span className=\"font-medium text-blue-600\">{property.roi}%</span>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center text-sm text-gray-600\">\n                          <Users className=\"w-4 h-4 mr-1\" />\n                          {property.tenants}/{property.maxTenants} tenants\n                        </div>\n                        <div className=\"flex space-x-2\">\n                          <button className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\">\n                            <Eye className=\"w-4 h-4\" />\n                          </button>\n                          <button className=\"p-2 text-gray-400 hover:text-green-600 transition-colors\">\n                            <Edit className=\"w-4 h-4\" />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'analytics' && (\n            <div className=\"text-center py-12\">\n              <BarChart3 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Analytics Dashboard</h3>\n              <p className=\"text-gray-600\">Detailed analytics and performance metrics coming soon.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;;;AArBA;;;;AAkDe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2C;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuD;IAElG,sBAAsB;IACtB,MAAM,iBAAiC;QACrC,YAAY;QACZ,iBAAiB;QACjB,aAAa;QACb,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,aAAyB;QAC7B;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;YACP,eAAe;YACf,cAAc;YACd,eAAe;YACf,eAAe;YACf,KAAK;YACL,QAAQ;YACR,OAAO;YACP,SAAS;YACT,YAAY;QACd;KACD;IAED,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,WAC3C,eAAe,SAAS,SAAS,IAAI,KAAK;IAG5C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAmC;oDAAE,eAAe,UAAU,CAAC,cAAc;;;;;;;;;;;;;kDAE5F,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAmC;oDAAE,eAAe,aAAa,CAAC,cAAc;;;;;;;;;;;;;kDAE/F,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAoC,eAAe,UAAU;oDAAC;;;;;;;;;;;;;kDAE7E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAoC,eAAe,aAAa;oDAAC;;;;;;;;;;;;;kDAEhF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM,iNAAA,CAAA,WAAQ;gCAAC;gCACpD;oCAAE,IAAI;oCAAc,OAAO;oCAAc,MAAM,mNAAA,CAAA,YAAS;gCAAC;gCACzD;oCAAE,IAAI;oCAAa,OAAO;oCAAa,MAAM,qNAAA,CAAA,YAAS;gCAAC;6BACxD,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,AAAC,8DAIX,OAHC,cAAc,IAAI,EAAE,GAChB,kCACA;;sDAGN,6LAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,4BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAGtE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAgC,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAGvE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAiC,OAAO;gEAAE,OAAO;4DAAK;;;;;;;;;;;;;;;;;;;;;;;kDAK3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOjD,cAAc,8BACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAc;;;;;;kEAC5B,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAa;;;;;;;;;;;;0DAG7B,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAMrC,6LAAC;wCAAI,WAAU;kDACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC;wDACC,KAAK,SAAS,KAAK;wDACnB,KAAK,SAAS,KAAK;wDACnB,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAoD,SAAS,KAAK;;;;;;kFAChF,6LAAC;wEAAK,WAAW,AAAC,8CAIjB,OAHC,SAAS,MAAM,KAAK,WAAW,gCAC/B,SAAS,MAAM,KAAK,YAAY,kCAChC;kFAEC,SAAS,MAAM;;;;;;;;;;;;0EAIpB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6LAAC;wEAAK,WAAU;kFAAW,SAAS,QAAQ;;;;;;;;;;;;0EAG9C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,6LAAC;gFAAK,WAAU;;oFAAc;oFAAE,SAAS,KAAK,CAAC,cAAc;;;;;;;;;;;;;kFAE/D,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,6LAAC;gFAAK,WAAU;;oFAA6B;oFAAE,SAAS,aAAa,CAAC,cAAc;;;;;;;;;;;;;kFAEtF,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,6LAAC;gFAAK,WAAU;;oFAA6B,SAAS,GAAG;oFAAC;;;;;;;;;;;;;;;;;;;0EAI9D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,SAAS,OAAO;4EAAC;4EAAE,SAAS,UAAU;4EAAC;;;;;;;kFAE1C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAO,WAAU;0FAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;;;;;;0FAEjB,6LAAC;gFAAO,WAAU;0FAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAhDhB,SAAS,EAAE;;;;;;;;;;;;;;;;4BA2D5B,cAAc,6BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GA/TwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 1739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/RentalsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Home,\n  Calendar,\n  DollarSign,\n  User,\n  Phone,\n  Mail,\n  MapPin,\n  Clock,\n  AlertCircle,\n  CheckCircle,\n  XCircle,\n  Filter,\n  Search,\n  Plus,\n  Eye,\n  Edit,\n  MessageSquare,\n  FileText,\n  Wrench\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface Rental {\n  id: string;\n  propertyTitle: string;\n  propertyAddress: string;\n  tenantName: string;\n  tenantEmail: string;\n  tenantPhone: string;\n  rentAmount: number;\n  leaseStart: string;\n  leaseEnd: string;\n  status: 'active' | 'pending' | 'expired' | 'terminated';\n  paymentStatus: 'paid' | 'pending' | 'overdue';\n  lastPayment: string;\n  nextPayment: string;\n  securityDeposit: number;\n  propertyImage: string;\n}\n\ninterface MaintenanceRequest {\n  id: string;\n  propertyTitle: string;\n  tenantName: string;\n  issue: string;\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  status: 'open' | 'in-progress' | 'completed';\n  dateSubmitted: string;\n  description: string;\n}\n\nexport default function RentalsPanel() {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'rentals' | 'maintenance' | 'applications'>('rentals');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'pending' | 'expired'>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock rental data\n  const rentals: Rental[] = [\n    {\n      id: '1',\n      propertyTitle: 'Luxury Downtown Apartment',\n      propertyAddress: '123 Main St, Manhattan, NY',\n      tenantName: 'Sarah Johnson',\n      tenantEmail: '<EMAIL>',\n      tenantPhone: '+1 (*************',\n      rentAmount: 6500,\n      leaseStart: '2023-01-01',\n      leaseEnd: '2024-12-31',\n      status: 'active',\n      paymentStatus: 'paid',\n      lastPayment: '2024-01-01',\n      nextPayment: '2024-02-01',\n      securityDeposit: 13000,\n      propertyImage: '/api/placeholder/300/200',\n    },\n    {\n      id: '2',\n      propertyTitle: 'Suburban Family Home',\n      propertyAddress: '456 Oak Ave, Phoenix, AZ',\n      tenantName: 'Michael Chen',\n      tenantEmail: '<EMAIL>',\n      tenantPhone: '+1 (*************',\n      rentAmount: 2800,\n      leaseStart: '2023-07-01',\n      leaseEnd: '2024-06-30',\n      status: 'active',\n      paymentStatus: 'pending',\n      lastPayment: '2023-12-01',\n      nextPayment: '2024-01-01',\n      securityDeposit: 5600,\n      propertyImage: '/api/placeholder/300/200',\n    },\n    {\n      id: '3',\n      propertyTitle: 'Modern Office Space',\n      propertyAddress: '789 Business Blvd, Austin, TX',\n      tenantName: 'TechStart Inc.',\n      tenantEmail: '<EMAIL>',\n      tenantPhone: '+1 (*************',\n      rentAmount: 8200,\n      leaseStart: '2022-11-01',\n      leaseEnd: '2025-10-31',\n      status: 'active',\n      paymentStatus: 'overdue',\n      lastPayment: '2023-11-01',\n      nextPayment: '2023-12-01',\n      securityDeposit: 16400,\n      propertyImage: '/api/placeholder/300/200',\n    },\n  ];\n\n  const maintenanceRequests: MaintenanceRequest[] = [\n    {\n      id: '1',\n      propertyTitle: 'Luxury Downtown Apartment',\n      tenantName: 'Sarah Johnson',\n      issue: 'Leaking faucet in kitchen',\n      priority: 'medium',\n      status: 'open',\n      dateSubmitted: '2024-01-15',\n      description: 'The kitchen faucet has been dripping constantly for the past week.',\n    },\n    {\n      id: '2',\n      propertyTitle: 'Suburban Family Home',\n      tenantName: 'Michael Chen',\n      issue: 'Heating system not working',\n      priority: 'urgent',\n      status: 'in-progress',\n      dateSubmitted: '2024-01-10',\n      description: 'The heating system stopped working completely. House is very cold.',\n    },\n  ];\n\n  const filteredRentals = rentals.filter(rental => {\n    const matchesStatus = filterStatus === 'all' || rental.status === filterStatus;\n    const matchesSearch = rental.propertyTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         rental.tenantName.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesStatus && matchesSearch;\n  });\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'expired': return 'bg-red-100 text-red-800';\n      case 'terminated': return 'bg-gray-100 text-gray-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPaymentStatusColor = (status: string) => {\n    switch (status) {\n      case 'paid': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'overdue': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'low': return 'bg-blue-100 text-blue-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'high': return 'bg-orange-100 text-orange-800';\n      case 'urgent': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Summary Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Active Rentals</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{rentals.filter(r => r.status === 'active').length}</p>\n            </div>\n            <Home className=\"w-8 h-8 text-blue-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Monthly Revenue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                ${rentals.filter(r => r.status === 'active').reduce((sum, r) => sum + r.rentAmount, 0).toLocaleString()}\n              </p>\n            </div>\n            <DollarSign className=\"w-8 h-8 text-green-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Overdue Payments</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{rentals.filter(r => r.paymentStatus === 'overdue').length}</p>\n            </div>\n            <AlertCircle className=\"w-8 h-8 text-red-600\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Maintenance Requests</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{maintenanceRequests.filter(r => r.status !== 'completed').length}</p>\n            </div>\n            <Wrench className=\"w-8 h-8 text-orange-600\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {[\n              { id: 'rentals', label: 'Active Rentals', icon: Home },\n              { id: 'maintenance', label: 'Maintenance', icon: Wrench },\n              { id: 'applications', label: 'Applications', icon: FileText },\n            ].map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4 mr-2\" />\n                  {tab.label}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'rentals' && (\n            <div className=\"space-y-6\">\n              {/* Filters */}\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search properties or tenants...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n                <select\n                  value={filterStatus}\n                  onChange={(e) => setFilterStatus(e.target.value as any)}\n                  className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"active\">Active</option>\n                  <option value=\"pending\">Pending</option>\n                  <option value=\"expired\">Expired</option>\n                </select>\n              </div>\n\n              {/* Rentals List */}\n              <div className=\"space-y-4\">\n                {filteredRentals.map((rental) => (\n                  <div key={rental.id} className=\"bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors\">\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div className=\"flex items-start space-x-4\">\n                        <img\n                          src={rental.propertyImage}\n                          alt={rental.propertyTitle}\n                          className=\"w-16 h-16 rounded-lg object-cover\"\n                        />\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-gray-900\">{rental.propertyTitle}</h3>\n                          <div className=\"flex items-center text-gray-600 mt-1\">\n                            <MapPin className=\"w-4 h-4 mr-1\" />\n                            <span className=\"text-sm\">{rental.propertyAddress}</span>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(rental.status)}`}>\n                          {rental.status}\n                        </span>\n                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPaymentStatusColor(rental.paymentStatus)}`}>\n                          {rental.paymentStatus}\n                        </span>\n                      </div>\n                    </div>\n\n                    <div className=\"grid md:grid-cols-3 gap-6\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 mb-2\">Tenant Information</h4>\n                        <div className=\"space-y-1 text-sm text-gray-600\">\n                          <div className=\"flex items-center\">\n                            <User className=\"w-4 h-4 mr-2\" />\n                            {rental.tenantName}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Mail className=\"w-4 h-4 mr-2\" />\n                            {rental.tenantEmail}\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Phone className=\"w-4 h-4 mr-2\" />\n                            {rental.tenantPhone}\n                          </div>\n                        </div>\n                      </div>\n\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 mb-2\">Lease Details</h4>\n                        <div className=\"space-y-1 text-sm text-gray-600\">\n                          <div>Rent: <span className=\"font-medium text-green-600\">${rental.rentAmount.toLocaleString()}/month</span></div>\n                          <div>Lease: {new Date(rental.leaseStart).toLocaleDateString()} - {new Date(rental.leaseEnd).toLocaleDateString()}</div>\n                          <div>Security Deposit: ${rental.securityDeposit.toLocaleString()}</div>\n                        </div>\n                      </div>\n\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 mb-2\">Payment Status</h4>\n                        <div className=\"space-y-1 text-sm text-gray-600\">\n                          <div>Last Payment: {new Date(rental.lastPayment).toLocaleDateString()}</div>\n                          <div>Next Payment: {new Date(rental.nextPayment).toLocaleDateString()}</div>\n                        </div>\n                        <div className=\"flex space-x-2 mt-3\">\n                          <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                            View Details\n                          </button>\n                          <button className=\"text-green-600 hover:text-green-800 text-sm font-medium\">\n                            Contact Tenant\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'maintenance' && (\n            <div className=\"space-y-4\">\n              {maintenanceRequests.map((request) => (\n                <div key={request.id} className=\"bg-gray-50 rounded-lg p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{request.issue}</h3>\n                      <p className=\"text-gray-600\">{request.propertyTitle} • {request.tenantName}</p>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(request.priority)}`}>\n                        {request.priority}\n                      </span>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        request.status === 'completed' ? 'bg-green-100 text-green-800' :\n                        request.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :\n                        'bg-gray-100 text-gray-800'\n                      }`}>\n                        {request.status}\n                      </span>\n                    </div>\n                  </div>\n                  <p className=\"text-gray-700 mb-4\">{request.description}</p>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-500\">\n                      Submitted: {new Date(request.dateSubmitted).toLocaleDateString()}\n                    </span>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                        Update Status\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-800 text-sm font-medium\">\n                        Contact Tenant\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {activeTab === 'applications' && (\n            <div className=\"text-center py-12\">\n              <FileText className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Rental Applications</h3>\n              <p className=\"text-gray-600\">No pending applications at this time.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;;;AAxBA;;;;AAuDe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C;IACvF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IAC3F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,mBAAmB;IACnB,MAAM,UAAoB;QACxB;YACE,IAAI;YACJ,eAAe;YACf,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,eAAe;QACjB;QACA;YACE,IAAI;YACJ,eAAe;YACf,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,eAAe;QACjB;QACA;YACE,IAAI;YACJ,eAAe;YACf,iBAAiB;YACjB,YAAY;YACZ,aAAa;YACb,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,eAAe;QACjB;KACD;IAED,MAAM,sBAA4C;QAChD;YACE,IAAI;YACJ,eAAe;YACf,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;QACf;QACA;YACE,IAAI;YACJ,eAAe;YACf,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;YACR,eAAe;YACf,aAAa;QACf;KACD;IAED,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,KAAK;QAClE,MAAM,gBAAgB,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,OAAO,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACpF,OAAO,iBAAiB;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;8CAEpG,6LAAC,sMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAC5C,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,GAAG,cAAc;;;;;;;;;;;;;8CAGzG,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,WAAW,MAAM;;;;;;;;;;;;8CAE5G,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;8CAEnH,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAW,OAAO;oCAAkB,MAAM,sMAAA,CAAA,OAAI;gCAAC;gCACrD;oCAAE,IAAI;oCAAe,OAAO;oCAAe,MAAM,yMAAA,CAAA,SAAM;gCAAC;gCACxD;oCAAE,IAAI;oCAAgB,OAAO;oCAAgB,MAAM,iNAAA,CAAA,WAAQ;gCAAC;6BAC7D,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,AAAC,8DAIX,OAHC,cAAc,IAAI,EAAE,GAChB,kCACA;;sDAGN,6LAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,2BACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAU;;;;;;;;;;;;;;;;;0DAIhB,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,KAAK,OAAO,aAAa;wEACzB,KAAK,OAAO,aAAa;wEACzB,WAAU;;;;;;kFAEZ,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAAuC,OAAO,aAAa;;;;;;0FACzE,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;kGAClB,6LAAC;wFAAK,WAAU;kGAAW,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;0EAIvD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAW,AAAC,8CAA2E,OAA9B,eAAe,OAAO,MAAM;kFACxF,OAAO,MAAM;;;;;;kFAEhB,6LAAC;wEAAK,WAAW,AAAC,8CAAyF,OAA5C,sBAAsB,OAAO,aAAa;kFACtG,OAAO,aAAa;;;;;;;;;;;;;;;;;;kEAK3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFACf,OAAO,UAAU;;;;;;;0FAEpB,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFACf,OAAO,WAAW;;;;;;;0FAErB,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAChB,OAAO,WAAW;;;;;;;;;;;;;;;;;;;0EAKzB,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAI;kGAAM,6LAAC;wFAAK,WAAU;;4FAA6B;4FAAE,OAAO,UAAU,CAAC,cAAc;4FAAG;;;;;;;;;;;;;0FAC7F,6LAAC;;oFAAI;oFAAQ,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB;oFAAG;oFAAI,IAAI,KAAK,OAAO,QAAQ,EAAE,kBAAkB;;;;;;;0FAC9G,6LAAC;;oFAAI;oFAAoB,OAAO,eAAe,CAAC,cAAc;;;;;;;;;;;;;;;;;;;0EAIlE,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAiC;;;;;;kFAC/C,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAI;oFAAe,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB;;;;;;;0FACnE,6LAAC;;oFAAI;oFAAe,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;kFAErE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAO,WAAU;0FAAwD;;;;;;0FAG1E,6LAAC;gFAAO,WAAU;0FAA0D;;;;;;;;;;;;;;;;;;;;;;;;;+CAhE1E,OAAO,EAAE;;;;;;;;;;;;;;;;4BA4E1B,cAAc,+BACb,6LAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAC,wBACxB,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAuC,QAAQ,KAAK;;;;;;0EAClE,6LAAC;gEAAE,WAAU;;oEAAiB,QAAQ,aAAa;oEAAC;oEAAI,QAAQ,UAAU;;;;;;;;;;;;;kEAE5E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,AAAC,8CAAgF,OAAnC,iBAAiB,QAAQ,QAAQ;0EAC7F,QAAQ,QAAQ;;;;;;0EAEnB,6LAAC;gEAAK,WAAW,AAAC,8CAIjB,OAHC,QAAQ,MAAM,KAAK,cAAc,gCACjC,QAAQ,MAAM,KAAK,gBAAgB,8BACnC;0EAEC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAAwB;4DAC1B,IAAI,KAAK,QAAQ,aAAa,EAAE,kBAAkB;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAO,WAAU;0EAAwD;;;;;;0EAG1E,6LAAC;gEAAO,WAAU;0EAA0D;;;;;;;;;;;;;;;;;;;uCA5BxE,QAAQ,EAAE;;;;;;;;;;4BAsCzB,cAAc,gCACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GApWwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 2773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/ProfilePanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  Briefcase,\n  Globe,\n  Camera,\n  Edit,\n  Save,\n  X,\n  Check,\n  AlertTriangle,\n  Shield,\n  Wallet,\n  Award,\n  TrendingUp,\n  Building2,\n  Coins,\n  DollarSign\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface UserProfile {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  location: string;\n  bio: string;\n  website: string;\n  joinDate: string;\n  avatar: string;\n  roles: string[];\n  verified: boolean;\n  kycStatus: 'pending' | 'verified' | 'rejected';\n}\n\ninterface UserStats {\n  totalInvestment: number;\n  portfolioValue: number;\n  monthlyIncome: number;\n  propertiesOwned: number;\n  totalReturns: number;\n  rewardPoints: number;\n  membershipLevel: string;\n  joinedCommunities: number;\n}\n\nexport default function ProfilePanel() {\n  const { user } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n\n  const [profile, setProfile] = useState<UserProfile>({\n    id: user?.id || '1',\n    name: user?.name || 'John Doe',\n    email: user?.email || '<EMAIL>',\n    phone: '+1 (*************',\n    location: 'New York, NY',\n    bio: 'Real estate investor passionate about blockchain technology and decentralized finance.',\n    website: 'https://johndoe.com',\n    joinDate: '2023-01-15',\n    avatar: '/api/placeholder/150/150',\n    roles: user?.roles || ['homeowner'],\n    verified: true,\n    kycStatus: 'verified',\n  });\n\n  const [editedProfile, setEditedProfile] = useState(profile);\n\n  const userStats: UserStats = {\n    totalInvestment: 850000,\n    portfolioValue: 1200000,\n    monthlyIncome: 8500,\n    propertiesOwned: 3,\n    totalReturns: 350000,\n    rewardPoints: 12500,\n    membershipLevel: 'Gold',\n    joinedCommunities: 5,\n  };\n\n  const handleSave = async () => {\n    setIsSaving(true);\n    setSaveMessage(null);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setProfile(editedProfile);\n      setIsEditing(false);\n      setSaveMessage({ type: 'success', text: 'Profile updated successfully!' });\n    } catch (error: any) {\n      setSaveMessage({ type: 'error', text: error.message || 'Failed to update profile' });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setEditedProfile(profile);\n    setIsEditing(false);\n    setSaveMessage(null);\n  };\n\n  const getRoleDisplayName = (role: string) => {\n    const roleMap: { [key: string]: string } = {\n      'homeowner': 'Property Owner',\n      'renter': 'Renter',\n      'buyer': 'Investor',\n      'portfolio-manager': 'Portfolio Manager',\n      'community-member': 'Community Member',\n    };\n    return roleMap[role] || role;\n  };\n\n  const getKycStatusColor = (status: string) => {\n    switch (status) {\n      case 'verified': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'rejected': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Save Message */}\n      {saveMessage && (\n        <div className={`p-4 rounded-lg flex items-center ${\n          saveMessage.type === 'success' \n            ? 'bg-green-50 border border-green-200 text-green-700' \n            : 'bg-red-50 border border-red-200 text-red-700'\n        }`}>\n          {saveMessage.type === 'success' ? (\n            <Check className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n          ) : (\n            <AlertTriangle className=\"w-5 h-5 mr-3 flex-shrink-0\" />\n          )}\n          <p>{saveMessage.text}</p>\n          <button\n            onClick={() => setSaveMessage(null)}\n            className=\"ml-auto text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        </div>\n      )}\n\n      <div className=\"grid lg:grid-cols-3 gap-6\">\n        {/* Profile Information */}\n        <div className=\"lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"p-6 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">Profile Information</h2>\n              {!isEditing ? (\n                <button\n                  onClick={() => setIsEditing(true)}\n                  className=\"text-blue-600 hover:text-blue-800 font-medium flex items-center\"\n                >\n                  <Edit className=\"w-4 h-4 mr-1\" />\n                  Edit Profile\n                </button>\n              ) : (\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={handleCancel}\n                    className=\"text-gray-600 hover:text-gray-800 font-medium flex items-center\"\n                  >\n                    <X className=\"w-4 h-4 mr-1\" />\n                    Cancel\n                  </button>\n                  <button\n                    onClick={handleSave}\n                    disabled={isSaving}\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center\"\n                  >\n                    {isSaving ? (\n                      <>\n                        <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                        Saving...\n                      </>\n                    ) : (\n                      <>\n                        <Save className=\"w-4 h-4 mr-1\" />\n                        Save\n                      </>\n                    )}\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"p-6\">\n            {/* Avatar and Basic Info */}\n            <div className=\"flex items-start space-x-6 mb-8\">\n              <div className=\"relative\">\n                <img\n                  src={profile.avatar}\n                  alt={profile.name}\n                  className=\"w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg\"\n                />\n                {isEditing && (\n                  <button className=\"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors\">\n                    <Camera className=\"w-4 h-4\" />\n                  </button>\n                )}\n              </div>\n              \n              <div className=\"flex-1\">\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  {isEditing ? (\n                    <input\n                      type=\"text\"\n                      value={editedProfile.name}\n                      onChange={(e) => setEditedProfile(prev => ({ ...prev, name: e.target.value }))}\n                      className=\"text-2xl font-bold text-gray-900 border-b-2 border-blue-500 bg-transparent focus:outline-none\"\n                    />\n                  ) : (\n                    <h1 className=\"text-2xl font-bold text-gray-900\">{profile.name}</h1>\n                  )}\n                  \n                  {profile.verified && (\n                    <div className=\"flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                      <Shield className=\"w-3 h-3 mr-1\" />\n                      Verified\n                    </div>\n                  )}\n                  \n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getKycStatusColor(profile.kycStatus)}`}>\n                    KYC {profile.kycStatus}\n                  </span>\n                </div>\n                \n                <div className=\"flex flex-wrap gap-2 mb-3\">\n                  {profile.roles.map((role) => (\n                    <span key={role} className=\"bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium\">\n                      {getRoleDisplayName(role)}\n                    </span>\n                  ))}\n                </div>\n                \n                <p className=\"text-gray-600\">Member since {new Date(profile.joinDate).toLocaleDateString()}</p>\n              </div>\n            </div>\n\n            {/* Contact Information */}\n            <div className=\"grid md:grid-cols-2 gap-6 mb-8\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email</label>\n                {isEditing ? (\n                  <input\n                    type=\"email\"\n                    value={editedProfile.email}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, email: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <Mail className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    {profile.email}\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Phone</label>\n                {isEditing ? (\n                  <input\n                    type=\"tel\"\n                    value={editedProfile.phone}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, phone: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <Phone className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    {profile.phone}\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Location</label>\n                {isEditing ? (\n                  <input\n                    type=\"text\"\n                    value={editedProfile.location}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, location: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <MapPin className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    {profile.location}\n                  </div>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Website</label>\n                {isEditing ? (\n                  <input\n                    type=\"url\"\n                    value={editedProfile.website}\n                    onChange={(e) => setEditedProfile(prev => ({ ...prev, website: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                ) : (\n                  <div className=\"flex items-center text-gray-900\">\n                    <Globe className=\"w-4 h-4 mr-2 text-gray-400\" />\n                    <a href={profile.website} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 hover:text-blue-800\">\n                      {profile.website}\n                    </a>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Bio */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Bio</label>\n              {isEditing ? (\n                <textarea\n                  value={editedProfile.bio}\n                  onChange={(e) => setEditedProfile(prev => ({ ...prev, bio: e.target.value }))}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              ) : (\n                <p className=\"text-gray-700\">{profile.bio}</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Stats and Achievements */}\n        <div className=\"space-y-6\">\n          {/* Portfolio Stats */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Portfolio Overview</h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <DollarSign className=\"w-5 h-5 text-green-600 mr-2\" />\n                  <span className=\"text-gray-600\">Total Value</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">${userStats.portfolioValue.toLocaleString()}</span>\n              </div>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <TrendingUp className=\"w-5 h-5 text-blue-600 mr-2\" />\n                  <span className=\"text-gray-600\">Monthly Income</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">${userStats.monthlyIncome.toLocaleString()}</span>\n              </div>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <Building2 className=\"w-5 h-5 text-purple-600 mr-2\" />\n                  <span className=\"text-gray-600\">Properties</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{userStats.propertiesOwned}</span>\n              </div>\n              \n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <Coins className=\"w-5 h-5 text-orange-600 mr-2\" />\n                  <span className=\"text-gray-600\">Reward Points</span>\n                </div>\n                <span className=\"font-semibold text-gray-900\">{userStats.rewardPoints.toLocaleString()}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Membership Level */}\n          <div className=\"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl shadow-sm p-6 text-white\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Membership Level</h3>\n                <p className=\"text-2xl font-bold\">{userStats.membershipLevel}</p>\n              </div>\n              <Award className=\"w-12 h-12 opacity-80\" />\n            </div>\n            <div className=\"mt-4\">\n              <div className=\"flex justify-between text-sm mb-1\">\n                <span>Progress to Platinum</span>\n                <span>75%</span>\n              </div>\n              <div className=\"w-full bg-white bg-opacity-30 rounded-full h-2\">\n                <div className=\"bg-white h-2 rounded-full\" style={{ width: '75%' }}></div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n            <div className=\"space-y-3\">\n              <button className=\"w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Wallet className=\"w-5 h-5 text-blue-600 mr-3\" />\n                  <span className=\"font-medium\">Connect Wallet</span>\n                </div>\n              </button>\n              \n              <button className=\"w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Shield className=\"w-5 h-5 text-green-600 mr-3\" />\n                  <span className=\"font-medium\">Security Settings</span>\n                </div>\n              </button>\n              \n              <button className=\"w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n                <div className=\"flex items-center\">\n                  <Award className=\"w-5 h-5 text-purple-600 mr-3\" />\n                  <span className=\"font-medium\">View Achievements</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;;;AAzBA;;;;AAqDe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IAEnG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,IAAI,CAAA,iBAAA,2BAAA,KAAM,EAAE,KAAI;QAChB,MAAM,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI;QACpB,OAAO,CAAA,iBAAA,2BAAA,KAAM,KAAK,KAAI;QACtB,OAAO;QACP,UAAU;QACV,KAAK;QACL,SAAS;QACT,UAAU;QACV,QAAQ;QACR,OAAO,CAAA,iBAAA,2BAAA,KAAM,KAAK,KAAI;YAAC;SAAY;QACnC,UAAU;QACV,WAAW;IACb;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,YAAuB;QAC3B,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,eAAe;QAEf,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,WAAW;YACX,aAAa;YACb,eAAe;gBAAE,MAAM;gBAAW,MAAM;YAAgC;QAC1E,EAAE,OAAO,OAAY;YACnB,eAAe;gBAAE,MAAM;gBAAS,MAAM,MAAM,OAAO,IAAI;YAA2B;QACpF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,iBAAiB;QACjB,aAAa;QACb,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,UAAqC;YACzC,aAAa;YACb,UAAU;YACV,SAAS;YACT,qBAAqB;YACrB,oBAAoB;QACtB;QACA,OAAO,OAAO,CAAC,KAAK,IAAI;IAC1B;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBAAI,WAAW,AAAC,oCAIhB,OAHC,YAAY,IAAI,KAAK,YACjB,uDACA;;oBAEH,YAAY,IAAI,KAAK,0BACpB,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;6CAEjB,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCAE3B,6LAAC;kCAAG,YAAY,IAAI;;;;;;kCACpB,6LAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;wCACnD,CAAC,0BACA,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAU;;8DAEV,6LAAC,8MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;iEAInC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,+LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGhC,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,yBACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;4DAA0F;;qFAI3G;;0EACE,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAU/C,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,KAAK,QAAQ,MAAM;wDACnB,KAAK,QAAQ,IAAI;wDACjB,WAAU;;;;;;oDAEX,2BACC,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,0BACC,6LAAC;gEACC,MAAK;gEACL,OAAO,cAAc,IAAI;gEACzB,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC5E,WAAU;;;;;qFAGZ,6LAAC;gEAAG,WAAU;0EAAoC,QAAQ,IAAI;;;;;;4DAG/D,QAAQ,QAAQ,kBACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAKvC,6LAAC;gEAAK,WAAW,AAAC,8CAAkF,OAArC,kBAAkB,QAAQ,SAAS;;oEAAK;oEAChG,QAAQ,SAAS;;;;;;;;;;;;;kEAI1B,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;gEAAgB,WAAU;0EACxB,mBAAmB;+DADX;;;;;;;;;;kEAMf,6LAAC;wDAAE,WAAU;;4DAAgB;4DAAc,IAAI,KAAK,QAAQ,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;kDAK5F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,6LAAC;wDACC,MAAK;wDACL,OAAO,cAAc,KAAK;wDAC1B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC7E,WAAU;;;;;6EAGZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,QAAQ,KAAK;;;;;;;;;;;;;0DAKpB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,6LAAC;wDACC,MAAK;wDACL,OAAO,cAAc,KAAK;wDAC1B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC7E,WAAU;;;;;6EAGZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,QAAQ,KAAK;;;;;;;;;;;;;0DAKpB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,6LAAC;wDACC,MAAK;wDACL,OAAO,cAAc,QAAQ;wDAC7B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAChF,WAAU;;;;;6EAGZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,QAAQ,QAAQ;;;;;;;;;;;;;0DAKvB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;oDAC/D,0BACC,6LAAC;wDACC,MAAK;wDACL,OAAO,cAAc,OAAO;wDAC5B,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC/E,WAAU;;;;;6EAGZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAE,MAAM,QAAQ,OAAO;gEAAE,QAAO;gEAAS,KAAI;gEAAsB,WAAU;0EAC3E,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;4CAC/D,0BACC,6LAAC;gDACC,OAAO,cAAc,GAAG;gDACxB,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC3E,MAAM;gDACN,WAAU;;;;;qEAGZ,6LAAC;gDAAE,WAAU;0DAAiB,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAOjD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,6LAAC;wDAAK,WAAU;;4DAA8B;4DAAE,UAAU,cAAc,CAAC,cAAc;;;;;;;;;;;;;0DAGzF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,6LAAC;wDAAK,WAAU;;4DAA8B;4DAAE,UAAU,aAAa,CAAC,cAAc;;;;;;;;;;;;;0DAGxF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,6LAAC;wDAAK,WAAU;kEAA+B,UAAU,eAAe;;;;;;;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,6LAAC;wDAAK,WAAU;kEAA+B,UAAU,YAAY,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0CAM1F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAE,WAAU;kEAAsB,UAAU,eAAe;;;;;;;;;;;;0DAE9D,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAA4B,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;0DAIlC,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;0DAIlC,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GA3XwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 3871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/NotificationsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  <PERSON>,\n  <PERSON>O<PERSON>,\n  Check,\n  X,\n  Trash2,\n  Filter,\n  DollarSign,\n  Home,\n  Users,\n  AlertTriangle,\n  Info,\n  CheckCircle,\n  Calendar,\n  Settings,\n  Gift\n} from 'lucide-react';\n\ninterface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: 'payment' | 'maintenance' | 'community' | 'system' | 'reward' | 'alert';\n  priority: 'low' | 'medium' | 'high';\n  read: boolean;\n  timestamp: string;\n  actionUrl?: string;\n  actionText?: string;\n}\n\nexport default function NotificationsPanel() {\n  const [filter, setFilter] = useState<'all' | 'unread' | 'payment' | 'maintenance' | 'community' | 'system' | 'reward' | 'alert'>('all');\n  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);\n\n  // Mock notifications data\n  const [notifications, setNotifications] = useState<Notification[]>([\n    {\n      id: '1',\n      title: 'Rent Payment Due',\n      message: 'Your rent payment of $2,800 is due on February 1st, 2024.',\n      type: 'payment',\n      priority: 'high',\n      read: false,\n      timestamp: '2024-01-25T10:30:00Z',\n      actionUrl: '/dashboard?tab=payments',\n      actionText: 'Pay Now',\n    },\n    {\n      id: '2',\n      title: '$MLIFE Rewards Earned',\n      message: 'You earned 150 $MLIFE tokens for timely rent payment!',\n      type: 'reward',\n      priority: 'medium',\n      read: false,\n      timestamp: '2024-01-24T14:15:00Z',\n      actionUrl: '/dashboard?tab=rewards',\n      actionText: 'View Rewards',\n    },\n    {\n      id: '3',\n      title: 'Maintenance Request Update',\n      message: 'Your kitchen faucet repair has been scheduled for January 30th.',\n      type: 'maintenance',\n      priority: 'medium',\n      read: true,\n      timestamp: '2024-01-23T09:45:00Z',\n      actionUrl: '/dashboard?tab=maintenance',\n      actionText: 'View Details',\n    },\n    {\n      id: '4',\n      title: 'Community Event',\n      message: 'Join our virtual real estate investment webinar this Friday at 2 PM.',\n      type: 'community',\n      priority: 'low',\n      read: true,\n      timestamp: '2024-01-22T16:20:00Z',\n      actionUrl: '/dashboard?tab=community',\n      actionText: 'Learn More',\n    },\n    {\n      id: '5',\n      title: 'System Maintenance',\n      message: 'Scheduled maintenance will occur on January 28th from 2-4 AM EST.',\n      type: 'system',\n      priority: 'medium',\n      read: false,\n      timestamp: '2024-01-21T11:00:00Z',\n    },\n    {\n      id: '6',\n      title: 'Security Alert',\n      message: 'New login detected from a different device. If this wasn\\'t you, please secure your account.',\n      type: 'alert',\n      priority: 'high',\n      read: true,\n      timestamp: '2024-01-20T08:30:00Z',\n      actionUrl: '/dashboard?tab=settings',\n      actionText: 'Review Security',\n    },\n  ]);\n\n  const filteredNotifications = notifications.filter(notification => {\n    if (filter === 'all') return true;\n    if (filter === 'unread') return !notification.read;\n    return notification.type === filter;\n  });\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'payment':\n        return <DollarSign className=\"w-5 h-5 text-green-600\" />;\n      case 'maintenance':\n        return <Home className=\"w-5 h-5 text-orange-600\" />;\n      case 'community':\n        return <Users className=\"w-5 h-5 text-blue-600\" />;\n      case 'system':\n        return <Settings className=\"w-5 h-5 text-gray-600\" />;\n      case 'reward':\n        return <Gift className=\"w-5 h-5 text-purple-600\" />;\n      case 'alert':\n        return <AlertTriangle className=\"w-5 h-5 text-red-600\" />;\n      default:\n        return <Info className=\"w-5 h-5 text-gray-600\" />;\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'border-l-red-500';\n      case 'medium':\n        return 'border-l-yellow-500';\n      case 'low':\n        return 'border-l-green-500';\n      default:\n        return 'border-l-gray-300';\n    }\n  };\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id ? { ...notification, read: true } : notification\n      )\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n  };\n\n  const deleteNotification = (id: string) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  };\n\n  const deleteSelected = () => {\n    setNotifications(prev => \n      prev.filter(notification => !selectedNotifications.includes(notification.id))\n    );\n    setSelectedNotifications([]);\n  };\n\n  const toggleSelection = (id: string) => {\n    setSelectedNotifications(prev =>\n      prev.includes(id)\n        ? prev.filter(notificationId => notificationId !== id)\n        : [...prev, id]\n    );\n  };\n\n  const selectAll = () => {\n    setSelectedNotifications(filteredNotifications.map(n => n.id));\n  };\n\n  const clearSelection = () => {\n    setSelectedNotifications([]);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Notifications</h2>\n          {unreadCount > 0 && (\n            <span className=\"bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded-full\">\n              {unreadCount} unread\n            </span>\n          )}\n        </div>\n        {unreadCount > 0 && (\n          <button\n            onClick={markAllAsRead}\n            className=\"text-blue-600 hover:text-blue-700 font-medium text-sm\"\n          >\n            Mark all as read\n          </button>\n        )}\n      </div>\n\n      {/* Filters and Actions */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex flex-wrap gap-2\">\n            {[\n              { id: 'all', label: 'All', count: notifications.length },\n              { id: 'unread', label: 'Unread', count: unreadCount },\n              { id: 'payment', label: 'Payments', count: notifications.filter(n => n.type === 'payment').length },\n              { id: 'maintenance', label: 'Maintenance', count: notifications.filter(n => n.type === 'maintenance').length },\n              { id: 'community', label: 'Community', count: notifications.filter(n => n.type === 'community').length },\n              { id: 'reward', label: 'Rewards', count: notifications.filter(n => n.type === 'reward').length },\n            ].map((filterOption) => (\n              <button\n                key={filterOption.id}\n                onClick={() => setFilter(filterOption.id as any)}\n                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors ${\n                  filter === filterOption.id\n                    ? 'bg-blue-100 text-blue-700'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                }`}\n              >\n                {filterOption.label} ({filterOption.count})\n              </button>\n            ))}\n          </div>\n\n          {selectedNotifications.length > 0 && (\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600\">\n                {selectedNotifications.length} selected\n              </span>\n              <button\n                onClick={deleteSelected}\n                className=\"text-red-600 hover:text-red-700 p-1\"\n              >\n                <Trash2 className=\"w-4 h-4\" />\n              </button>\n              <button\n                onClick={clearSelection}\n                className=\"text-gray-600 hover:text-gray-700 p-1\"\n              >\n                <X className=\"w-4 h-4\" />\n              </button>\n            </div>\n          )}\n        </div>\n\n        {filteredNotifications.length > 0 && (\n          <div className=\"mt-4 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={selectedNotifications.length === filteredNotifications.length ? clearSelection : selectAll}\n                className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\"\n              >\n                {selectedNotifications.length === filteredNotifications.length ? 'Deselect All' : 'Select All'}\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Notifications List */}\n      <div className=\"space-y-3\">\n        {filteredNotifications.length === 0 ? (\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n            <Bell className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No notifications</h3>\n            <p className=\"text-gray-600\">\n              {filter === 'unread' \n                ? \"You're all caught up! No unread notifications.\"\n                : `No ${filter === 'all' ? '' : filter} notifications found.`\n              }\n            </p>\n          </div>\n        ) : (\n          filteredNotifications.map((notification) => (\n            <div\n              key={notification.id}\n              className={`bg-white rounded-xl shadow-sm border border-gray-200 border-l-4 ${getPriorityColor(notification.priority)} p-6 hover:shadow-md transition-shadow ${\n                !notification.read ? 'bg-blue-50' : ''\n              }`}\n            >\n              <div className=\"flex items-start space-x-4\">\n                <input\n                  type=\"checkbox\"\n                  checked={selectedNotifications.includes(notification.id)}\n                  onChange={() => toggleSelection(notification.id)}\n                  className=\"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                \n                <div className=\"flex-shrink-0 mt-1\">\n                  {getNotificationIcon(notification.type)}\n                </div>\n\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <h3 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>\n                        {notification.title}\n                        {!notification.read && (\n                          <span className=\"ml-2 w-2 h-2 bg-blue-600 rounded-full inline-block\"></span>\n                        )}\n                      </h3>\n                      <p className=\"mt-1 text-sm text-gray-600\">{notification.message}</p>\n                      <div className=\"mt-2 flex items-center space-x-4 text-xs text-gray-500\">\n                        <span>{new Date(notification.timestamp).toLocaleString()}</span>\n                        <span className=\"capitalize\">{notification.type}</span>\n                        <span className=\"capitalize\">{notification.priority} priority</span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center space-x-2 ml-4\">\n                      {notification.actionUrl && (\n                        <button\n                          onClick={() => {\n                            // Handle navigation to specific tab\n                            const url = new URL(notification.actionUrl!, window.location.origin);\n                            const tab = url.searchParams.get('tab');\n                            if (tab) {\n                              // This would trigger tab change in parent component\n                              window.dispatchEvent(new CustomEvent('changeTab', { detail: tab }));\n                            }\n                          }}\n                          className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n                        >\n                          {notification.actionText || 'View'}\n                        </button>\n                      )}\n                      \n                      {!notification.read && (\n                        <button\n                          onClick={() => markAsRead(notification.id)}\n                          className=\"text-gray-400 hover:text-blue-600 p-1\"\n                          title=\"Mark as read\"\n                        >\n                          <Check className=\"w-4 h-4\" />\n                        </button>\n                      )}\n                      \n                      <button\n                        onClick={() => deleteNotification(notification.id)}\n                        className=\"text-gray-400 hover:text-red-600 p-1\"\n                        title=\"Delete notification\"\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiCe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8F;IACjI,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/E,0BAA0B;IAC1B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjE;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YAC<PERSON>,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;QACd;KACD;IAED,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,OAAO,OAAO;QAC7B,IAAI,WAAW,UAAU,OAAO,CAAC,aAAa,IAAI;QAClD,OAAO,aAAa,IAAI,KAAK;IAC/B;IAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,sMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAAK;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAAI;IAGjE;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;IAE7D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAC3E;IAEA,MAAM,iBAAiB;QACrB,iBAAiB,CAAA,OACf,KAAK,MAAM,CAAC,CAAA,eAAgB,CAAC,sBAAsB,QAAQ,CAAC,aAAa,EAAE;QAE7E,yBAAyB,EAAE;IAC7B;IAEA,MAAM,kBAAkB,CAAC;QACvB,yBAAyB,CAAA,OACvB,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,iBAAkB,mBAAmB,MACjD;mBAAI;gBAAM;aAAG;IAErB;IAEA,MAAM,YAAY;QAChB,yBAAyB,sBAAsB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAC9D;IAEA,MAAM,iBAAiB;QACrB,yBAAyB,EAAE;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,cAAc,mBACb,6LAAC;gCAAK,WAAU;;oCACb;oCAAY;;;;;;;;;;;;;oBAIlB,cAAc,mBACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,IAAI;wCAAO,OAAO;wCAAO,OAAO,cAAc,MAAM;oCAAC;oCACvD;wCAAE,IAAI;wCAAU,OAAO;wCAAU,OAAO;oCAAY;oCACpD;wCAAE,IAAI;wCAAW,OAAO;wCAAY,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;oCAAC;oCAClG;wCAAE,IAAI;wCAAe,OAAO;wCAAe,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,eAAe,MAAM;oCAAC;oCAC7G;wCAAE,IAAI;wCAAa,OAAO;wCAAa,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;oCAAC;oCACvG;wCAAE,IAAI;wCAAU,OAAO;wCAAW,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;oCAAC;iCAChG,CAAC,GAAG,CAAC,CAAC,6BACL,6LAAC;wCAEC,SAAS,IAAM,UAAU,aAAa,EAAE;wCACxC,WAAW,AAAC,gEAIX,OAHC,WAAW,aAAa,EAAE,GACtB,8BACA;;4CAGL,aAAa,KAAK;4CAAC;4CAAG,aAAa,KAAK;4CAAC;;uCARrC,aAAa,EAAE;;;;;;;;;;4BAazB,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CACb,sBAAsB,MAAM;4CAAC;;;;;;;kDAEhC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMpB,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,sBAAsB,MAAM,KAAK,sBAAsB,MAAM,GAAG,iBAAiB;gCAC1F,WAAU;0CAET,sBAAsB,MAAM,KAAK,sBAAsB,MAAM,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;;;;0BAQ5F,6LAAC;gBAAI,WAAU;0BACZ,sBAAsB,MAAM,KAAK,kBAChC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCACV,WAAW,WACR,mDACA,AAAC,MAAoC,OAA/B,WAAW,QAAQ,KAAK,QAAO;;;;;;;;;;;2BAK7C,sBAAsB,GAAG,CAAC,CAAC,6BACzB,6LAAC;wBAEC,WAAW,AAAC,mEACV,OAD4E,iBAAiB,aAAa,QAAQ,GAAE,2CAErH,OADC,CAAC,aAAa,IAAI,GAAG,eAAe;kCAGtC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,sBAAsB,QAAQ,CAAC,aAAa,EAAE;oCACvD,UAAU,IAAM,gBAAgB,aAAa,EAAE;oCAC/C,WAAU;;;;;;8CAGZ,6LAAC;oCAAI,WAAU;8CACZ,oBAAoB,aAAa,IAAI;;;;;;8CAGxC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAW,AAAC,uBAA6E,OAAvD,CAAC,aAAa,IAAI,GAAG,kBAAkB;;4DAC1E,aAAa,KAAK;4DAClB,CAAC,aAAa,IAAI,kBACjB,6LAAC;gEAAK,WAAU;;;;;;;;;;;;kEAGpB,6LAAC;wDAAE,WAAU;kEAA8B,aAAa,OAAO;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;0EACtD,6LAAC;gEAAK,WAAU;0EAAc,aAAa,IAAI;;;;;;0EAC/C,6LAAC;gEAAK,WAAU;;oEAAc,aAAa,QAAQ;oEAAC;;;;;;;;;;;;;;;;;;;0DAIxD,6LAAC;gDAAI,WAAU;;oDACZ,aAAa,SAAS,kBACrB,6LAAC;wDACC,SAAS;4DACP,oCAAoC;4DACpC,MAAM,MAAM,IAAI,IAAI,aAAa,SAAS,EAAG,OAAO,QAAQ,CAAC,MAAM;4DACnE,MAAM,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC;4DACjC,IAAI,KAAK;gEACP,oDAAoD;gEACpD,OAAO,aAAa,CAAC,IAAI,YAAY,aAAa;oEAAE,QAAQ;gEAAI;4DAClE;wDACF;wDACA,WAAU;kEAET,aAAa,UAAU,IAAI;;;;;;oDAI/B,CAAC,aAAa,IAAI,kBACjB,6LAAC;wDACC,SAAS,IAAM,WAAW,aAAa,EAAE;wDACzC,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAIrB,6LAAC;wDACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;wDACjD,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAnEvB,aAAa,EAAE;;;;;;;;;;;;;;;;AA+ElC;GA3UwB;KAAA", "debugId": null}}, {"offset": {"line": 4501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/PropertiesPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Building2,\n  MapPin,\n  DollarSign,\n  Users,\n  Calendar,\n  TrendingUp,\n  Eye,\n  Edit,\n  Plus,\n  Search,\n  Filter,\n  MoreVertical,\n  Home,\n  Bed,\n  Bath,\n  Square,\n  Star,\n  AlertCircle,\n  CheckCircle\n} from 'lucide-react';\n\ninterface Property {\n  id: string;\n  title: string;\n  address: string;\n  type: 'apartment' | 'house' | 'condo' | 'townhouse';\n  bedrooms: number;\n  bathrooms: number;\n  sqft: number;\n  monthlyRent: number;\n  currentTenant?: string;\n  tenantSince?: string;\n  occupancyStatus: 'occupied' | 'vacant' | 'maintenance';\n  monthlyIncome: number;\n  totalValue: number;\n  roi: number;\n  imageUrl: string;\n  lastUpdated: string;\n}\n\nexport default function PropertiesPanel() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<'all' | 'apartment' | 'house' | 'condo' | 'townhouse'>('all');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'occupied' | 'vacant' | 'maintenance'>('all');\n\n  // Mock properties data\n  const [properties] = useState<Property[]>([\n    {\n      id: '1',\n      title: 'Modern Downtown Apartment',\n      address: '123 Main St, Downtown, NY 10001',\n      type: 'apartment',\n      bedrooms: 2,\n      bathrooms: 2,\n      sqft: 1200,\n      monthlyRent: 2800,\n      currentTenant: 'John Smith',\n      tenantSince: '2023-06-01',\n      occupancyStatus: 'occupied',\n      monthlyIncome: 2800,\n      totalValue: 450000,\n      roi: 7.5,\n      imageUrl: '/api/placeholder/300/200',\n      lastUpdated: '2024-01-20T10:30:00Z',\n    },\n    {\n      id: '2',\n      title: 'Suburban Family House',\n      address: '456 Oak Ave, Suburbia, NY 10002',\n      type: 'house',\n      bedrooms: 4,\n      bathrooms: 3,\n      sqft: 2400,\n      monthlyRent: 3500,\n      occupancyStatus: 'vacant',\n      monthlyIncome: 0,\n      totalValue: 650000,\n      roi: 6.5,\n      imageUrl: '/api/placeholder/300/200',\n      lastUpdated: '2024-01-18T14:15:00Z',\n    },\n    {\n      id: '3',\n      title: 'Luxury Condo with View',\n      address: '789 Park Blvd, Uptown, NY 10003',\n      type: 'condo',\n      bedrooms: 3,\n      bathrooms: 2,\n      sqft: 1800,\n      monthlyRent: 4200,\n      currentTenant: 'Sarah Johnson',\n      tenantSince: '2023-09-15',\n      occupancyStatus: 'occupied',\n      monthlyIncome: 4200,\n      totalValue: 750000,\n      roi: 6.7,\n      imageUrl: '/api/placeholder/300/200',\n      lastUpdated: '2024-01-22T09:45:00Z',\n    },\n    {\n      id: '4',\n      title: 'Cozy Townhouse',\n      address: '321 Elm St, Midtown, NY 10004',\n      type: 'townhouse',\n      bedrooms: 3,\n      bathrooms: 2,\n      sqft: 1600,\n      monthlyRent: 3200,\n      occupancyStatus: 'maintenance',\n      monthlyIncome: 0,\n      totalValue: 520000,\n      roi: 7.4,\n      imageUrl: '/api/placeholder/300/200',\n      lastUpdated: '2024-01-19T16:20:00Z',\n    },\n  ]);\n\n  const filteredProperties = properties.filter(property => {\n    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         property.address.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || property.type === filterType;\n    const matchesStatus = filterStatus === 'all' || property.occupancyStatus === filterStatus;\n    \n    return matchesSearch && matchesType && matchesStatus;\n  });\n\n  const totalProperties = properties.length;\n  const occupiedProperties = properties.filter(p => p.occupancyStatus === 'occupied').length;\n  const vacantProperties = properties.filter(p => p.occupancyStatus === 'vacant').length;\n  const totalMonthlyIncome = properties.reduce((sum, p) => sum + p.monthlyIncome, 0);\n  const totalValue = properties.reduce((sum, p) => sum + p.totalValue, 0);\n  const averageROI = properties.reduce((sum, p) => sum + p.roi, 0) / properties.length;\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'occupied':\n        return 'bg-green-100 text-green-800';\n      case 'vacant':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'maintenance':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'occupied':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      case 'vacant':\n        return <Home className=\"w-4 h-4\" />;\n      case 'maintenance':\n        return <AlertCircle className=\"w-4 h-4\" />;\n      default:\n        return <Home className=\"w-4 h-4\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">My Properties</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Add Property\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Building2 className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Properties</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalProperties}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Monthly Income</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${totalMonthlyIncome.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Average ROI</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{averageROI.toFixed(1)}%</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <Users className=\"w-6 h-6 text-orange-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Occupancy Rate</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{Math.round((occupiedProperties / totalProperties) * 100)}%</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex flex-col sm:flex-row gap-4 flex-1\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search properties...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            \n            <select\n              value={filterType}\n              onChange={(e) => setFilterType(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"all\">All Types</option>\n              <option value=\"apartment\">Apartment</option>\n              <option value=\"house\">House</option>\n              <option value=\"condo\">Condo</option>\n              <option value=\"townhouse\">Townhouse</option>\n            </select>\n\n            <select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"occupied\">Occupied</option>\n              <option value=\"vacant\">Vacant</option>\n              <option value=\"maintenance\">Maintenance</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Properties Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {filteredProperties.map((property) => (\n          <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n            <div className=\"relative\">\n              <img\n                src={property.imageUrl}\n                alt={property.title}\n                className=\"w-full h-48 object-cover\"\n              />\n              <div className=\"absolute top-4 left-4\">\n                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(property.occupancyStatus)}`}>\n                  {getStatusIcon(property.occupancyStatus)}\n                  <span className=\"ml-1 capitalize\">{property.occupancyStatus}</span>\n                </span>\n              </div>\n              <div className=\"absolute top-4 right-4\">\n                <button className=\"bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all\">\n                  <MoreVertical className=\"w-4 h-4 text-gray-600\" />\n                </button>\n              </div>\n            </div>\n\n            <div className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-3\">\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">{property.title}</h3>\n                  <p className=\"text-sm text-gray-600 flex items-center\">\n                    <MapPin className=\"w-4 h-4 mr-1\" />\n                    {property.address}\n                  </p>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-lg font-bold text-gray-900\">${property.monthlyRent.toLocaleString()}/mo</p>\n                  <p className=\"text-sm text-gray-600\">ROI: {property.roi}%</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-4\">\n                <span className=\"flex items-center\">\n                  <Bed className=\"w-4 h-4 mr-1\" />\n                  {property.bedrooms} bed\n                </span>\n                <span className=\"flex items-center\">\n                  <Bath className=\"w-4 h-4 mr-1\" />\n                  {property.bathrooms} bath\n                </span>\n                <span className=\"flex items-center\">\n                  <Square className=\"w-4 h-4 mr-1\" />\n                  {property.sqft.toLocaleString()} sqft\n                </span>\n              </div>\n\n              {property.currentTenant && (\n                <div className=\"bg-gray-50 rounded-lg p-3 mb-4\">\n                  <p className=\"text-sm font-medium text-gray-900\">Current Tenant</p>\n                  <p className=\"text-sm text-gray-600\">{property.currentTenant}</p>\n                  <p className=\"text-xs text-gray-500\">Since {new Date(property.tenantSince!).toLocaleDateString()}</p>\n                </div>\n              )}\n\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                <div className=\"flex space-x-2\">\n                  <button className=\"text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors\">\n                    <Eye className=\"w-4 h-4\" />\n                  </button>\n                  <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                    <Edit className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <p className=\"text-xs text-gray-500\">\n                  Updated {new Date(property.lastUpdated).toLocaleDateString()}\n                </p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredProperties.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <Building2 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No properties found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm || filterType !== 'all' || filterStatus !== 'all'\n              ? 'Try adjusting your search or filters.'\n              : 'Get started by adding your first property.'\n            }\n          </p>\n          <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\">\n            Add Property\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA4Ce,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyD;IACpG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiD;IAEhG,uBAAuB;IACvB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACxC;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,aAAa;YACb,eAAe;YACf,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,YAAY;YACZ,KAAK;YACL,UAAU;YACV,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,YAAY;YACZ,KAAK;YACL,UAAU;YACV,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,aAAa;YACb,eAAe;YACf,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,YAAY;YACZ,KAAK;YACL,UAAU;YACV,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,YAAY;YACZ,KAAK;YACL,UAAU;YACV,aAAa;QACf;KACD;IAED,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA;QAC3C,MAAM,gBAAgB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACnF,MAAM,cAAc,eAAe,SAAS,SAAS,IAAI,KAAK;QAC9D,MAAM,gBAAgB,iBAAiB,SAAS,SAAS,eAAe,KAAK;QAE7E,OAAO,iBAAiB,eAAe;IACzC;IAEA,MAAM,kBAAkB,WAAW,MAAM;IACzC,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,YAAY,MAAM;IAC1F,MAAM,mBAAmB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,UAAU,MAAM;IACtF,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;IAChF,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE;IACrE,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,GAAG,EAAE,KAAK,WAAW,MAAM;IAEpF,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,sMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,sMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,mBAAmB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAKzF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAoC,WAAW,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAK7E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAoC,KAAK,KAAK,CAAC,AAAC,qBAAqB,kBAAmB;gDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,6LAAC;gBAAI,WAAU;0BACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC;wBAAsB,WAAU;;0CAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,SAAS,QAAQ;wCACtB,KAAK,SAAS,KAAK;wCACnB,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAW,AAAC,2EAAmH,OAAzC,eAAe,SAAS,eAAe;;gDAChI,cAAc,SAAS,eAAe;8DACvC,6LAAC;oDAAK,WAAU;8DAAmB,SAAS,eAAe;;;;;;;;;;;;;;;;;kDAG/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,6NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA4C,SAAS,KAAK;;;;;;kEACxE,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,SAAS,OAAO;;;;;;;;;;;;;0DAGrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAkC;4DAAE,SAAS,WAAW,CAAC,cAAc;4DAAG;;;;;;;kEACvF,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAM,SAAS,GAAG;4DAAC;;;;;;;;;;;;;;;;;;;kDAI5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,SAAS,QAAQ;oDAAC;;;;;;;0DAErB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,SAAS,SAAS;oDAAC;;;;;;;0DAEtB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,SAAS,IAAI,CAAC,cAAc;oDAAG;;;;;;;;;;;;;oCAInC,SAAS,aAAa,kBACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAyB,SAAS,aAAa;;;;;;0DAC5D,6LAAC;gDAAE,WAAU;;oDAAwB;oDAAO,IAAI,KAAK,SAAS,WAAW,EAAG,kBAAkB;;;;;;;;;;;;;kDAIlG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGpB,6LAAC;gDAAE,WAAU;;oDAAwB;oDAC1B,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;uBApExD,SAAS,EAAE;;;;;;;;;;YA4ExB,mBAAmB,MAAM,KAAK,mBAC7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,eAAe,SAAS,iBAAiB,QACpD,0CACA;;;;;;kCAGN,6LAAC;wBAAO,WAAU;kCAA8F;;;;;;;;;;;;;;;;;;AAO1H;GA9TwB;KAAA", "debugId": null}}, {"offset": {"line": 5462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/NFTsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Co<PERSON>,\n  Eye,\n  ExternalLink,\n  Share2,\n  Download,\n  Star,\n  TrendingUp,\n  Calendar,\n  DollarSign,\n  Filter,\n  Search,\n  Grid3X3,\n  List,\n  Plus\n} from 'lucide-react';\n\ninterface NFT {\n  id: string;\n  name: string;\n  description: string;\n  tokenId: string;\n  contractAddress: string;\n  imageUrl: string;\n  propertyAddress: string;\n  mintDate: string;\n  currentValue: number;\n  originalPrice: number;\n  ownership: number; // percentage\n  totalSupply: number;\n  attributes: {\n    trait_type: string;\n    value: string;\n  }[];\n  blockchain: 'ethereum' | 'polygon';\n  status: 'active' | 'listed' | 'sold';\n}\n\nexport default function NFTsPanel() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'listed' | 'sold'>('all');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n\n  // Mock NFTs data\n  const [nfts] = useState<NFT[]>([\n    {\n      id: '1',\n      name: 'Downtown Apartment #123',\n      description: 'Tokenized ownership of a modern 2-bedroom apartment in downtown Manhattan',\n      tokenId: '123',\n      contractAddress: '0x1234...5678',\n      imageUrl: '/api/placeholder/300/300',\n      propertyAddress: '123 Main St, Downtown, NY 10001',\n      mintDate: '2023-06-01T10:00:00Z',\n      currentValue: 45000,\n      originalPrice: 40000,\n      ownership: 10,\n      totalSupply: 100,\n      attributes: [\n        { trait_type: 'Property Type', value: 'Apartment' },\n        { trait_type: 'Bedrooms', value: '2' },\n        { trait_type: 'Bathrooms', value: '2' },\n        { trait_type: 'Square Feet', value: '1200' },\n        { trait_type: 'Location', value: 'Downtown' },\n      ],\n      blockchain: 'ethereum',\n      status: 'active',\n    },\n    {\n      id: '2',\n      name: 'Luxury Condo #789',\n      description: 'Premium condo with city view, tokenized for fractional ownership',\n      tokenId: '789',\n      contractAddress: '0x9876...5432',\n      imageUrl: '/api/placeholder/300/300',\n      propertyAddress: '789 Park Blvd, Uptown, NY 10003',\n      mintDate: '2023-09-15T14:30:00Z',\n      currentValue: 75000,\n      originalPrice: 70000,\n      ownership: 15,\n      totalSupply: 200,\n      attributes: [\n        { trait_type: 'Property Type', value: 'Condo' },\n        { trait_type: 'Bedrooms', value: '3' },\n        { trait_type: 'Bathrooms', value: '2' },\n        { trait_type: 'Square Feet', value: '1800' },\n        { trait_type: 'Location', value: 'Uptown' },\n        { trait_type: 'View', value: 'City' },\n      ],\n      blockchain: 'polygon',\n      status: 'active',\n    },\n    {\n      id: '3',\n      name: 'Suburban House #456',\n      description: 'Family house in quiet suburban neighborhood',\n      tokenId: '456',\n      contractAddress: '0x5555...7777',\n      imageUrl: '/api/placeholder/300/300',\n      propertyAddress: '456 Oak Ave, Suburbia, NY 10002',\n      mintDate: '2023-12-01T09:15:00Z',\n      currentValue: 32000,\n      originalPrice: 35000,\n      ownership: 8,\n      totalSupply: 150,\n      attributes: [\n        { trait_type: 'Property Type', value: 'House' },\n        { trait_type: 'Bedrooms', value: '4' },\n        { trait_type: 'Bathrooms', value: '3' },\n        { trait_type: 'Square Feet', value: '2400' },\n        { trait_type: 'Location', value: 'Suburban' },\n      ],\n      blockchain: 'ethereum',\n      status: 'listed',\n    },\n  ]);\n\n  const filteredNFTs = nfts.filter(nft => {\n    const matchesSearch = nft.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         nft.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || nft.status === filterStatus;\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const totalValue = nfts.reduce((sum, nft) => sum + nft.currentValue, 0);\n  const totalGainLoss = nfts.reduce((sum, nft) => sum + (nft.currentValue - nft.originalPrice), 0);\n  const totalOwnership = nfts.reduce((sum, nft) => sum + nft.ownership, 0);\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'active':\n        return 'bg-green-100 text-green-800';\n      case 'listed':\n        return 'bg-blue-100 text-blue-800';\n      case 'sold':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getBlockchainColor = (blockchain: string) => {\n    switch (blockchain) {\n      case 'ethereum':\n        return 'bg-purple-100 text-purple-800';\n      case 'polygon':\n        return 'bg-indigo-100 text-indigo-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">My NFTs</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Mint NFT\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Coins className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Portfolio Value</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${totalValue.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${\n              totalGainLoss >= 0 ? 'bg-green-100' : 'bg-red-100'\n            }`}>\n              <TrendingUp className={`w-6 h-6 ${\n                totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'\n              }`} />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Gain/Loss</p>\n              <p className={`text-2xl font-bold ${\n                totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {totalGainLoss >= 0 ? '+' : ''}${totalGainLoss.toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Star className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total NFTs</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{nfts.length}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex flex-col sm:flex-row gap-4 flex-1\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search NFTs...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            \n            <select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"active\">Active</option>\n              <option value=\"listed\">Listed</option>\n              <option value=\"sold\">Sold</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => setViewMode('grid')}\n              className={`p-2 rounded-lg transition-colors ${\n                viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'\n              }`}\n            >\n              <Grid3X3 className=\"w-4 h-4\" />\n            </button>\n            <button\n              onClick={() => setViewMode('list')}\n              className={`p-2 rounded-lg transition-colors ${\n                viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'\n              }`}\n            >\n              <List className=\"w-4 h-4\" />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* NFTs Grid/List */}\n      {viewMode === 'grid' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredNFTs.map((nft) => (\n            <div key={nft.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n              <div className=\"relative\">\n                <img\n                  src={nft.imageUrl}\n                  alt={nft.name}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"absolute top-4 left-4 flex space-x-2\">\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(nft.status)}`}>\n                    {nft.status}\n                  </span>\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBlockchainColor(nft.blockchain)}`}>\n                    {nft.blockchain}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{nft.name}</h3>\n                <p className=\"text-sm text-gray-600 mb-3\">{nft.description}</p>\n                \n                <div className=\"space-y-2 mb-4\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600\">Current Value:</span>\n                    <span className=\"font-medium\">${nft.currentValue.toLocaleString()}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600\">Ownership:</span>\n                    <span className=\"font-medium\">{nft.ownership}%</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600\">Token ID:</span>\n                    <span className=\"font-mono text-xs\">{nft.tokenId}</span>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                  <div className=\"flex space-x-2\">\n                    <button className=\"text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors\">\n                      <Eye className=\"w-4 h-4\" />\n                    </button>\n                    <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                      <ExternalLink className=\"w-4 h-4\" />\n                    </button>\n                    <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                      <Share2 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  <p className=\"text-xs text-gray-500\">\n                    Minted {new Date(nft.mintDate).toLocaleDateString()}\n                  </p>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">NFT</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Value</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Ownership</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Blockchain</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredNFTs.map((nft) => (\n                  <tr key={nft.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <img className=\"h-12 w-12 rounded-lg object-cover\" src={nft.imageUrl} alt={nft.name} />\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">{nft.name}</div>\n                          <div className=\"text-sm text-gray-500\">Token #{nft.tokenId}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">${nft.currentValue.toLocaleString()}</div>\n                      <div className={`text-sm ${\n                        nft.currentValue >= nft.originalPrice ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {nft.currentValue >= nft.originalPrice ? '+' : ''}\n                        ${(nft.currentValue - nft.originalPrice).toLocaleString()}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {nft.ownership}%\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(nft.status)}`}>\n                        {nft.status}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getBlockchainColor(nft.blockchain)}`}>\n                        {nft.blockchain}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button className=\"text-blue-600 hover:text-blue-700\">\n                          <Eye className=\"w-4 h-4\" />\n                        </button>\n                        <button className=\"text-gray-600 hover:text-gray-700\">\n                          <ExternalLink className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {filteredNFTs.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <Coins className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No NFTs found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm || filterStatus !== 'all'\n              ? 'Try adjusting your search or filters.'\n              : 'Get started by minting your first property NFT.'\n            }\n          </p>\n          <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\">\n            Mint NFT\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAyCe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,iBAAiB;IACjB,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;QAC7B;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,iBAAiB;YACjB,UAAU;YACV,iBAAiB;YACjB,UAAU;YACV,cAAc;YACd,eAAe;YACf,WAAW;YACX,aAAa;YACb,YAAY;gBACV;oBAAE,YAAY;oBAAiB,OAAO;gBAAY;gBAClD;oBAAE,YAAY;oBAAY,OAAO;gBAAI;gBACrC;oBAAE,YAAY;oBAAa,OAAO;gBAAI;gBACtC;oBAAE,YAAY;oBAAe,OAAO;gBAAO;gBAC3C;oBAAE,YAAY;oBAAY,OAAO;gBAAW;aAC7C;YACD,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,iBAAiB;YACjB,UAAU;YACV,iBAAiB;YACjB,UAAU;YACV,cAAc;YACd,eAAe;YACf,WAAW;YACX,aAAa;YACb,YAAY;gBACV;oBAAE,YAAY;oBAAiB,OAAO;gBAAQ;gBAC9C;oBAAE,YAAY;oBAAY,OAAO;gBAAI;gBACrC;oBAAE,YAAY;oBAAa,OAAO;gBAAI;gBACtC;oBAAE,YAAY;oBAAe,OAAO;gBAAO;gBAC3C;oBAAE,YAAY;oBAAY,OAAO;gBAAS;gBAC1C;oBAAE,YAAY;oBAAQ,OAAO;gBAAO;aACrC;YACD,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,iBAAiB;YACjB,UAAU;YACV,iBAAiB;YACjB,UAAU;YACV,cAAc;YACd,eAAe;YACf,WAAW;YACX,aAAa;YACb,YAAY;gBACV;oBAAE,YAAY;oBAAiB,OAAO;gBAAQ;gBAC9C;oBAAE,YAAY;oBAAY,OAAO;gBAAI;gBACrC;oBAAE,YAAY;oBAAa,OAAO;gBAAI;gBACtC;oBAAE,YAAY;oBAAe,OAAO;gBAAO;gBAC3C;oBAAE,YAAY;oBAAY,OAAO;gBAAW;aAC7C;YACD,YAAY;YACZ,QAAQ;QACV;KACD;IAED,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA;QAC/B,MAAM,gBAAgB,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,IAAI,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACtF,MAAM,gBAAgB,iBAAiB,SAAS,IAAI,MAAM,KAAK;QAE/D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,YAAY,EAAE;IACrE,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,YAAY,GAAG,IAAI,aAAa,GAAG;IAC9F,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,SAAS,EAAE;IAEtE,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAKjF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,AAAC,yDAEhB,OADC,iBAAiB,IAAI,iBAAiB;8CAEtC,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAW,AAAC,WAEvB,OADC,iBAAiB,IAAI,mBAAmB;;;;;;;;;;;8CAG5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAW,AAAC,sBAEd,OADC,iBAAiB,IAAI,mBAAmB;;gDAEvC,iBAAiB,IAAI,MAAM;gDAAG;gDAAE,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAMrE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;;;;;;;;;;;;;sCAIzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAW,AAAC,oCAEX,OADC,aAAa,SAAS,8BAA8B;8CAGtD,cAAA,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAW,AAAC,oCAEX,OADC,aAAa,SAAS,8BAA8B;8CAGtD,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOvB,aAAa,uBACZ,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC;wBAAiB,WAAU;;0CAC1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,IAAI,QAAQ;wCACjB,KAAK,IAAI,IAAI;wCACb,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,AAAC,2EAAqG,OAA3B,eAAe,IAAI,MAAM;0DAClH,IAAI,MAAM;;;;;;0DAEb,6LAAC;gDAAK,WAAW,AAAC,2EAA6G,OAAnC,mBAAmB,IAAI,UAAU;0DAC1H,IAAI,UAAU;;;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4C,IAAI,IAAI;;;;;;kDAClE,6LAAC;wCAAE,WAAU;kDAA8B,IAAI,WAAW;;;;;;kDAE1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAc;4DAAE,IAAI,YAAY,CAAC,cAAc;;;;;;;;;;;;;0DAEjE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAAe,IAAI,SAAS;4DAAC;;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAqB,IAAI,OAAO;;;;;;;;;;;;;;;;;;kDAIpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGtB,6LAAC;gDAAE,WAAU;;oDAAwB;oDAC3B,IAAI,KAAK,IAAI,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;uBAjD/C,IAAI,EAAE;;;;;;;;;qCAyDpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAC/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAC/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAC/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAC/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAC/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAGnG,6LAAC;gCAAM,WAAU;0CACd,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;4DAAoC,KAAK,IAAI,QAAQ;4DAAE,KAAK,IAAI,IAAI;;;;;;sEACnF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC,IAAI,IAAI;;;;;;8EAC5D,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAQ,IAAI,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0DAIhE,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAI,WAAU;;4DAAwB;4DAAE,IAAI,YAAY,CAAC,cAAc;;;;;;;kEACxE,6LAAC;wDAAI,WAAW,AAAC,WAEhB,OADC,IAAI,YAAY,IAAI,IAAI,aAAa,GAAG,mBAAmB;;4DAE1D,IAAI,YAAY,IAAI,IAAI,aAAa,GAAG,MAAM;4DAAG;4DAChD,CAAC,IAAI,YAAY,GAAG,IAAI,aAAa,EAAE,cAAc;;;;;;;;;;;;;0DAG3D,6LAAC;gDAAG,WAAU;;oDACX,IAAI,SAAS;oDAAC;;;;;;;0DAEjB,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,AAAC,4DAAsF,OAA3B,eAAe,IAAI,MAAM;8DACnG,IAAI,MAAM;;;;;;;;;;;0DAGf,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,AAAC,4DAA8F,OAAnC,mBAAmB,IAAI,UAAU;8DAC3G,IAAI,UAAU;;;;;;;;;;;0DAGnB,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAtCvB,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAkD1B,aAAa,MAAM,KAAK,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,iBAAiB,QAC5B,0CACA;;;;;;kCAGN,6LAAC;wBAAO,WAAU;kCAA8F;;;;;;;;;;;;;;;;;;AAO1H;GA5WwB;KAAA", "debugId": null}}, {"offset": {"line": 6566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/PaymentsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  CreditCard,\n  DollarSign,\n  Calendar,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Download,\n  Eye,\n  Plus,\n  Filter,\n  Search,\n  Wallet,\n  Building,\n  Receipt,\n  TrendingUp,\n  ArrowUpRight\n} from 'lucide-react';\n\ninterface Payment {\n  id: string;\n  type: 'rent' | 'deposit' | 'utilities' | 'maintenance' | 'late_fee';\n  amount: number;\n  dueDate: string;\n  paidDate?: string;\n  status: 'pending' | 'paid' | 'overdue' | 'partial';\n  description: string;\n  property: string;\n  paymentMethod?: string;\n  transactionId?: string;\n  lateFee?: number;\n  receipt?: string;\n}\n\nexport default function PaymentsPanel() {\n  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'paid' | 'overdue'>('all');\n  const [filterType, setFilterType] = useState<'all' | 'rent' | 'deposit' | 'utilities' | 'maintenance' | 'late_fee'>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Mock payments data\n  const [payments] = useState<Payment[]>([\n    {\n      id: '1',\n      type: 'rent',\n      amount: 2800,\n      dueDate: '2024-02-01T00:00:00Z',\n      status: 'pending',\n      description: 'Monthly Rent - February 2024',\n      property: '123 Main St, Downtown, NY 10001',\n    },\n    {\n      id: '2',\n      type: 'rent',\n      amount: 2800,\n      dueDate: '2024-01-01T00:00:00Z',\n      paidDate: '2023-12-28T10:30:00Z',\n      status: 'paid',\n      description: 'Monthly Rent - January 2024',\n      property: '123 Main St, Downtown, NY 10001',\n      paymentMethod: 'Credit Card',\n      transactionId: 'TXN-2024-001',\n      receipt: 'receipt-jan-2024.pdf',\n    },\n    {\n      id: '3',\n      type: 'utilities',\n      amount: 150,\n      dueDate: '2024-01-15T00:00:00Z',\n      paidDate: '2024-01-14T14:20:00Z',\n      status: 'paid',\n      description: 'Electricity & Gas - January 2024',\n      property: '123 Main St, Downtown, NY 10001',\n      paymentMethod: 'Bank Transfer',\n      transactionId: 'TXN-2024-002',\n    },\n    {\n      id: '4',\n      type: 'maintenance',\n      amount: 75,\n      dueDate: '2024-01-20T00:00:00Z',\n      status: 'overdue',\n      description: 'Kitchen Faucet Repair',\n      property: '123 Main St, Downtown, NY 10001',\n      lateFee: 25,\n    },\n    {\n      id: '5',\n      type: 'deposit',\n      amount: 2800,\n      dueDate: '2023-06-01T00:00:00Z',\n      paidDate: '2023-05-25T09:15:00Z',\n      status: 'paid',\n      description: 'Security Deposit',\n      property: '123 Main St, Downtown, NY 10001',\n      paymentMethod: 'Bank Transfer',\n      transactionId: 'TXN-2023-001',\n    },\n  ]);\n\n  const filteredPayments = payments.filter(payment => {\n    const matchesSearch = payment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         payment.property.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || payment.status === filterStatus;\n    const matchesType = filterType === 'all' || payment.type === filterType;\n    \n    return matchesSearch && matchesStatus && matchesType;\n  });\n\n  const totalPaid = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);\n  const totalPending = payments.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0);\n  const totalOverdue = payments.filter(p => p.status === 'overdue').reduce((sum, p) => sum + p.amount + (p.lateFee || 0), 0);\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'paid':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'overdue':\n        return 'bg-red-100 text-red-800';\n      case 'partial':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'paid':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      case 'pending':\n        return <Clock className=\"w-4 h-4\" />;\n      case 'overdue':\n        return <AlertCircle className=\"w-4 h-4\" />;\n      default:\n        return <Clock className=\"w-4 h-4\" />;\n    }\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'rent':\n        return <Building className=\"w-5 h-5 text-blue-600\" />;\n      case 'utilities':\n        return <TrendingUp className=\"w-5 h-5 text-green-600\" />;\n      case 'maintenance':\n        return <Receipt className=\"w-5 h-5 text-orange-600\" />;\n      case 'deposit':\n        return <Wallet className=\"w-5 h-5 text-purple-600\" />;\n      case 'late_fee':\n        return <AlertCircle className=\"w-5 h-5 text-red-600\" />;\n      default:\n        return <DollarSign className=\"w-5 h-5 text-gray-600\" />;\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const isOverdue = (dueDate: string, status: string) => {\n    return status !== 'paid' && new Date(dueDate) < new Date();\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Payments</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Make Payment\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CheckCircle className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Paid</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${totalPaid.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <Clock className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${totalPending.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <AlertCircle className=\"w-6 h-6 text-red-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Overdue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${totalOverdue.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex flex-col sm:flex-row gap-4 flex-1\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search payments...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            \n            <select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"pending\">Pending</option>\n              <option value=\"paid\">Paid</option>\n              <option value=\"overdue\">Overdue</option>\n            </select>\n\n            <select\n              value={filterType}\n              onChange={(e) => setFilterType(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"all\">All Types</option>\n              <option value=\"rent\">Rent</option>\n              <option value=\"utilities\">Utilities</option>\n              <option value=\"maintenance\">Maintenance</option>\n              <option value=\"deposit\">Deposit</option>\n              <option value=\"late_fee\">Late Fee</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Payments List */}\n      <div className=\"space-y-4\">\n        {filteredPayments.map((payment) => (\n          <div key={payment.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-shrink-0 mt-1\">\n                  {getTypeIcon(payment.type)}\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{payment.description}</h3>\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>\n                      {getStatusIcon(payment.status)}\n                      <span className=\"ml-1 capitalize\">{payment.status}</span>\n                    </span>\n                    {isOverdue(payment.dueDate, payment.status) && (\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                        Overdue\n                      </span>\n                    )}\n                  </div>\n                  \n                  <p className=\"text-sm text-gray-600 mb-3\">{payment.property}</p>\n                  \n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                    <div>\n                      <p className=\"text-gray-500\">Amount</p>\n                      <p className=\"font-medium\">${payment.amount.toLocaleString()}</p>\n                      {payment.lateFee && (\n                        <p className=\"text-red-600 text-xs\">+ ${payment.lateFee} late fee</p>\n                      )}\n                    </div>\n                    <div>\n                      <p className=\"text-gray-500\">Due Date</p>\n                      <p className=\"font-medium\">{formatDate(payment.dueDate)}</p>\n                    </div>\n                    {payment.paidDate && (\n                      <div>\n                        <p className=\"text-gray-500\">Paid Date</p>\n                        <p className=\"font-medium\">{formatDate(payment.paidDate)}</p>\n                      </div>\n                    )}\n                    {payment.paymentMethod && (\n                      <div>\n                        <p className=\"text-gray-500\">Method</p>\n                        <p className=\"font-medium\">{payment.paymentMethod}</p>\n                      </div>\n                    )}\n                  </div>\n                  \n                  {payment.transactionId && (\n                    <div className=\"mt-3 text-xs text-gray-500\">\n                      Transaction ID: {payment.transactionId}\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-2 ml-4\">\n                {payment.status === 'pending' && (\n                  <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\">\n                    Pay Now\n                  </button>\n                )}\n                \n                {payment.receipt && (\n                  <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                    <Download className=\"w-4 h-4\" />\n                  </button>\n                )}\n                \n                <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                  <Eye className=\"w-4 h-4\" />\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredPayments.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <CreditCard className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No payments found</h3>\n          <p className=\"text-gray-600\">\n            {searchTerm || filterStatus !== 'all' || filterType !== 'all'\n              ? 'Try adjusting your search or filters.'\n              : 'No payment history available.'\n            }\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAqCe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IACzF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyE;IACpH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBAAqB;IACrB,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACrC;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;YACb,UAAU;YACV,eAAe;YACf,eAAe;YACf,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;YACb,UAAU;YACV,eAAe;YACf,eAAe;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,aAAa;YACb,UAAU;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;YACb,UAAU;YACV,eAAe;YACf,eAAe;QACjB;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACnF,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,KAAK;QACnE,MAAM,cAAc,eAAe,SAAS,QAAQ,IAAI,KAAK;QAE7D,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IAC/F,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;IACrG,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG;IAExH,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,2MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;QACjC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,YAAY,CAAC,SAAiB;QAClC,OAAO,WAAW,UAAU,IAAI,KAAK,WAAW,IAAI;IACtD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAKhF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAKnF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;0CAG1B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wBAAqB,WAAU;kCAC9B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ,IAAI;;;;;;sDAG3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuC,QAAQ,WAAW;;;;;;sEACxE,6LAAC;4DAAK,WAAW,AAAC,2EAAyG,OAA/B,eAAe,QAAQ,MAAM;;gEACtH,cAAc,QAAQ,MAAM;8EAC7B,6LAAC;oEAAK,WAAU;8EAAmB,QAAQ,MAAM;;;;;;;;;;;;wDAElD,UAAU,QAAQ,OAAO,EAAE,QAAQ,MAAM,mBACxC,6LAAC;4DAAK,WAAU;sEAAkG;;;;;;;;;;;;8DAMtH,6LAAC;oDAAE,WAAU;8DAA8B,QAAQ,QAAQ;;;;;;8DAE3D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;;wEAAc;wEAAE,QAAQ,MAAM,CAAC,cAAc;;;;;;;gEACzD,QAAQ,OAAO,kBACd,6LAAC;oEAAE,WAAU;;wEAAuB;wEAAI,QAAQ,OAAO;wEAAC;;;;;;;;;;;;;sEAG5D,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAe,WAAW,QAAQ,OAAO;;;;;;;;;;;;wDAEvD,QAAQ,QAAQ,kBACf,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAe,WAAW,QAAQ,QAAQ;;;;;;;;;;;;wDAG1D,QAAQ,aAAa,kBACpB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAe,QAAQ,aAAa;;;;;;;;;;;;;;;;;;gDAKtD,QAAQ,aAAa,kBACpB,6LAAC;oDAAI,WAAU;;wDAA6B;wDACzB,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;8CAM9C,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,MAAM,KAAK,2BAClB,6LAAC;4CAAO,WAAU;sDAAsG;;;;;;wCAKzH,QAAQ,OAAO,kBACd,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAIxB,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBAvEb,QAAQ,EAAE;;;;;;;;;;YA+EvB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,iBAAiB,SAAS,eAAe,QACpD,0CACA;;;;;;;;;;;;;;;;;;AAOhB;GAnUwB;KAAA", "debugId": null}}, {"offset": {"line": 7474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/MaintenancePanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Wrench,\n  Plus,\n  Search,\n  Filter,\n  Calendar,\n  Clock,\n  CheckCircle,\n  AlertTriangle,\n  User,\n  Phone,\n  Mail,\n  MapPin,\n  Camera,\n  MessageSquare,\n  Star,\n  Upload,\n  Eye,\n  Edit,\n  X\n} from 'lucide-react';\n\ninterface MaintenanceRequest {\n  id: string;\n  title: string;\n  description: string;\n  category: 'plumbing' | 'electrical' | 'hvac' | 'appliance' | 'structural' | 'other';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  status: 'submitted' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled';\n  submittedDate: string;\n  scheduledDate?: string;\n  completedDate?: string;\n  property: string;\n  assignedTo?: string;\n  contactInfo?: string;\n  estimatedCost?: number;\n  actualCost?: number;\n  images?: string[];\n  notes?: string;\n  rating?: number;\n  feedback?: string;\n}\n\nexport default function MaintenancePanel() {\n  const [filterStatus, setFilterStatus] = useState<'all' | 'submitted' | 'scheduled' | 'in_progress' | 'completed'>('all');\n  const [filterPriority, setFilterPriority] = useState<'all' | 'low' | 'medium' | 'high' | 'urgent'>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showNewRequestForm, setShowNewRequestForm] = useState(false);\n\n  // Mock maintenance requests data\n  const [requests] = useState<MaintenanceRequest[]>([\n    {\n      id: '1',\n      title: 'Kitchen Faucet Leak',\n      description: 'The kitchen faucet has been dripping constantly for the past week. Water is pooling under the sink.',\n      category: 'plumbing',\n      priority: 'medium',\n      status: 'scheduled',\n      submittedDate: '2024-01-20T10:30:00Z',\n      scheduledDate: '2024-01-30T14:00:00Z',\n      property: '123 Main St, Downtown, NY 10001',\n      assignedTo: 'Mike Johnson - Plumbing Pro',\n      contactInfo: '(*************',\n      estimatedCost: 150,\n      images: ['/api/placeholder/300/200'],\n    },\n    {\n      id: '2',\n      title: 'Broken Light Switch',\n      description: 'Living room light switch is not working. Lights won\\'t turn on.',\n      category: 'electrical',\n      priority: 'high',\n      status: 'in_progress',\n      submittedDate: '2024-01-18T16:45:00Z',\n      scheduledDate: '2024-01-25T09:00:00Z',\n      property: '123 Main St, Downtown, NY 10001',\n      assignedTo: 'Sarah Electric Services',\n      contactInfo: '(*************',\n      estimatedCost: 75,\n    },\n    {\n      id: '3',\n      title: 'AC Not Cooling',\n      description: 'Air conditioning unit is running but not cooling the apartment effectively.',\n      category: 'hvac',\n      priority: 'urgent',\n      status: 'submitted',\n      submittedDate: '2024-01-22T08:15:00Z',\n      property: '123 Main St, Downtown, NY 10001',\n    },\n    {\n      id: '4',\n      title: 'Dishwasher Repair',\n      description: 'Dishwasher is making loud noises and not cleaning dishes properly.',\n      category: 'appliance',\n      priority: 'low',\n      status: 'completed',\n      submittedDate: '2024-01-10T12:00:00Z',\n      scheduledDate: '2024-01-15T10:00:00Z',\n      completedDate: '2024-01-15T11:30:00Z',\n      property: '123 Main St, Downtown, NY 10001',\n      assignedTo: 'Appliance Fix Co.',\n      contactInfo: '(*************',\n      estimatedCost: 120,\n      actualCost: 95,\n      rating: 5,\n      feedback: 'Excellent service! Fixed quickly and professionally.',\n    },\n  ]);\n\n  const filteredRequests = requests.filter(request => {\n    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         request.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || request.status === filterStatus;\n    const matchesPriority = filterPriority === 'all' || request.priority === filterPriority;\n    \n    return matchesSearch && matchesStatus && matchesPriority;\n  });\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'submitted':\n        return 'bg-blue-100 text-blue-800';\n      case 'scheduled':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'in_progress':\n        return 'bg-orange-100 text-orange-800';\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'cancelled':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'urgent':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'high':\n        return 'bg-orange-100 text-orange-800 border-orange-200';\n      case 'medium':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'low':\n        return 'bg-green-100 text-green-800 border-green-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'plumbing':\n        return '🔧';\n      case 'electrical':\n        return '⚡';\n      case 'hvac':\n        return '❄️';\n      case 'appliance':\n        return '🏠';\n      case 'structural':\n        return '🏗️';\n      default:\n        return '🔨';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const activeRequests = requests.filter(r => ['submitted', 'scheduled', 'in_progress'].includes(r.status)).length;\n  const completedRequests = requests.filter(r => r.status === 'completed').length;\n  const urgentRequests = requests.filter(r => r.priority === 'urgent' && r.status !== 'completed').length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Maintenance Requests</h2>\n        <button \n          onClick={() => setShowNewRequestForm(true)}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\"\n        >\n          <Plus className=\"w-4 h-4 mr-2\" />\n          New Request\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Clock className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Active Requests</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{activeRequests}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CheckCircle className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Completed</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{completedRequests}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <AlertTriangle className=\"w-6 h-6 text-red-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Urgent</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{urgentRequests}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex flex-col sm:flex-row gap-4 flex-1\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search requests...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            \n            <select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"submitted\">Submitted</option>\n              <option value=\"scheduled\">Scheduled</option>\n              <option value=\"in_progress\">In Progress</option>\n              <option value=\"completed\">Completed</option>\n            </select>\n\n            <select\n              value={filterPriority}\n              onChange={(e) => setFilterPriority(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"all\">All Priority</option>\n              <option value=\"urgent\">Urgent</option>\n              <option value=\"high\">High</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"low\">Low</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Requests List */}\n      <div className=\"space-y-4\">\n        {filteredRequests.map((request) => (\n          <div key={request.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex items-start space-x-4 flex-1\">\n                <div className=\"text-2xl\">{getCategoryIcon(request.category)}</div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <h3 className=\"text-lg font-semibold text-gray-900\">{request.title}</h3>\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>\n                      {request.status.replace('_', ' ')}\n                    </span>\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getPriorityColor(request.priority)}`}>\n                      {request.priority}\n                    </span>\n                  </div>\n                  \n                  <p className=\"text-sm text-gray-600 mb-3\">{request.description}</p>\n                  <p className=\"text-sm text-gray-500 mb-4 flex items-center\">\n                    <MapPin className=\"w-4 h-4 mr-1\" />\n                    {request.property}\n                  </p>\n                  \n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                    <div>\n                      <p className=\"text-gray-500\">Submitted</p>\n                      <p className=\"font-medium\">{formatDate(request.submittedDate)}</p>\n                    </div>\n                    {request.scheduledDate && (\n                      <div>\n                        <p className=\"text-gray-500\">Scheduled</p>\n                        <p className=\"font-medium\">{formatDate(request.scheduledDate)}</p>\n                      </div>\n                    )}\n                    {request.completedDate && (\n                      <div>\n                        <p className=\"text-gray-500\">Completed</p>\n                        <p className=\"font-medium\">{formatDate(request.completedDate)}</p>\n                      </div>\n                    )}\n                  </div>\n\n                  {request.assignedTo && (\n                    <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\n                      <p className=\"text-sm font-medium text-gray-900 flex items-center\">\n                        <User className=\"w-4 h-4 mr-2\" />\n                        {request.assignedTo}\n                      </p>\n                      {request.contactInfo && (\n                        <p className=\"text-sm text-gray-600 flex items-center mt-1\">\n                          <Phone className=\"w-4 h-4 mr-2\" />\n                          {request.contactInfo}\n                        </p>\n                      )}\n                    </div>\n                  )}\n\n                  {request.rating && (\n                    <div className=\"mt-4 p-3 bg-green-50 rounded-lg\">\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <span className=\"text-sm font-medium text-gray-900\">Rating:</span>\n                        <div className=\"flex\">\n                          {[...Array(5)].map((_, i) => (\n                            <Star\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < request.rating! ? 'text-yellow-400 fill-current' : 'text-gray-300'\n                              }`}\n                            />\n                          ))}\n                        </div>\n                      </div>\n                      {request.feedback && (\n                        <p className=\"text-sm text-gray-600\">{request.feedback}</p>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"flex flex-col items-end space-y-2 ml-4\">\n                {request.estimatedCost && (\n                  <div className=\"text-right\">\n                    <p className=\"text-sm text-gray-500\">\n                      {request.actualCost ? 'Actual Cost' : 'Estimated Cost'}\n                    </p>\n                    <p className=\"font-semibold text-gray-900\">\n                      ${(request.actualCost || request.estimatedCost).toLocaleString()}\n                    </p>\n                  </div>\n                )}\n                \n                <div className=\"flex space-x-2\">\n                  <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                    <Eye className=\"w-4 h-4\" />\n                  </button>\n                  {request.status !== 'completed' && (\n                    <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                      <Edit className=\"w-4 h-4\" />\n                    </button>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredRequests.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <Wrench className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No maintenance requests found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm || filterStatus !== 'all' || filterPriority !== 'all'\n              ? 'Try adjusting your search or filters.'\n              : 'No maintenance requests submitted yet.'\n            }\n          </p>\n          <button \n            onClick={() => setShowNewRequestForm(true)}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\"\n          >\n            Submit Request\n          </button>\n        </div>\n      )}\n\n      {/* New Request Form Modal would go here */}\n      {showNewRequestForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-xl max-w-md w-full p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">New Maintenance Request</h3>\n              <button \n                onClick={() => setShowNewRequestForm(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"w-6 h-6\" />\n              </button>\n            </div>\n            <p className=\"text-gray-600 text-center py-8\">\n              Request form would be implemented here with fields for title, description, category, priority, and image upload.\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA8Ce,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmE;IAClH,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgD;IACnG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,iCAAiC;IACjC,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QAChD;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,eAAe;YACf,eAAe;YACf,UAAU;YACV,YAAY;YACZ,aAAa;YACb,eAAe;YACf,QAAQ;gBAAC;aAA2B;QACtC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,eAAe;YACf,eAAe;YACf,UAAU;YACV,YAAY;YACZ,aAAa;YACb,eAAe;QACjB;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,eAAe;YACf,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,eAAe;YACf,eAAe;YACf,eAAe;YACf,UAAU;YACV,YAAY;YACZ,aAAa;YACb,eAAe;YACf,YAAY;YACZ,QAAQ;YACR,UAAU;QACZ;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACtF,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,KAAK;QACnE,MAAM,kBAAkB,mBAAmB,SAAS,QAAQ,QAAQ,KAAK;QAEzE,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK;YAAC;YAAa;YAAa;SAAc,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;IAChH,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IAC/E,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,EAAE,MAAM,KAAK,aAAa,MAAM;IAEvG,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBACC,SAAS,IAAM,sBAAsB;wBACrC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wBAAqB,WAAU;kCAC9B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAY,gBAAgB,QAAQ,QAAQ;;;;;;sDAE3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuC,QAAQ,KAAK;;;;;;sEAClE,6LAAC;4DAAK,WAAW,AAAC,2EAAyG,OAA/B,eAAe,QAAQ,MAAM;sEACtH,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;sEAE/B,6LAAC;4DAAK,WAAW,AAAC,kFAAoH,OAAnC,iBAAiB,QAAQ,QAAQ;sEACjI,QAAQ,QAAQ;;;;;;;;;;;;8DAIrB,6LAAC;oDAAE,WAAU;8DAA8B,QAAQ,WAAW;;;;;;8DAC9D,6LAAC;oDAAE,WAAU;;sEACX,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,QAAQ,QAAQ;;;;;;;8DAGnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAe,WAAW,QAAQ,aAAa;;;;;;;;;;;;wDAE7D,QAAQ,aAAa,kBACpB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAe,WAAW,QAAQ,aAAa;;;;;;;;;;;;wDAG/D,QAAQ,aAAa,kBACpB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAe,WAAW,QAAQ,aAAa;;;;;;;;;;;;;;;;;;gDAKjE,QAAQ,UAAU,kBACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,QAAQ,UAAU;;;;;;;wDAEpB,QAAQ,WAAW,kBAClB,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,QAAQ,WAAW;;;;;;;;;;;;;gDAM3B,QAAQ,MAAM,kBACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAoC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EACZ;2EAAI,MAAM;qEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;4EAEH,WAAW,AAAC,WAEX,OADC,IAAI,QAAQ,MAAM,GAAI,iCAAiC;2EAFpD;;;;;;;;;;;;;;;;wDAQZ,QAAQ,QAAQ,kBACf,6LAAC;4DAAE,WAAU;sEAAyB,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8CAOhE,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,aAAa,kBACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,QAAQ,UAAU,GAAG,gBAAgB;;;;;;8DAExC,6LAAC;oDAAE,WAAU;;wDAA8B;wDACvC,CAAC,QAAQ,UAAU,IAAI,QAAQ,aAAa,EAAE,cAAc;;;;;;;;;;;;;sDAKpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;gDAEhB,QAAQ,MAAM,KAAK,6BAClB,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAjGlB,QAAQ,EAAE;;;;;;;;;;YA2GvB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,iBAAiB,SAAS,mBAAmB,QACxD,0CACA;;;;;;kCAGN,6LAAC;wBACC,SAAS,IAAM,sBAAsB;wBACrC,WAAU;kCACX;;;;;;;;;;;;YAOJ,oCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCACC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAGjB,6LAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GAhYwB;KAAA", "debugId": null}}, {"offset": {"line": 8469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/MarketplacePanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Search,\n  Filter,\n  Heart,\n  MapPin,\n  Bed,\n  Bath,\n  Square,\n  DollarSign,\n  TrendingUp,\n  Eye,\n  Share2,\n  Star,\n  Building2,\n  Calendar,\n  Users,\n  Coins,\n  Grid3X3,\n  List,\n  SlidersHorizontal\n} from 'lucide-react';\n\ninterface Property {\n  id: string;\n  title: string;\n  address: string;\n  price: number;\n  type: 'apartment' | 'house' | 'condo' | 'townhouse';\n  bedrooms: number;\n  bathrooms: number;\n  sqft: number;\n  yearBuilt: number;\n  description: string;\n  features: string[];\n  images: string[];\n  agent: {\n    name: string;\n    phone: string;\n    email: string;\n  };\n  listingDate: string;\n  status: 'available' | 'pending' | 'sold';\n  pricePerSqft: number;\n  monthlyRent?: number;\n  roi?: number;\n  neighborhood: string;\n  walkScore?: number;\n  isFavorited: boolean;\n  viewCount: number;\n}\n\nexport default function MarketplacePanel() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<'all' | 'apartment' | 'house' | 'condo' | 'townhouse'>('all');\n  const [priceRange, setPriceRange] = useState<'all' | '0-500k' | '500k-1m' | '1m+'>('all');\n  const [sortBy, setSortBy] = useState<'price-low' | 'price-high' | 'newest' | 'popular'>('newest');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Mock properties data\n  const [properties, setProperties] = useState<Property[]>([\n    {\n      id: '1',\n      title: 'Modern Downtown Loft',\n      address: '456 Broadway, Downtown, NY 10013',\n      price: 850000,\n      type: 'apartment',\n      bedrooms: 2,\n      bathrooms: 2,\n      sqft: 1400,\n      yearBuilt: 2018,\n      description: 'Stunning modern loft with floor-to-ceiling windows and city views.',\n      features: ['Hardwood Floors', 'Stainless Steel Appliances', 'In-Unit Laundry', 'Balcony'],\n      images: ['/api/placeholder/400/300'],\n      agent: {\n        name: 'Sarah Johnson',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      listingDate: '2024-01-15T10:00:00Z',\n      status: 'available',\n      pricePerSqft: 607,\n      monthlyRent: 4200,\n      roi: 5.9,\n      neighborhood: 'SoHo',\n      walkScore: 95,\n      isFavorited: false,\n      viewCount: 234\n    },\n    {\n      id: '2',\n      title: 'Charming Brooklyn Townhouse',\n      address: '789 Park Slope Ave, Brooklyn, NY 11215',\n      price: 1200000,\n      type: 'townhouse',\n      bedrooms: 4,\n      bathrooms: 3,\n      sqft: 2200,\n      yearBuilt: 1920,\n      description: 'Historic townhouse with original details and modern updates.',\n      features: ['Original Hardwood', 'Renovated Kitchen', 'Private Garden', 'Fireplace'],\n      images: ['/api/placeholder/400/300'],\n      agent: {\n        name: 'Mike Chen',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      listingDate: '2024-01-20T14:30:00Z',\n      status: 'available',\n      pricePerSqft: 545,\n      monthlyRent: 5800,\n      roi: 5.8,\n      neighborhood: 'Park Slope',\n      walkScore: 88,\n      isFavorited: true,\n      viewCount: 156\n    },\n    {\n      id: '3',\n      title: 'Luxury Upper East Side Condo',\n      address: '321 E 72nd St, Upper East Side, NY 10021',\n      price: 2500000,\n      type: 'condo',\n      bedrooms: 3,\n      bathrooms: 2,\n      sqft: 1800,\n      yearBuilt: 2015,\n      description: 'Elegant condo with Central Park views and premium amenities.',\n      features: ['Central Park Views', 'Doorman', 'Gym', 'Roof Deck'],\n      images: ['/api/placeholder/400/300'],\n      agent: {\n        name: 'Emily Rodriguez',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      listingDate: '2024-01-18T09:15:00Z',\n      status: 'pending',\n      pricePerSqft: 1389,\n      monthlyRent: 8500,\n      roi: 4.1,\n      neighborhood: 'Upper East Side',\n      walkScore: 92,\n      isFavorited: false,\n      viewCount: 89\n    },\n    {\n      id: '4',\n      title: 'Cozy Queens Family Home',\n      address: '654 Maple St, Astoria, NY 11106',\n      price: 650000,\n      type: 'house',\n      bedrooms: 3,\n      bathrooms: 2,\n      sqft: 1600,\n      yearBuilt: 1950,\n      description: 'Perfect family home with yard and garage in quiet neighborhood.',\n      features: ['Private Yard', 'Garage', 'Updated Kitchen', 'Basement'],\n      images: ['/api/placeholder/400/300'],\n      agent: {\n        name: 'David Kim',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      listingDate: '2024-01-22T16:45:00Z',\n      status: 'available',\n      pricePerSqft: 406,\n      monthlyRent: 3200,\n      roi: 5.9,\n      neighborhood: 'Astoria',\n      walkScore: 78,\n      isFavorited: true,\n      viewCount: 67\n    }\n  ]);\n\n  const filteredProperties = properties.filter(property => {\n    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         property.neighborhood.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || property.type === filterType;\n    const matchesPrice = priceRange === 'all' || \n                        (priceRange === '0-500k' && property.price <= 500000) ||\n                        (priceRange === '500k-1m' && property.price > 500000 && property.price <= 1000000) ||\n                        (priceRange === '1m+' && property.price > 1000000);\n    \n    return matchesSearch && matchesType && matchesPrice;\n  });\n\n  // Sort properties\n  const sortedProperties = [...filteredProperties].sort((a, b) => {\n    switch (sortBy) {\n      case 'price-low':\n        return a.price - b.price;\n      case 'price-high':\n        return b.price - a.price;\n      case 'newest':\n        return new Date(b.listingDate).getTime() - new Date(a.listingDate).getTime();\n      case 'popular':\n        return b.viewCount - a.viewCount;\n      default:\n        return 0;\n    }\n  });\n\n  const toggleFavorite = (propertyId: string) => {\n    setProperties(prev =>\n      prev.map(property =>\n        property.id === propertyId\n          ? { ...property, isFavorited: !property.isFavorited }\n          : property\n      )\n    );\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'available':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'sold':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    if (price >= 1000000) {\n      return `$${(price / 1000000).toFixed(1)}M`;\n    }\n    return `$${(price / 1000).toFixed(0)}K`;\n  };\n\n  const totalListings = properties.length;\n  const availableListings = properties.filter(p => p.status === 'available').length;\n  const averagePrice = properties.reduce((sum, p) => sum + p.price, 0) / properties.length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Property Marketplace</h2>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <SlidersHorizontal className=\"w-5 h-5\" />\n          </button>\n          <button\n            onClick={() => setViewMode('grid')}\n            className={`p-2 rounded-lg transition-colors ${\n              viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            <Grid3X3 className=\"w-4 h-4\" />\n          </button>\n          <button\n            onClick={() => setViewMode('list')}\n            className={`p-2 rounded-lg transition-colors ${\n              viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            <List className=\"w-4 h-4\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Building2 className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Listings</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalListings}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Available</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{availableListings}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Avg. Price</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{formatPrice(averagePrice)}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search by location, neighborhood...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            \n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as any)}\n              className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"newest\">Newest First</option>\n              <option value=\"price-low\">Price: Low to High</option>\n              <option value=\"price-high\">Price: High to Low</option>\n              <option value=\"popular\">Most Popular</option>\n            </select>\n          </div>\n\n          {showFilters && (\n            <div className=\"flex flex-wrap gap-4 pt-4 border-t border-gray-200\">\n              <select\n                value={filterType}\n                onChange={(e) => setFilterType(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Types</option>\n                <option value=\"apartment\">Apartment</option>\n                <option value=\"house\">House</option>\n                <option value=\"condo\">Condo</option>\n                <option value=\"townhouse\">Townhouse</option>\n              </select>\n\n              <select\n                value={priceRange}\n                onChange={(e) => setPriceRange(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Prices</option>\n                <option value=\"0-500k\">Under $500K</option>\n                <option value=\"500k-1m\">$500K - $1M</option>\n                <option value=\"1m+\">Over $1M</option>\n              </select>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Properties Grid/List */}\n      {viewMode === 'grid' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {sortedProperties.map((property) => (\n            <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n              <div className=\"relative\">\n                <img\n                  src={property.images[0]}\n                  alt={property.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"absolute top-4 left-4\">\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(property.status)}`}>\n                    {property.status}\n                  </span>\n                </div>\n                <div className=\"absolute top-4 right-4 flex space-x-2\">\n                  <button\n                    onClick={() => toggleFavorite(property.id)}\n                    className={`p-2 rounded-full transition-colors ${\n                      property.isFavorited \n                        ? 'bg-red-100 text-red-600' \n                        : 'bg-white bg-opacity-90 text-gray-600 hover:bg-opacity-100'\n                    }`}\n                  >\n                    <Heart className={`w-4 h-4 ${property.isFavorited ? 'fill-current' : ''}`} />\n                  </button>\n                  <button className=\"bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all\">\n                    <Share2 className=\"w-4 h-4 text-gray-600\" />\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-3\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">{property.title}</h3>\n                    <p className=\"text-sm text-gray-600 flex items-center\">\n                      <MapPin className=\"w-4 h-4 mr-1\" />\n                      {property.neighborhood}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-xl font-bold text-gray-900\">{formatPrice(property.price)}</p>\n                    <p className=\"text-sm text-gray-600\">${property.pricePerSqft}/sqft</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-4\">\n                  <span className=\"flex items-center\">\n                    <Bed className=\"w-4 h-4 mr-1\" />\n                    {property.bedrooms} bed\n                  </span>\n                  <span className=\"flex items-center\">\n                    <Bath className=\"w-4 h-4 mr-1\" />\n                    {property.bathrooms} bath\n                  </span>\n                  <span className=\"flex items-center\">\n                    <Square className=\"w-4 h-4 mr-1\" />\n                    {property.sqft.toLocaleString()} sqft\n                  </span>\n                </div>\n\n                {property.roi && (\n                  <div className=\"bg-green-50 rounded-lg p-3 mb-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm font-medium text-gray-900\">Investment ROI</span>\n                      <span className=\"text-sm font-bold text-green-600\">{property.roi}%</span>\n                    </div>\n                    <p className=\"text-xs text-gray-600 mt-1\">\n                      Est. monthly rent: ${property.monthlyRent?.toLocaleString()}\n                    </p>\n                  </div>\n                )}\n\n                <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                  <div className=\"flex items-center space-x-2 text-xs text-gray-500\">\n                    <Eye className=\"w-4 h-4\" />\n                    <span>{property.viewCount} views</span>\n                  </div>\n                  <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\">\n                    View Details\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n          <div className=\"space-y-4 p-6\">\n            {sortedProperties.map((property) => (\n              <div key={property.id} className=\"flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow\">\n                <img\n                  src={property.images[0]}\n                  alt={property.title}\n                  className=\"w-24 h-24 object-cover rounded-lg flex-shrink-0\"\n                />\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{property.title}</h3>\n                      <p className=\"text-sm text-gray-600 flex items-center mt-1\">\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        {property.address}\n                      </p>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600 mt-2\">\n                        <span>{property.bedrooms} bed</span>\n                        <span>{property.bathrooms} bath</span>\n                        <span>{property.sqft.toLocaleString()} sqft</span>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-xl font-bold text-gray-900\">{formatPrice(property.price)}</p>\n                      <p className=\"text-sm text-gray-600\">${property.pricePerSqft}/sqft</p>\n                      {property.roi && (\n                        <p className=\"text-sm text-green-600 font-medium mt-1\">{property.roi}% ROI</p>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                <div className=\"flex flex-col items-end space-y-2\">\n                  <button\n                    onClick={() => toggleFavorite(property.id)}\n                    className={`p-2 rounded-full transition-colors ${\n                      property.isFavorited \n                        ? 'bg-red-100 text-red-600' \n                        : 'text-gray-400 hover:text-red-600 hover:bg-red-50'\n                    }`}\n                  >\n                    <Heart className={`w-4 h-4 ${property.isFavorited ? 'fill-current' : ''}`} />\n                  </button>\n                  <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\">\n                    View Details\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {sortedProperties.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <Search className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No properties found</h3>\n          <p className=\"text-gray-600\">\n            Try adjusting your search criteria or filters to find more properties.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAsDe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyD;IACpG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IACnF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqD;IACxF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uBAAuB;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,WAAW;YACX,aAAa;YACb,UAAU;gBAAC;gBAAmB;gBAA8B;gBAAmB;aAAU;YACzF,QAAQ;gBAAC;aAA2B;YACpC,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,aAAa;YACb,QAAQ;YACR,cAAc;YACd,aAAa;YACb,KAAK;YACL,cAAc;YACd,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,WAAW;YACX,aAAa;YACb,UAAU;gBAAC;gBAAqB;gBAAqB;gBAAkB;aAAY;YACnF,QAAQ;gBAAC;aAA2B;YACpC,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,aAAa;YACb,QAAQ;YACR,cAAc;YACd,aAAa;YACb,KAAK;YACL,cAAc;YACd,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,WAAW;YACX,aAAa;YACb,UAAU;gBAAC;gBAAsB;gBAAW;gBAAO;aAAY;YAC/D,QAAQ;gBAAC;aAA2B;YACpC,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,aAAa;YACb,QAAQ;YACR,cAAc;YACd,aAAa;YACb,KAAK;YACL,cAAc;YACd,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,WAAW;YACX,aAAa;YACb,UAAU;gBAAC;gBAAgB;gBAAU;gBAAmB;aAAW;YACnE,QAAQ;gBAAC;aAA2B;YACpC,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,aAAa;YACb,QAAQ;YACR,cAAc;YACd,aAAa;YACb,KAAK;YACL,cAAc;YACd,WAAW;YACX,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA;QAC3C,MAAM,gBAAgB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,SAAS,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACxF,MAAM,cAAc,eAAe,SAAS,SAAS,IAAI,KAAK;QAC9D,MAAM,eAAe,eAAe,SACf,eAAe,YAAY,SAAS,KAAK,IAAI,UAC7C,eAAe,aAAa,SAAS,KAAK,GAAG,UAAU,SAAS,KAAK,IAAI,WACzE,eAAe,SAAS,SAAS,KAAK,GAAG;QAE9D,OAAO,iBAAiB,eAAe;IACzC;IAEA,kBAAkB;IAClB,MAAM,mBAAmB;WAAI;KAAmB,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;YAC5E,KAAK;gBACH,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;YAClC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,cAAc,CAAA,OACZ,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,aACZ;oBAAE,GAAG,QAAQ;oBAAE,aAAa,CAAC,SAAS,WAAW;gBAAC,IAClD;IAGV;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,SAAS;YACpB,OAAO,AAAC,IAAgC,OAA7B,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,IAAG;QAC1C;QACA,OAAO,AAAC,IAA6B,OAA1B,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAG;IACvC;IAEA,MAAM,gBAAgB,WAAW,MAAM;IACvC,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IACjF,MAAM,eAAe,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,WAAW,MAAM;IAExF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;0CAEV,cAAA,6LAAC,mOAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;;;;;;0CAE/B,6LAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAW,AAAC,oCAEX,OADC,aAAa,SAAS,8BAA8B;0CAGtD,cAAA,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAErB,6LAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAW,AAAC,oCAEX,OADC,aAAa,SAAS,8BAA8B;0CAGtD,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAa;;;;;;sDAC3B,6LAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;;;;;;;wBAI3B,6BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;;;;;;;8CAG5B,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ7B,aAAa,uBACZ,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC;wBAmEU;yCAlE/B,6LAAC;wBAAsB,WAAU;;0CAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,SAAS,MAAM,CAAC,EAAE;wCACvB,KAAK,SAAS,KAAK;wCACnB,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAW,AAAC,2EAA0G,OAAhC,eAAe,SAAS,MAAM;sDACvH,SAAS,MAAM;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,eAAe,SAAS,EAAE;gDACzC,WAAW,AAAC,sCAIX,OAHC,SAAS,WAAW,GAChB,4BACA;0DAGN,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAW,AAAC,WAAqD,OAA3C,SAAS,WAAW,GAAG,iBAAiB;;;;;;;;;;;0DAEvE,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA4C,SAAS,KAAK;;;;;;kEACxE,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,SAAS,YAAY;;;;;;;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAmC,YAAY,SAAS,KAAK;;;;;;kEAC1E,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAE,SAAS,YAAY;4DAAC;;;;;;;;;;;;;;;;;;;kDAIjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,SAAS,QAAQ;oDAAC;;;;;;;0DAErB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,SAAS,SAAS;oDAAC;;;;;;;0DAEtB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,SAAS,IAAI,CAAC,cAAc;oDAAG;;;;;;;;;;;;;oCAInC,SAAS,GAAG,kBACX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,6LAAC;wDAAK,WAAU;;4DAAoC,SAAS,GAAG;4DAAC;;;;;;;;;;;;;0DAEnE,6LAAC;gDAAE,WAAU;;oDAA6B;qDACnB,wBAAA,SAAS,WAAW,cAApB,4CAAA,sBAAsB,cAAc;;;;;;;;;;;;;kDAK/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;;4DAAM,SAAS,SAAS;4DAAC;;;;;;;;;;;;;0DAE5B,6LAAC;gDAAO,WAAU;0DAAsG;;;;;;;;;;;;;;;;;;;uBA5EpH,SAAS,EAAE;;;;;;;;;;qCAqFzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;4BAAsB,WAAU;;8CAC/B,6LAAC;oCACC,KAAK,SAAS,MAAM,CAAC,EAAE;oCACvB,KAAK,SAAS,KAAK;oCACnB,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAuC,SAAS,KAAK;;;;;;kEACnE,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,SAAS,OAAO;;;;;;;kEAEnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,SAAS,QAAQ;oEAAC;;;;;;;0EACzB,6LAAC;;oEAAM,SAAS,SAAS;oEAAC;;;;;;;0EAC1B,6LAAC;;oEAAM,SAAS,IAAI,CAAC,cAAc;oEAAG;;;;;;;;;;;;;;;;;;;0DAG1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAmC,YAAY,SAAS,KAAK;;;;;;kEAC1E,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAE,SAAS,YAAY;4DAAC;;;;;;;oDAC5D,SAAS,GAAG,kBACX,6LAAC;wDAAE,WAAU;;4DAA2C,SAAS,GAAG;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAK7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe,SAAS,EAAE;4CACzC,WAAW,AAAC,sCAIX,OAHC,SAAS,WAAW,GAChB,4BACA;sDAGN,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAAqD,OAA3C,SAAS,WAAW,GAAG,iBAAiB;;;;;;;;;;;sDAEvE,6LAAC;4CAAO,WAAU;sDAAsG;;;;;;;;;;;;;2BAxClH,SAAS,EAAE;;;;;;;;;;;;;;;YAkD5B,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAOvC;GAldwB;KAAA", "debugId": null}}, {"offset": {"line": 9707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/FavoritesPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Heart,\n  MapPin,\n  Bed,\n  Bath,\n  Square,\n  DollarSign,\n  TrendingUp,\n  Eye,\n  Share2,\n  X,\n  Calendar,\n  Filter,\n  Search,\n  Grid3X3,\n  List,\n  AlertCircle,\n  ExternalLink\n} from 'lucide-react';\n\ninterface FavoriteProperty {\n  id: string;\n  title: string;\n  address: string;\n  price: number;\n  type: 'apartment' | 'house' | 'condo' | 'townhouse';\n  bedrooms: number;\n  bathrooms: number;\n  sqft: number;\n  pricePerSqft: number;\n  monthlyRent?: number;\n  roi?: number;\n  neighborhood: string;\n  images: string[];\n  addedDate: string;\n  lastViewed: string;\n  priceChange?: {\n    amount: number;\n    direction: 'up' | 'down';\n    date: string;\n  };\n  status: 'available' | 'pending' | 'sold' | 'off-market';\n  notes?: string;\n}\n\nexport default function FavoritesPanel() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<'all' | 'apartment' | 'house' | 'condo' | 'townhouse'>('all');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'available' | 'pending' | 'sold' | 'off-market'>('all');\n  const [sortBy, setSortBy] = useState<'newest' | 'price-low' | 'price-high' | 'recently-viewed'>('newest');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n\n  // Mock favorites data\n  const [favorites, setFavorites] = useState<FavoriteProperty[]>([\n    {\n      id: '1',\n      title: 'Modern Downtown Loft',\n      address: '456 Broadway, Downtown, NY 10013',\n      price: 850000,\n      type: 'apartment',\n      bedrooms: 2,\n      bathrooms: 2,\n      sqft: 1400,\n      pricePerSqft: 607,\n      monthlyRent: 4200,\n      roi: 5.9,\n      neighborhood: 'SoHo',\n      images: ['/api/placeholder/400/300'],\n      addedDate: '2024-01-15T10:00:00Z',\n      lastViewed: '2024-01-23T14:30:00Z',\n      priceChange: {\n        amount: 25000,\n        direction: 'down',\n        date: '2024-01-20T00:00:00Z'\n      },\n      status: 'available',\n      notes: 'Great investment opportunity in prime location'\n    },\n    {\n      id: '2',\n      title: 'Charming Brooklyn Townhouse',\n      address: '789 Park Slope Ave, Brooklyn, NY 11215',\n      price: 1200000,\n      type: 'townhouse',\n      bedrooms: 4,\n      bathrooms: 3,\n      sqft: 2200,\n      pricePerSqft: 545,\n      monthlyRent: 5800,\n      roi: 5.8,\n      neighborhood: 'Park Slope',\n      images: ['/api/placeholder/400/300'],\n      addedDate: '2024-01-10T16:45:00Z',\n      lastViewed: '2024-01-22T09:15:00Z',\n      status: 'available'\n    },\n    {\n      id: '3',\n      title: 'Cozy Queens Family Home',\n      address: '654 Maple St, Astoria, NY 11106',\n      price: 650000,\n      type: 'house',\n      bedrooms: 3,\n      bathrooms: 2,\n      sqft: 1600,\n      pricePerSqft: 406,\n      monthlyRent: 3200,\n      roi: 5.9,\n      neighborhood: 'Astoria',\n      images: ['/api/placeholder/400/300'],\n      addedDate: '2024-01-08T12:20:00Z',\n      lastViewed: '2024-01-21T18:45:00Z',\n      priceChange: {\n        amount: 15000,\n        direction: 'up',\n        date: '2024-01-18T00:00:00Z'\n      },\n      status: 'pending',\n      notes: 'Owner motivated to sell quickly'\n    },\n    {\n      id: '4',\n      title: 'Luxury Manhattan Penthouse',\n      address: '123 5th Ave, Midtown, NY 10016',\n      price: 3500000,\n      type: 'condo',\n      bedrooms: 4,\n      bathrooms: 3,\n      sqft: 2800,\n      pricePerSqft: 1250,\n      neighborhood: 'Midtown',\n      images: ['/api/placeholder/400/300'],\n      addedDate: '2024-01-05T08:30:00Z',\n      lastViewed: '2024-01-19T11:20:00Z',\n      status: 'sold'\n    }\n  ]);\n\n  const filteredFavorites = favorites.filter(property => {\n    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         property.neighborhood.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = filterType === 'all' || property.type === filterType;\n    const matchesStatus = filterStatus === 'all' || property.status === filterStatus;\n    \n    return matchesSearch && matchesType && matchesStatus;\n  });\n\n  // Sort favorites\n  const sortedFavorites = [...filteredFavorites].sort((a, b) => {\n    switch (sortBy) {\n      case 'price-low':\n        return a.price - b.price;\n      case 'price-high':\n        return b.price - a.price;\n      case 'newest':\n        return new Date(b.addedDate).getTime() - new Date(a.addedDate).getTime();\n      case 'recently-viewed':\n        return new Date(b.lastViewed).getTime() - new Date(a.lastViewed).getTime();\n      default:\n        return 0;\n    }\n  });\n\n  const removeFavorite = (propertyId: string) => {\n    setFavorites(prev => prev.filter(property => property.id !== propertyId));\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'available':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'sold':\n        return 'bg-gray-100 text-gray-800';\n      case 'off-market':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    if (price >= 1000000) {\n      return `$${(price / 1000000).toFixed(1)}M`;\n    }\n    return `$${(price / 1000).toFixed(0)}K`;\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const totalFavorites = favorites.length;\n  const availableFavorites = favorites.filter(p => p.status === 'available').length;\n  const recentPriceChanges = favorites.filter(p => p.priceChange).length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Favorite Properties</h2>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => setViewMode('grid')}\n            className={`p-2 rounded-lg transition-colors ${\n              viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            <Grid3X3 className=\"w-4 h-4\" />\n          </button>\n          <button\n            onClick={() => setViewMode('list')}\n            className={`p-2 rounded-lg transition-colors ${\n              viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            <List className=\"w-4 h-4\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <Heart className=\"w-6 h-6 text-red-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Favorites</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalFavorites}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Available</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{availableFavorites}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <AlertCircle className=\"w-6 h-6 text-orange-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Price Changes</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{recentPriceChanges}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center\">\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search favorites...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          \n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Types</option>\n            <option value=\"apartment\">Apartment</option>\n            <option value=\"house\">House</option>\n            <option value=\"condo\">Condo</option>\n            <option value=\"townhouse\">Townhouse</option>\n          </select>\n\n          <select\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Status</option>\n            <option value=\"available\">Available</option>\n            <option value=\"pending\">Pending</option>\n            <option value=\"sold\">Sold</option>\n            <option value=\"off-market\">Off Market</option>\n          </select>\n\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"newest\">Recently Added</option>\n            <option value=\"recently-viewed\">Recently Viewed</option>\n            <option value=\"price-low\">Price: Low to High</option>\n            <option value=\"price-high\">Price: High to Low</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Favorites Grid/List */}\n      {viewMode === 'grid' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {sortedFavorites.map((property) => (\n            <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n              <div className=\"relative\">\n                <img\n                  src={property.images[0]}\n                  alt={property.title}\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"absolute top-4 left-4\">\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(property.status)}`}>\n                    {property.status}\n                  </span>\n                </div>\n                <div className=\"absolute top-4 right-4 flex space-x-2\">\n                  <button\n                    onClick={() => removeFavorite(property.id)}\n                    className=\"bg-white bg-opacity-90 p-2 rounded-full hover:bg-red-50 hover:text-red-600 transition-all\"\n                  >\n                    <X className=\"w-4 h-4\" />\n                  </button>\n                  <button className=\"bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all\">\n                    <Share2 className=\"w-4 h-4 text-gray-600\" />\n                  </button>\n                </div>\n                {property.priceChange && (\n                  <div className=\"absolute bottom-4 left-4\">\n                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                      property.priceChange.direction === 'down' \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      <TrendingUp className={`w-3 h-3 mr-1 ${\n                        property.priceChange.direction === 'down' ? 'rotate-180' : ''\n                      }`} />\n                      {property.priceChange.direction === 'down' ? '-' : '+'}${property.priceChange.amount.toLocaleString()}\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-3\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">{property.title}</h3>\n                    <p className=\"text-sm text-gray-600 flex items-center\">\n                      <MapPin className=\"w-4 h-4 mr-1\" />\n                      {property.neighborhood}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-xl font-bold text-gray-900\">{formatPrice(property.price)}</p>\n                    <p className=\"text-sm text-gray-600\">${property.pricePerSqft}/sqft</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-4 text-sm text-gray-600 mb-4\">\n                  <span className=\"flex items-center\">\n                    <Bed className=\"w-4 h-4 mr-1\" />\n                    {property.bedrooms} bed\n                  </span>\n                  <span className=\"flex items-center\">\n                    <Bath className=\"w-4 h-4 mr-1\" />\n                    {property.bathrooms} bath\n                  </span>\n                  <span className=\"flex items-center\">\n                    <Square className=\"w-4 h-4 mr-1\" />\n                    {property.sqft.toLocaleString()} sqft\n                  </span>\n                </div>\n\n                {property.notes && (\n                  <div className=\"bg-blue-50 rounded-lg p-3 mb-4\">\n                    <p className=\"text-sm text-blue-800\">{property.notes}</p>\n                  </div>\n                )}\n\n                <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                  <div className=\"text-xs text-gray-500\">\n                    <p>Added {formatDate(property.addedDate)}</p>\n                    <p>Viewed {formatDate(property.lastViewed)}</p>\n                  </div>\n                  <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center\">\n                    <ExternalLink className=\"w-4 h-4 mr-1\" />\n                    View\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\n          <div className=\"space-y-4 p-6\">\n            {sortedFavorites.map((property) => (\n              <div key={property.id} className=\"flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow\">\n                <img\n                  src={property.images[0]}\n                  alt={property.title}\n                  className=\"w-24 h-24 object-cover rounded-lg flex-shrink-0\"\n                />\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{property.title}</h3>\n                      <p className=\"text-sm text-gray-600 flex items-center mt-1\">\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        {property.address}\n                      </p>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600 mt-2\">\n                        <span>{property.bedrooms} bed</span>\n                        <span>{property.bathrooms} bath</span>\n                        <span>{property.sqft.toLocaleString()} sqft</span>\n                      </div>\n                      {property.priceChange && (\n                        <div className=\"mt-2\">\n                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                            property.priceChange.direction === 'down' \n                              ? 'bg-green-100 text-green-800' \n                              : 'bg-red-100 text-red-800'\n                          }`}>\n                            Price {property.priceChange.direction === 'down' ? 'decreased' : 'increased'} by ${property.priceChange.amount.toLocaleString()}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-xl font-bold text-gray-900\">{formatPrice(property.price)}</p>\n                      <p className=\"text-sm text-gray-600\">${property.pricePerSqft}/sqft</p>\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${getStatusColor(property.status)}`}>\n                        {property.status}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"flex flex-col items-end space-y-2\">\n                  <button\n                    onClick={() => removeFavorite(property.id)}\n                    className=\"text-gray-400 hover:text-red-600 p-1\"\n                  >\n                    <X className=\"w-4 h-4\" />\n                  </button>\n                  <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\">\n                    View Details\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {sortedFavorites.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <Heart className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No favorite properties found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm || filterType !== 'all' || filterStatus !== 'all'\n              ? 'Try adjusting your search or filters.'\n              : 'Start browsing properties and add them to your favorites to see them here.'\n            }\n          </p>\n          <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\">\n            Browse Properties\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAgDe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyD;IACpG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2D;IAC1G,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6D;IAChG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,sBAAsB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QAC7D;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,cAAc;YACd,aAAa;YACb,KAAK;YACL,cAAc;YACd,QAAQ;gBAAC;aAA2B;YACpC,WAAW;YACX,YAAY;YACZ,aAAa;gBACX,QAAQ;gBACR,WAAW;gBACX,MAAM;YACR;YACA,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,cAAc;YACd,aAAa;YACb,KAAK;YACL,cAAc;YACd,QAAQ;gBAAC;aAA2B;YACpC,WAAW;YACX,YAAY;YACZ,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,cAAc;YACd,aAAa;YACb,KAAK;YACL,cAAc;YACd,QAAQ;gBAAC;aAA2B;YACpC,WAAW;YACX,YAAY;YACZ,aAAa;gBACX,QAAQ;gBACR,WAAW;gBACX,MAAM;YACR;YACA,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,cAAc;YACd,cAAc;YACd,QAAQ;gBAAC;aAA2B;YACpC,WAAW;YACX,YAAY;YACZ,QAAQ;QACV;KACD;IAED,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,SAAS,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,SAAS,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACxF,MAAM,cAAc,eAAe,SAAS,SAAS,IAAI,KAAK;QAC9D,MAAM,gBAAgB,iBAAiB,SAAS,SAAS,MAAM,KAAK;QAEpE,OAAO,iBAAiB,eAAe;IACzC;IAEA,iBAAiB;IACjB,MAAM,kBAAkB;WAAI;KAAkB,CAAC,IAAI,CAAC,CAAC,GAAG;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAC1E;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;IAC/D;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,SAAS;YACpB,OAAO,AAAC,IAAgC,OAA7B,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,IAAG;QAC1C;QACA,OAAO,AAAC,IAA6B,OAA1B,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAG;IACvC;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,iBAAiB,UAAU,MAAM;IACvC,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IACjF,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;IAEtE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAW,AAAC,oCAEX,OADC,aAAa,SAAS,8BAA8B;0CAGtD,cAAA,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAErB,6LAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAW,AAAC,oCAEX,OADC,aAAa,SAAS,8BAA8B;0CAGtD,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAQ;;;;;;8CACtB,6LAAC;oCAAO,OAAM;8CAAY;;;;;;;;;;;;sCAG5B,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAa;;;;;;;;;;;;sCAG7B,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4BACzC,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAkB;;;;;;8CAChC,6LAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,6LAAC;oCAAO,OAAM;8CAAa;;;;;;;;;;;;;;;;;;;;;;;YAMhC,aAAa,uBACZ,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;wBAAsB,WAAU;;0CAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,SAAS,MAAM,CAAC,EAAE;wCACvB,KAAK,SAAS,KAAK;wCACnB,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAW,AAAC,2EAA0G,OAAhC,eAAe,SAAS,MAAM;sDACvH,SAAS,MAAM;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,eAAe,SAAS,EAAE;gDACzC,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAGrB,SAAS,WAAW,kBACnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,AAAC,uEAIhB,OAHC,SAAS,WAAW,CAAC,SAAS,KAAK,SAC/B,gCACA;;8DAEJ,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAW,AAAC,gBAEvB,OADC,SAAS,WAAW,CAAC,SAAS,KAAK,SAAS,eAAe;;;;;;gDAE5D,SAAS,WAAW,CAAC,SAAS,KAAK,SAAS,MAAM;gDAAI;gDAAE,SAAS,WAAW,CAAC,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;0CAM3G,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA4C,SAAS,KAAK;;;;;;kEACxE,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,SAAS,YAAY;;;;;;;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAmC,YAAY,SAAS,KAAK;;;;;;kEAC1E,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAE,SAAS,YAAY;4DAAC;;;;;;;;;;;;;;;;;;;kDAIjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,SAAS,QAAQ;oDAAC;;;;;;;0DAErB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,SAAS,SAAS;oDAAC;;;;;;;0DAEtB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,SAAS,IAAI,CAAC,cAAc;oDAAG;;;;;;;;;;;;;oCAInC,SAAS,KAAK,kBACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAyB,SAAS,KAAK;;;;;;;;;;;kDAIxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAE;4DAAO,WAAW,SAAS,SAAS;;;;;;;kEACvC,6LAAC;;4DAAE;4DAAQ,WAAW,SAAS,UAAU;;;;;;;;;;;;;0DAE3C,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAjFvC,SAAS,EAAE;;;;;;;;;qCA0FzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;4BAAsB,WAAU;;8CAC/B,6LAAC;oCACC,KAAK,SAAS,MAAM,CAAC,EAAE;oCACvB,KAAK,SAAS,KAAK;oCACnB,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAuC,SAAS,KAAK;;;;;;kEACnE,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,SAAS,OAAO;;;;;;;kEAEnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,SAAS,QAAQ;oEAAC;;;;;;;0EACzB,6LAAC;;oEAAM,SAAS,SAAS;oEAAC;;;;;;;0EAC1B,6LAAC;;oEAAM,SAAS,IAAI,CAAC,cAAc;oEAAG;;;;;;;;;;;;;oDAEvC,SAAS,WAAW,kBACnB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAW,AAAC,uEAIjB,OAHC,SAAS,WAAW,CAAC,SAAS,KAAK,SAC/B,gCACA;;gEACF;gEACK,SAAS,WAAW,CAAC,SAAS,KAAK,SAAS,cAAc;gEAAY;gEAAM,SAAS,WAAW,CAAC,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;0DAKrI,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAmC,YAAY,SAAS,KAAK;;;;;;kEAC1E,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAE,SAAS,YAAY;4DAAC;;;;;;;kEAC7D,6LAAC;wDAAK,WAAW,AAAC,gFAA+G,OAAhC,eAAe,SAAS,MAAM;kEAC5H,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe,SAAS,EAAE;4CACzC,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;sDAEf,6LAAC;4CAAO,WAAU;sDAAsG;;;;;;;;;;;;;2BA/ClH,SAAS,EAAE;;;;;;;;;;;;;;;YAyD5B,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,eAAe,SAAS,iBAAiB,QACpD,0CACA;;;;;;kCAGN,6LAAC;wBAAO,WAAU;kCAA8F;;;;;;;;;;;;;;;;;;AAO1H;GA1bwB;KAAA", "debugId": null}}, {"offset": {"line": 10900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/OffersPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  HandCoins,\n  DollarSign,\n  Calendar,\n  Clock,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  Eye,\n  Edit,\n  MessageSquare,\n  TrendingUp,\n  TrendingDown,\n  MapPin,\n  Bed,\n  Bath,\n  Square,\n  Filter,\n  Search,\n  Plus,\n  FileText,\n  Phone,\n  Mail\n} from 'lucide-react';\n\ninterface Offer {\n  id: string;\n  propertyId: string;\n  propertyTitle: string;\n  propertyAddress: string;\n  propertyImage: string;\n  propertyDetails: {\n    bedrooms: number;\n    bathrooms: number;\n    sqft: number;\n    listingPrice: number;\n  };\n  offerAmount: number;\n  offerDate: string;\n  expirationDate: string;\n  status: 'pending' | 'accepted' | 'rejected' | 'countered' | 'withdrawn' | 'expired';\n  counterOffer?: {\n    amount: number;\n    date: string;\n    message?: string;\n  };\n  contingencies: string[];\n  financing: {\n    type: 'cash' | 'conventional' | 'fha' | 'va';\n    downPayment: number;\n    preApproved: boolean;\n  };\n  closingDate: string;\n  agent: {\n    name: string;\n    phone: string;\n    email: string;\n  };\n  notes?: string;\n  lastActivity: string;\n}\n\nexport default function OffersPanel() {\n  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'accepted' | 'rejected' | 'countered' | 'withdrawn' | 'expired'>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState<'newest' | 'amount-high' | 'amount-low' | 'expiring-soon'>('newest');\n\n  // Mock offers data\n  const [offers] = useState<Offer[]>([\n    {\n      id: '1',\n      propertyId: 'prop-1',\n      propertyTitle: 'Modern Downtown Loft',\n      propertyAddress: '456 Broadway, Downtown, NY 10013',\n      propertyImage: '/api/placeholder/300/200',\n      propertyDetails: {\n        bedrooms: 2,\n        bathrooms: 2,\n        sqft: 1400,\n        listingPrice: 850000\n      },\n      offerAmount: 825000,\n      offerDate: '2024-01-20T10:00:00Z',\n      expirationDate: '2024-01-27T23:59:59Z',\n      status: 'countered',\n      counterOffer: {\n        amount: 840000,\n        date: '2024-01-22T14:30:00Z',\n        message: 'We can meet in the middle at $840K. Property is in excellent condition.'\n      },\n      contingencies: ['Inspection', 'Financing', 'Appraisal'],\n      financing: {\n        type: 'conventional',\n        downPayment: 170000,\n        preApproved: true\n      },\n      closingDate: '2024-03-15T00:00:00Z',\n      agent: {\n        name: 'Sarah Johnson',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      notes: 'Seller is motivated. Property has been on market for 45 days.',\n      lastActivity: '2024-01-22T14:30:00Z'\n    },\n    {\n      id: '2',\n      propertyId: 'prop-2',\n      propertyTitle: 'Charming Brooklyn Townhouse',\n      propertyAddress: '789 Park Slope Ave, Brooklyn, NY 11215',\n      propertyImage: '/api/placeholder/300/200',\n      propertyDetails: {\n        bedrooms: 4,\n        bathrooms: 3,\n        sqft: 2200,\n        listingPrice: 1200000\n      },\n      offerAmount: 1150000,\n      offerDate: '2024-01-18T16:45:00Z',\n      expirationDate: '2024-01-25T23:59:59Z',\n      status: 'pending',\n      contingencies: ['Inspection', 'Financing'],\n      financing: {\n        type: 'conventional',\n        downPayment: 230000,\n        preApproved: true\n      },\n      closingDate: '2024-03-01T00:00:00Z',\n      agent: {\n        name: 'Mike Chen',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      lastActivity: '2024-01-18T16:45:00Z'\n    },\n    {\n      id: '3',\n      propertyId: 'prop-3',\n      propertyTitle: 'Cozy Queens Family Home',\n      propertyAddress: '654 Maple St, Astoria, NY 11106',\n      propertyImage: '/api/placeholder/300/200',\n      propertyDetails: {\n        bedrooms: 3,\n        bathrooms: 2,\n        sqft: 1600,\n        listingPrice: 650000\n      },\n      offerAmount: 635000,\n      offerDate: '2024-01-15T12:20:00Z',\n      expirationDate: '2024-01-22T23:59:59Z',\n      status: 'accepted',\n      contingencies: ['Inspection'],\n      financing: {\n        type: 'fha',\n        downPayment: 22225,\n        preApproved: true\n      },\n      closingDate: '2024-02-28T00:00:00Z',\n      agent: {\n        name: 'David Kim',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      notes: 'Great deal! Under asking price.',\n      lastActivity: '2024-01-16T09:30:00Z'\n    },\n    {\n      id: '4',\n      propertyId: 'prop-4',\n      propertyTitle: 'Manhattan Studio Apartment',\n      propertyAddress: '123 E 42nd St, Midtown, NY 10017',\n      propertyImage: '/api/placeholder/300/200',\n      propertyDetails: {\n        bedrooms: 0,\n        bathrooms: 1,\n        sqft: 500,\n        listingPrice: 450000\n      },\n      offerAmount: 420000,\n      offerDate: '2024-01-10T14:15:00Z',\n      expirationDate: '2024-01-17T23:59:59Z',\n      status: 'rejected',\n      contingencies: ['Financing'],\n      financing: {\n        type: 'cash',\n        downPayment: 420000,\n        preApproved: true\n      },\n      closingDate: '2024-02-15T00:00:00Z',\n      agent: {\n        name: 'Lisa Wang',\n        phone: '(*************',\n        email: '<EMAIL>'\n      },\n      notes: 'Offer was too low. Seller not negotiating.',\n      lastActivity: '2024-01-12T11:00:00Z'\n    }\n  ]);\n\n  const filteredOffers = offers.filter(offer => {\n    const matchesSearch = offer.propertyTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         offer.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || offer.status === filterStatus;\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  // Sort offers\n  const sortedOffers = [...filteredOffers].sort((a, b) => {\n    switch (sortBy) {\n      case 'amount-high':\n        return b.offerAmount - a.offerAmount;\n      case 'amount-low':\n        return a.offerAmount - b.offerAmount;\n      case 'newest':\n        return new Date(b.offerDate).getTime() - new Date(a.offerDate).getTime();\n      case 'expiring-soon':\n        return new Date(a.expirationDate).getTime() - new Date(b.expirationDate).getTime();\n      default:\n        return 0;\n    }\n  });\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'accepted':\n        return 'bg-green-100 text-green-800';\n      case 'rejected':\n        return 'bg-red-100 text-red-800';\n      case 'countered':\n        return 'bg-blue-100 text-blue-800';\n      case 'withdrawn':\n        return 'bg-gray-100 text-gray-800';\n      case 'expired':\n        return 'bg-orange-100 text-orange-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"w-4 h-4\" />;\n      case 'accepted':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      case 'rejected':\n        return <XCircle className=\"w-4 h-4\" />;\n      case 'countered':\n        return <MessageSquare className=\"w-4 h-4\" />;\n      case 'withdrawn':\n        return <XCircle className=\"w-4 h-4\" />;\n      case 'expired':\n        return <AlertCircle className=\"w-4 h-4\" />;\n      default:\n        return <Clock className=\"w-4 h-4\" />;\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    return `$${price.toLocaleString()}`;\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  };\n\n  const getDaysUntilExpiration = (expirationDate: string) => {\n    const now = new Date();\n    const expiry = new Date(expirationDate);\n    const diffTime = expiry.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n\n  const totalOffers = offers.length;\n  const pendingOffers = offers.filter(o => o.status === 'pending').length;\n  const acceptedOffers = offers.filter(o => o.status === 'accepted').length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">My Offers</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Make Offer\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <HandCoins className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Offers</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalOffers}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <Clock className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{pendingOffers}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CheckCircle className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Accepted</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{acceptedOffers}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center\">\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search offers...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          \n          <select\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Status</option>\n            <option value=\"pending\">Pending</option>\n            <option value=\"accepted\">Accepted</option>\n            <option value=\"rejected\">Rejected</option>\n            <option value=\"countered\">Countered</option>\n            <option value=\"withdrawn\">Withdrawn</option>\n            <option value=\"expired\">Expired</option>\n          </select>\n\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"newest\">Newest First</option>\n            <option value=\"amount-high\">Highest Amount</option>\n            <option value=\"amount-low\">Lowest Amount</option>\n            <option value=\"expiring-soon\">Expiring Soon</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Offers List */}\n      <div className=\"space-y-4\">\n        {sortedOffers.map((offer) => {\n          const daysUntilExpiration = getDaysUntilExpiration(offer.expirationDate);\n          const isExpiringSoon = daysUntilExpiration <= 2 && daysUntilExpiration > 0;\n          const isExpired = daysUntilExpiration <= 0;\n\n          return (\n            <div key={offer.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"flex items-start space-x-4\">\n                <img\n                  src={offer.propertyImage}\n                  alt={offer.propertyTitle}\n                  className=\"w-24 h-24 object-cover rounded-lg flex-shrink-0\"\n                />\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">{offer.propertyTitle}</h3>\n                      <p className=\"text-sm text-gray-600 flex items-center\">\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        {offer.propertyAddress}\n                      </p>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(offer.status)}`}>\n                        {getStatusIcon(offer.status)}\n                        <span className=\"ml-1 capitalize\">{offer.status}</span>\n                      </span>\n                      {isExpiringSoon && (\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\">\n                          <AlertCircle className=\"w-3 h-3 mr-1\" />\n                          Expires in {daysUntilExpiration} day{daysUntilExpiration !== 1 ? 's' : ''}\n                        </span>\n                      )}\n                      {isExpired && (\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                          Expired\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Listing Price</p>\n                      <p className=\"font-semibold\">{formatPrice(offer.propertyDetails.listingPrice)}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Your Offer</p>\n                      <p className=\"font-semibold text-blue-600\">{formatPrice(offer.offerAmount)}</p>\n                    </div>\n                    {offer.counterOffer && (\n                      <div>\n                        <p className=\"text-sm text-gray-500\">Counter Offer</p>\n                        <p className=\"font-semibold text-orange-600\">{formatPrice(offer.counterOffer.amount)}</p>\n                      </div>\n                    )}\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Financing</p>\n                      <p className=\"font-semibold capitalize\">{offer.financing.type}</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-6 text-sm text-gray-600 mb-4\">\n                    <span className=\"flex items-center\">\n                      <Bed className=\"w-4 h-4 mr-1\" />\n                      {offer.propertyDetails.bedrooms} bed\n                    </span>\n                    <span className=\"flex items-center\">\n                      <Bath className=\"w-4 h-4 mr-1\" />\n                      {offer.propertyDetails.bathrooms} bath\n                    </span>\n                    <span className=\"flex items-center\">\n                      <Square className=\"w-4 h-4 mr-1\" />\n                      {offer.propertyDetails.sqft.toLocaleString()} sqft\n                    </span>\n                    <span className=\"flex items-center\">\n                      <Calendar className=\"w-4 h-4 mr-1\" />\n                      Close: {formatDate(offer.closingDate)}\n                    </span>\n                  </div>\n\n                  {offer.counterOffer?.message && (\n                    <div className=\"bg-blue-50 rounded-lg p-3 mb-4\">\n                      <p className=\"text-sm font-medium text-blue-900 mb-1\">Counter Offer Message:</p>\n                      <p className=\"text-sm text-blue-800\">{offer.counterOffer.message}</p>\n                    </div>\n                  )}\n\n                  {offer.notes && (\n                    <div className=\"bg-gray-50 rounded-lg p-3 mb-4\">\n                      <p className=\"text-sm text-gray-700\">{offer.notes}</p>\n                    </div>\n                  )}\n\n                  <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <span>Offered: {formatDate(offer.offerDate)}</span>\n                      <span>Expires: {formatDate(offer.expirationDate)}</span>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                        <Eye className=\"w-4 h-4\" />\n                      </button>\n                      {(offer.status === 'pending' || offer.status === 'countered') && (\n                        <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                          <Edit className=\"w-4 h-4\" />\n                        </button>\n                      )}\n                      <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                        <Phone className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {sortedOffers.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <HandCoins className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No offers found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm || filterStatus !== 'all'\n              ? 'Try adjusting your search or filters.'\n              : 'You haven\\'t made any offers yet. Start browsing properties to make your first offer.'\n            }\n          </p>\n          <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\">\n            Browse Properties\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiEe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuF;IACtI,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6D;IAEhG,mBAAmB;IACnB,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;QACjC;YACE,IAAI;YACJ,YAAY;YACZ,eAAe;YACf,iBAAiB;YACjB,eAAe;YACf,iBAAiB;gBACf,UAAU;gBACV,WAAW;gBACX,MAAM;gBACN,cAAc;YAChB;YACA,aAAa;YACb,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,cAAc;gBACZ,QAAQ;gBACR,MAAM;gBACN,SAAS;YACX;YACA,eAAe;gBAAC;gBAAc;gBAAa;aAAY;YACvD,WAAW;gBACT,MAAM;gBACN,aAAa;gBACb,aAAa;YACf;YACA,aAAa;YACb,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,eAAe;YACf,iBAAiB;YACjB,eAAe;YACf,iBAAiB;gBACf,UAAU;gBACV,WAAW;gBACX,MAAM;gBACN,cAAc;YAChB;YACA,aAAa;YACb,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,eAAe;gBAAC;gBAAc;aAAY;YAC1C,WAAW;gBACT,MAAM;gBACN,aAAa;gBACb,aAAa;YACf;YACA,aAAa;YACb,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,eAAe;YACf,iBAAiB;YACjB,eAAe;YACf,iBAAiB;gBACf,UAAU;gBACV,WAAW;gBACX,MAAM;gBACN,cAAc;YAChB;YACA,aAAa;YACb,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,eAAe;gBAAC;aAAa;YAC7B,WAAW;gBACT,MAAM;gBACN,aAAa;gBACb,aAAa;YACf;YACA,aAAa;YACb,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,OAAO;YACP,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,eAAe;YACf,iBAAiB;YACjB,eAAe;YACf,iBAAiB;gBACf,UAAU;gBACV,WAAW;gBACX,MAAM;gBACN,cAAc;YAChB;YACA,aAAa;YACb,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,eAAe;gBAAC;aAAY;YAC5B,WAAW;gBACT,MAAM;gBACN,aAAa;gBACb,aAAa;YACf;YACA,aAAa;YACb,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,OAAO;YACP,cAAc;QAChB;KACD;IAED,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,MAAM,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,MAAM,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACxF,MAAM,gBAAgB,iBAAiB,SAAS,MAAM,MAAM,KAAK;QAEjE,OAAO,iBAAiB;IAC1B;IAEA,cAAc;IACd,MAAM,eAAe;WAAI;KAAe,CAAC,IAAI,CAAC,CAAC,GAAG;QAChD,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;YACtC,KAAK;gBACH,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;YACtC,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO;YAClF;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,AAAC,IAA0B,OAAvB,MAAM,cAAc;IACjC;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,KAAK;QACxB,MAAM,WAAW,OAAO,OAAO,KAAK,IAAI,OAAO;QAC/C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO;IACT;IAEA,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IACvE,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;IAEzE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,6LAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,6LAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,6LAAC;oCAAO,OAAM;8CAAY;;;;;;8CAC1B,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;sCAG1B,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4BACzC,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAc;;;;;;8CAC5B,6LAAC;oCAAO,OAAM;8CAAa;;;;;;8CAC3B,6LAAC;oCAAO,OAAM;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC;wBAkFR;oBAjFT,MAAM,sBAAsB,uBAAuB,MAAM,cAAc;oBACvE,MAAM,iBAAiB,uBAAuB,KAAK,sBAAsB;oBACzE,MAAM,YAAY,uBAAuB;oBAEzC,qBACE,6LAAC;wBAAmB,WAAU;kCAC5B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAK,MAAM,aAAa;oCACxB,KAAK,MAAM,aAAa;oCACxB,WAAU;;;;;;8CAGZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4C,MAAM,aAAa;;;;;;sEAC7E,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,MAAM,eAAe;;;;;;;;;;;;;8DAG1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,AAAC,2EAAuG,OAA7B,eAAe,MAAM,MAAM;;gEACpH,cAAc,MAAM,MAAM;8EAC3B,6LAAC;oEAAK,WAAU;8EAAmB,MAAM,MAAM;;;;;;;;;;;;wDAEhD,gCACC,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAiB;gEAC5B;gEAAoB;gEAAK,wBAAwB,IAAI,MAAM;;;;;;;wDAG1E,2BACC,6LAAC;4DAAK,WAAU;sEAAkG;;;;;;;;;;;;;;;;;;sDAOxH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAiB,YAAY,MAAM,eAAe,CAAC,YAAY;;;;;;;;;;;;8DAE9E,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAA+B,YAAY,MAAM,WAAW;;;;;;;;;;;;gDAE1E,MAAM,YAAY,kBACjB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAAiC,YAAY,MAAM,YAAY,CAAC,MAAM;;;;;;;;;;;;8DAGvF,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;sEAA4B,MAAM,SAAS,CAAC,IAAI;;;;;;;;;;;;;;;;;;sDAIjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDACd,MAAM,eAAe,CAAC,QAAQ;wDAAC;;;;;;;8DAElC,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,MAAM,eAAe,CAAC,SAAS;wDAAC;;;;;;;8DAEnC,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,MAAM,eAAe,CAAC,IAAI,CAAC,cAAc;wDAAG;;;;;;;8DAE/C,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;wDAC7B,WAAW,MAAM,WAAW;;;;;;;;;;;;;wCAIvC,EAAA,sBAAA,MAAM,YAAY,cAAlB,0CAAA,oBAAoB,OAAO,mBAC1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAyB,MAAM,YAAY,CAAC,OAAO;;;;;;;;;;;;wCAInE,MAAM,KAAK,kBACV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAyB,MAAM,KAAK;;;;;;;;;;;sDAIrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAK;gEAAU,WAAW,MAAM,SAAS;;;;;;;sEAC1C,6LAAC;;gEAAK;gEAAU,WAAW,MAAM,cAAc;;;;;;;;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;wDAEhB,CAAC,MAAM,MAAM,KAAK,aAAa,MAAM,MAAM,KAAK,WAAW,mBAC1D,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAGpB,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAxGnB,MAAM,EAAE;;;;;gBAgHtB;;;;;;YAGD,aAAa,MAAM,KAAK,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,iBAAiB,QAC5B,0CACA;;;;;;kCAGN,6LAAC;wBAAO,WAAU;kCAA8F;;;;;;;;;;;;;;;;;;AAO1H;GAtcwB;KAAA", "debugId": null}}, {"offset": {"line": 12076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/TenantsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Users,\n  User,\n  Phone,\n  Mail,\n  MapPin,\n  Calendar,\n  DollarSign,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  FileText,\n  MessageSquare,\n  Plus,\n  Search,\n  Filter,\n  Eye,\n  Edit,\n  Star,\n  TrendingUp,\n  Home,\n  CreditCard,\n  Shield\n} from 'lucide-react';\n\ninterface Tenant {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  avatar?: string;\n  propertyId: string;\n  propertyAddress: string;\n  leaseStart: string;\n  leaseEnd: string;\n  monthlyRent: number;\n  securityDeposit: number;\n  paymentStatus: 'current' | 'late' | 'overdue';\n  lastPayment: string;\n  nextPayment: string;\n  leaseStatus: 'active' | 'expiring' | 'expired' | 'terminated';\n  rating: number;\n  notes?: string;\n  emergencyContact: {\n    name: string;\n    phone: string;\n    relationship: string;\n  };\n  maintenanceRequests: number;\n  paymentHistory: {\n    onTime: number;\n    late: number;\n    total: number;\n  };\n}\n\nexport default function TenantsPanel() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'current' | 'late' | 'overdue'>('all');\n  const [filterLease, setFilterLease] = useState<'all' | 'active' | 'expiring' | 'expired'>('all');\n  const [sortBy, setSortBy] = useState<'name' | 'property' | 'rent' | 'lease-end'>('name');\n\n  // Mock tenants data\n  const [tenants] = useState<Tenant[]>([\n    {\n      id: '1',\n      name: 'John Smith',\n      email: '<EMAIL>',\n      phone: '(*************',\n      avatar: '/api/placeholder/150/150',\n      propertyId: 'prop-1',\n      propertyAddress: '123 Main St, Downtown, NY 10001',\n      leaseStart: '2023-06-01T00:00:00Z',\n      leaseEnd: '2024-05-31T23:59:59Z',\n      monthlyRent: 2800,\n      securityDeposit: 2800,\n      paymentStatus: 'current',\n      lastPayment: '2024-01-01T00:00:00Z',\n      nextPayment: '2024-02-01T00:00:00Z',\n      leaseStatus: 'expiring',\n      rating: 5,\n      notes: 'Excellent tenant, always pays on time. Very clean and respectful.',\n      emergencyContact: {\n        name: 'Jane Smith',\n        phone: '(*************',\n        relationship: 'Spouse'\n      },\n      maintenanceRequests: 2,\n      paymentHistory: {\n        onTime: 8,\n        late: 0,\n        total: 8\n      }\n    },\n    {\n      id: '2',\n      name: 'Sarah Johnson',\n      email: '<EMAIL>',\n      phone: '(*************',\n      propertyId: 'prop-2',\n      propertyAddress: '789 Park Blvd, Uptown, NY 10003',\n      leaseStart: '2023-09-15T00:00:00Z',\n      leaseEnd: '2024-09-14T23:59:59Z',\n      monthlyRent: 4200,\n      securityDeposit: 4200,\n      paymentStatus: 'current',\n      lastPayment: '2024-01-15T00:00:00Z',\n      nextPayment: '2024-02-15T00:00:00Z',\n      leaseStatus: 'active',\n      rating: 4,\n      emergencyContact: {\n        name: 'Mike Johnson',\n        phone: '(*************',\n        relationship: 'Brother'\n      },\n      maintenanceRequests: 1,\n      paymentHistory: {\n        onTime: 4,\n        late: 0,\n        total: 4\n      }\n    },\n    {\n      id: '3',\n      name: 'Mike Chen',\n      email: '<EMAIL>',\n      phone: '(*************',\n      propertyId: 'prop-3',\n      propertyAddress: '321 Elm St, Midtown, NY 10004',\n      leaseStart: '2023-03-01T00:00:00Z',\n      leaseEnd: '2024-02-29T23:59:59Z',\n      monthlyRent: 3200,\n      securityDeposit: 3200,\n      paymentStatus: 'late',\n      lastPayment: '2023-12-28T00:00:00Z',\n      nextPayment: '2024-01-01T00:00:00Z',\n      leaseStatus: 'expiring',\n      rating: 3,\n      notes: 'Generally good tenant but occasionally late with payments.',\n      emergencyContact: {\n        name: 'Lisa Chen',\n        phone: '(*************',\n        relationship: 'Sister'\n      },\n      maintenanceRequests: 4,\n      paymentHistory: {\n        onTime: 8,\n        late: 2,\n        total: 10\n      }\n    },\n    {\n      id: '4',\n      name: 'Emily Rodriguez',\n      email: '<EMAIL>',\n      phone: '(*************',\n      propertyId: 'prop-4',\n      propertyAddress: '654 Oak Ave, Suburbia, NY 10002',\n      leaseStart: '2023-12-01T00:00:00Z',\n      leaseEnd: '2024-11-30T23:59:59Z',\n      monthlyRent: 2500,\n      securityDeposit: 2500,\n      paymentStatus: 'overdue',\n      lastPayment: '2023-11-28T00:00:00Z',\n      nextPayment: '2023-12-01T00:00:00Z',\n      leaseStatus: 'active',\n      rating: 2,\n      notes: 'Payment issues. Need to follow up regularly.',\n      emergencyContact: {\n        name: 'Carlos Rodriguez',\n        phone: '(*************',\n        relationship: 'Father'\n      },\n      maintenanceRequests: 6,\n      paymentHistory: {\n        onTime: 1,\n        late: 1,\n        total: 2\n      }\n    }\n  ]);\n\n  const filteredTenants = tenants.filter(tenant => {\n    const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tenant.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tenant.email.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || tenant.paymentStatus === filterStatus;\n    const matchesLease = filterLease === 'all' || tenant.leaseStatus === filterLease;\n    \n    return matchesSearch && matchesStatus && matchesLease;\n  });\n\n  // Sort tenants\n  const sortedTenants = [...filteredTenants].sort((a, b) => {\n    switch (sortBy) {\n      case 'name':\n        return a.name.localeCompare(b.name);\n      case 'property':\n        return a.propertyAddress.localeCompare(b.propertyAddress);\n      case 'rent':\n        return b.monthlyRent - a.monthlyRent;\n      case 'lease-end':\n        return new Date(a.leaseEnd).getTime() - new Date(b.leaseEnd).getTime();\n      default:\n        return 0;\n    }\n  });\n\n  const getPaymentStatusColor = (status: string) => {\n    switch (status) {\n      case 'current':\n        return 'bg-green-100 text-green-800';\n      case 'late':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'overdue':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getLeaseStatusColor = (status: string) => {\n    switch (status) {\n      case 'active':\n        return 'bg-blue-100 text-blue-800';\n      case 'expiring':\n        return 'bg-orange-100 text-orange-800';\n      case 'expired':\n        return 'bg-red-100 text-red-800';\n      case 'terminated':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getPaymentStatusIcon = (status: string) => {\n    switch (status) {\n      case 'current':\n        return <CheckCircle className=\"w-4 h-4\" />;\n      case 'late':\n        return <Clock className=\"w-4 h-4\" />;\n      case 'overdue':\n        return <AlertTriangle className=\"w-4 h-4\" />;\n      default:\n        return <Clock className=\"w-4 h-4\" />;\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  };\n\n  const getDaysUntilLeaseEnd = (leaseEnd: string) => {\n    const now = new Date();\n    const end = new Date(leaseEnd);\n    const diffTime = end.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n\n  const totalTenants = tenants.length;\n  const currentTenants = tenants.filter(t => t.paymentStatus === 'current').length;\n  const latePayments = tenants.filter(t => t.paymentStatus === 'late' || t.paymentStatus === 'overdue').length;\n  const expiringLeases = tenants.filter(t => {\n    const daysUntilEnd = getDaysUntilLeaseEnd(t.leaseEnd);\n    return daysUntilEnd <= 60 && daysUntilEnd > 0;\n  }).length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Tenant Management</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Add Tenant\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Users className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Tenants</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalTenants}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CheckCircle className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Current Payments</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{currentTenants}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <AlertTriangle className=\"w-6 h-6 text-red-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Late Payments</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{latePayments}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <Calendar className=\"w-6 h-6 text-orange-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Expiring Leases</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{expiringLeases}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center\">\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search tenants...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          \n          <select\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Payment Status</option>\n            <option value=\"current\">Current</option>\n            <option value=\"late\">Late</option>\n            <option value=\"overdue\">Overdue</option>\n          </select>\n\n          <select\n            value={filterLease}\n            onChange={(e) => setFilterLease(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Lease Status</option>\n            <option value=\"active\">Active</option>\n            <option value=\"expiring\">Expiring</option>\n            <option value=\"expired\">Expired</option>\n          </select>\n\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"name\">Sort by Name</option>\n            <option value=\"property\">Sort by Property</option>\n            <option value=\"rent\">Sort by Rent</option>\n            <option value=\"lease-end\">Sort by Lease End</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Tenants List */}\n      <div className=\"space-y-4\">\n        {sortedTenants.map((tenant) => {\n          const daysUntilLeaseEnd = getDaysUntilLeaseEnd(tenant.leaseEnd);\n          const isLeaseExpiringSoon = daysUntilLeaseEnd <= 60 && daysUntilLeaseEnd > 0;\n\n          return (\n            <div key={tenant.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-shrink-0\">\n                  {tenant.avatar ? (\n                    <img\n                      src={tenant.avatar}\n                      alt={tenant.name}\n                      className=\"w-16 h-16 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center\">\n                      <User className=\"w-8 h-8 text-gray-400\" />\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">{tenant.name}</h3>\n                      <p className=\"text-sm text-gray-600 flex items-center mb-1\">\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        {tenant.propertyAddress}\n                      </p>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                        <span className=\"flex items-center\">\n                          <Mail className=\"w-4 h-4 mr-1\" />\n                          {tenant.email}\n                        </span>\n                        <span className=\"flex items-center\">\n                          <Phone className=\"w-4 h-4 mr-1\" />\n                          {tenant.phone}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(tenant.paymentStatus)}`}>\n                        {getPaymentStatusIcon(tenant.paymentStatus)}\n                        <span className=\"ml-1 capitalize\">{tenant.paymentStatus}</span>\n                      </span>\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLeaseStatusColor(tenant.leaseStatus)}`}>\n                        {tenant.leaseStatus}\n                      </span>\n                      {isLeaseExpiringSoon && (\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\">\n                          <AlertTriangle className=\"w-3 h-3 mr-1\" />\n                          Expires in {daysUntilLeaseEnd} days\n                        </span>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Monthly Rent</p>\n                      <p className=\"font-semibold\">${tenant.monthlyRent.toLocaleString()}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Lease Period</p>\n                      <p className=\"font-semibold\">{formatDate(tenant.leaseStart)} - {formatDate(tenant.leaseEnd)}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Payment History</p>\n                      <p className=\"font-semibold\">{tenant.paymentHistory.onTime}/{tenant.paymentHistory.total} on time</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Rating</p>\n                      <div className=\"flex items-center\">\n                        {[...Array(5)].map((_, i) => (\n                          <Star\n                            key={i}\n                            className={`w-4 h-4 ${\n                              i < tenant.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'\n                            }`}\n                          />\n                        ))}\n                        <span className=\"ml-1 text-sm font-semibold\">{tenant.rating}/5</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {tenant.notes && (\n                    <div className=\"bg-blue-50 rounded-lg p-3 mb-4\">\n                      <p className=\"text-sm text-blue-800\">{tenant.notes}</p>\n                    </div>\n                  )}\n\n                  <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <span>Maintenance: {tenant.maintenanceRequests} requests</span>\n                      <span>Next payment: {formatDate(tenant.nextPayment)}</span>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                        <Eye className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                        <MessageSquare className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors\">\n                        <Edit className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {sortedTenants.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <Users className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No tenants found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm || filterStatus !== 'all' || filterLease !== 'all'\n              ? 'Try adjusting your search or filters.'\n              : 'No tenants have been added yet.'\n            }\n          </p>\n          <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\">\n            Add First Tenant\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA2De,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IACzF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAC1F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C;IAEjF,oBAAoB;IACpB,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACnC;YACE,IAAI;YAC<PERSON>,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,aAAa;YACb,aAAa;YACb,aAAa;YACb,QAAQ;YACR,OAAO;YACP,kBAAkB;gBAChB,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA,qBAAqB;YACrB,gBAAgB;gBACd,QAAQ;gBACR,MAAM;gBACN,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,aAAa;YACb,aAAa;YACb,aAAa;YACb,QAAQ;YACR,kBAAkB;gBAChB,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA,qBAAqB;YACrB,gBAAgB;gBACd,QAAQ;gBACR,MAAM;gBACN,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,aAAa;YACb,aAAa;YACb,aAAa;YACb,QAAQ;YACR,OAAO;YACP,kBAAkB;gBAChB,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA,qBAAqB;YACrB,gBAAgB;gBACd,QAAQ;gBACR,MAAM;gBACN,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,iBAAiB;YACjB,YAAY;YACZ,UAAU;YACV,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,aAAa;YACb,aAAa;YACb,aAAa;YACb,QAAQ;YACR,OAAO;YACP,kBAAkB;gBAChB,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA,qBAAqB;YACrB,gBAAgB;gBACd,QAAQ;gBACR,MAAM;gBACN,OAAO;YACT;QACF;KACD;IAED,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,OAAO,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpE,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC/E,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,aAAa,KAAK;QACzE,MAAM,eAAe,gBAAgB,SAAS,OAAO,WAAW,KAAK;QAErE,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,eAAe;IACf,MAAM,gBAAgB;WAAI;KAAgB,CAAC,IAAI,CAAC,CAAC,GAAG;QAClD,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,EAAE,eAAe,CAAC,aAAa,CAAC,EAAE,eAAe;YAC1D,KAAK;gBACH,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;YACtC,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;YACtE;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,MAAM,IAAI;QAChB,MAAM,MAAM,IAAI,KAAK;QACrB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO;QAC5C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO;IACT;IAEA,MAAM,eAAe,QAAQ,MAAM;IACnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,WAAW,MAAM;IAChF,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,UAAU,EAAE,aAAa,KAAK,WAAW,MAAM;IAC5G,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA;QACpC,MAAM,eAAe,qBAAqB,EAAE,QAAQ;QACpD,OAAO,gBAAgB,MAAM,eAAe;IAC9C,GAAG,MAAM;IAET,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAId,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;8CACxB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;sCAG1B,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,6LAAC;oCAAO,OAAM;8CAAS;;;;;;8CACvB,6LAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,6LAAC;oCAAO,OAAM;8CAAU;;;;;;;;;;;;sCAG1B,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4BACzC,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAW;;;;;;8CACzB,6LAAC;oCAAO,OAAM;8CAAO;;;;;;8CACrB,6LAAC;oCAAO,OAAM;8CAAY;;;;;;;;;;;;;;;;;;;;;;;0BAMhC,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC;oBAClB,MAAM,oBAAoB,qBAAqB,OAAO,QAAQ;oBAC9D,MAAM,sBAAsB,qBAAqB,MAAM,oBAAoB;oBAE3E,qBACE,6LAAC;wBAAoB,WAAU;kCAC7B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,iBACZ,6LAAC;wCACC,KAAK,OAAO,MAAM;wCAClB,KAAK,OAAO,IAAI;wCAChB,WAAU;;;;;6DAGZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4C,OAAO,IAAI;;;;;;sEACrE,6LAAC;4DAAE,WAAU;;8EACX,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,OAAO,eAAe;;;;;;;sEAEzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEACf,OAAO,KAAK;;;;;;;8EAEf,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;8DAInB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,AAAC,2EAAsH,OAA5C,sBAAsB,OAAO,aAAa;;gEACnI,qBAAqB,OAAO,aAAa;8EAC1C,6LAAC;oEAAK,WAAU;8EAAmB,OAAO,aAAa;;;;;;;;;;;;sEAEzD,6LAAC;4DAAK,WAAW,AAAC,2EAAkH,OAAxC,oBAAoB,OAAO,WAAW;sEAC/H,OAAO,WAAW;;;;;;wDAEpB,qCACC,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAiB;gEAC9B;gEAAkB;;;;;;;;;;;;;;;;;;;sDAMtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEAAgB;gEAAE,OAAO,WAAW,CAAC,cAAc;;;;;;;;;;;;;8DAElE,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEAAiB,WAAW,OAAO,UAAU;gEAAE;gEAAI,WAAW,OAAO,QAAQ;;;;;;;;;;;;;8DAE5F,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAE,WAAU;;gEAAiB,OAAO,cAAc,CAAC,MAAM;gEAAC;gEAAE,OAAO,cAAc,CAAC,KAAK;gEAAC;;;;;;;;;;;;;8DAE3F,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC;4DAAI,WAAU;;gEACZ;uEAAI,MAAM;iEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wEAEH,WAAW,AAAC,WAEX,OADC,IAAI,OAAO,MAAM,GAAG,iCAAiC;uEAFlD;;;;;8EAMT,6LAAC;oEAAK,WAAU;;wEAA8B,OAAO,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;wCAKjE,OAAO,KAAK,kBACX,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAyB,OAAO,KAAK;;;;;;;;;;;sDAItD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAK;gEAAc,OAAO,mBAAmB;gEAAC;;;;;;;sEAC/C,6LAAC;;gEAAK;gEAAe,WAAW,OAAO,WAAW;;;;;;;;;;;;;8DAEpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;sEAE3B,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBApGlB,OAAO,EAAE;;;;;gBA4GvB;;;;;;YAGD,cAAc,MAAM,KAAK,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,iBAAiB,SAAS,gBAAgB,QACrD,0CACA;;;;;;kCAGN,6LAAC;wBAAO,WAAU;kCAA8F;;;;;;;;;;;;;;;;;;AAO1H;GA/cwB;KAAA", "debugId": null}}, {"offset": {"line": 13277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/CommunityPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Users,\n  MessageSquare,\n  Heart,\n  Share2,\n  Plus,\n  Search,\n  Filter,\n  TrendingUp,\n  Clock,\n  MapPin,\n  User,\n  ThumbsUp,\n  MessageCircle,\n  Eye,\n  Pin,\n  Flag,\n  MoreHorizontal,\n  Image,\n  Video,\n  FileText,\n  Calendar,\n  Star,\n  Award\n} from 'lucide-react';\n\ninterface CommunityPost {\n  id: string;\n  author: {\n    name: string;\n    avatar?: string;\n    role: 'homeowner' | 'renter' | 'buyer' | 'manager' | 'member';\n    verified: boolean;\n  };\n  content: string;\n  images?: string[];\n  type: 'discussion' | 'question' | 'announcement' | 'event' | 'tip';\n  category: 'general' | 'maintenance' | 'neighborhood' | 'events' | 'marketplace';\n  timestamp: string;\n  likes: number;\n  comments: number;\n  views: number;\n  isLiked: boolean;\n  isPinned: boolean;\n  tags: string[];\n}\n\ninterface CommunityMember {\n  id: string;\n  name: string;\n  avatar?: string;\n  role: 'homeowner' | 'renter' | 'buyer' | 'manager' | 'member';\n  joinDate: string;\n  posts: number;\n  reputation: number;\n  isOnline: boolean;\n  verified: boolean;\n}\n\nexport default function CommunityPanel() {\n  const [activeFilter, setActiveFilter] = useState<'all' | 'discussion' | 'question' | 'announcement' | 'event' | 'tip'>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState<'newest' | 'popular' | 'trending'>('newest');\n\n  // Mock posts data\n  const [posts] = useState<CommunityPost[]>([\n    {\n      id: '1',\n      author: {\n        name: 'Sarah Johnson',\n        avatar: '/api/placeholder/150/150',\n        role: 'homeowner',\n        verified: true\n      },\n      content: 'Just wanted to share some tips for new homeowners about winter maintenance. Make sure to check your heating system before the cold season hits! Also, don\\'t forget to winterize your outdoor faucets.',\n      type: 'tip',\n      category: 'maintenance',\n      timestamp: '2024-01-23T14:30:00Z',\n      likes: 24,\n      comments: 8,\n      views: 156,\n      isLiked: false,\n      isPinned: true,\n      tags: ['maintenance', 'winter', 'tips']\n    },\n    {\n      id: '2',\n      author: {\n        name: 'Mike Chen',\n        role: 'renter',\n        verified: false\n      },\n      content: 'Has anyone else noticed the construction noise on Elm Street? It starts really early in the morning. Does anyone know what they\\'re building?',\n      type: 'question',\n      category: 'neighborhood',\n      timestamp: '2024-01-23T10:15:00Z',\n      likes: 12,\n      comments: 15,\n      views: 89,\n      isLiked: true,\n      isPinned: false,\n      tags: ['neighborhood', 'construction']\n    },\n    {\n      id: '3',\n      author: {\n        name: 'Community Manager',\n        avatar: '/api/placeholder/150/150',\n        role: 'manager',\n        verified: true\n      },\n      content: 'Reminder: Our monthly community meeting is this Saturday at 2 PM in the community center. We\\'ll be discussing the new playground project and upcoming events. Light refreshments will be provided!',\n      type: 'announcement',\n      category: 'events',\n      timestamp: '2024-01-22T16:45:00Z',\n      likes: 45,\n      comments: 12,\n      views: 234,\n      isLiked: false,\n      isPinned: true,\n      tags: ['meeting', 'community', 'announcement']\n    },\n    {\n      id: '4',\n      author: {\n        name: 'Emily Rodriguez',\n        role: 'buyer',\n        verified: false\n      },\n      content: 'Looking for recommendations for a good local plumber. Had a small leak in my bathroom and want to get it fixed properly. Any suggestions?',\n      type: 'question',\n      category: 'maintenance',\n      timestamp: '2024-01-22T09:20:00Z',\n      likes: 8,\n      comments: 6,\n      views: 67,\n      isLiked: false,\n      isPinned: false,\n      tags: ['plumber', 'recommendations', 'maintenance']\n    },\n    {\n      id: '5',\n      author: {\n        name: 'David Kim',\n        role: 'homeowner',\n        verified: true\n      },\n      content: 'Great turnout at yesterday\\'s block party! Thanks to everyone who came and brought food. The kids had a blast with the bounce house. Looking forward to the next one!',\n      images: ['/api/placeholder/400/300', '/api/placeholder/400/300'],\n      type: 'discussion',\n      category: 'events',\n      timestamp: '2024-01-21T20:30:00Z',\n      likes: 67,\n      comments: 23,\n      views: 189,\n      isLiked: true,\n      isPinned: false,\n      tags: ['block-party', 'community', 'fun']\n    }\n  ]);\n\n  // Mock members data\n  const [topMembers] = useState<CommunityMember[]>([\n    {\n      id: '1',\n      name: 'Sarah Johnson',\n      avatar: '/api/placeholder/150/150',\n      role: 'homeowner',\n      joinDate: '2023-03-15T00:00:00Z',\n      posts: 45,\n      reputation: 892,\n      isOnline: true,\n      verified: true\n    },\n    {\n      id: '2',\n      name: 'Community Manager',\n      avatar: '/api/placeholder/150/150',\n      role: 'manager',\n      joinDate: '2022-01-01T00:00:00Z',\n      posts: 156,\n      reputation: 2340,\n      isOnline: true,\n      verified: true\n    },\n    {\n      id: '3',\n      name: 'Mike Chen',\n      role: 'renter',\n      joinDate: '2023-08-20T00:00:00Z',\n      posts: 23,\n      reputation: 456,\n      isOnline: false,\n      verified: false\n    },\n    {\n      id: '4',\n      name: 'David Kim',\n      role: 'homeowner',\n      joinDate: '2023-05-10T00:00:00Z',\n      posts: 34,\n      reputation: 678,\n      isOnline: true,\n      verified: true\n    }\n  ]);\n\n  const filteredPosts = posts.filter(post => {\n    const matchesFilter = activeFilter === 'all' || post.type === activeFilter;\n    const matchesSearch = post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         post.author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    return matchesFilter && matchesSearch;\n  });\n\n  // Sort posts\n  const sortedPosts = [...filteredPosts].sort((a, b) => {\n    // Pinned posts always come first\n    if (a.isPinned && !b.isPinned) return -1;\n    if (!a.isPinned && b.isPinned) return 1;\n    \n    switch (sortBy) {\n      case 'newest':\n        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();\n      case 'popular':\n        return b.likes - a.likes;\n      case 'trending':\n        return (b.likes + b.comments) - (a.likes + a.comments);\n      default:\n        return 0;\n    }\n  });\n\n  const getPostTypeColor = (type: string) => {\n    switch (type) {\n      case 'discussion':\n        return 'bg-blue-100 text-blue-800';\n      case 'question':\n        return 'bg-purple-100 text-purple-800';\n      case 'announcement':\n        return 'bg-red-100 text-red-800';\n      case 'event':\n        return 'bg-green-100 text-green-800';\n      case 'tip':\n        return 'bg-yellow-100 text-yellow-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'homeowner':\n        return 'text-blue-600';\n      case 'renter':\n        return 'text-green-600';\n      case 'buyer':\n        return 'text-purple-600';\n      case 'manager':\n        return 'text-red-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const formatTimeAgo = (timestamp: string) => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60));\n    \n    if (diffInHours < 1) return 'Just now';\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  const totalPosts = posts.length;\n  const totalMembers = 156;\n  const activeMembers = topMembers.filter(m => m.isOnline).length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Community</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          New Post\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <MessageSquare className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Posts</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalPosts}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Users className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Members</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalMembers}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Active Now</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{activeMembers}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Main Content */}\n        <div className=\"lg:col-span-2 space-y-6\">\n          {/* Filters and Search */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4\">\n              <div className=\"relative flex-1 max-w-md\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search posts...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value as any)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"newest\">Newest</option>\n                <option value=\"popular\">Most Popular</option>\n                <option value=\"trending\">Trending</option>\n              </select>\n            </div>\n\n            <div className=\"flex flex-wrap gap-2\">\n              {['all', 'discussion', 'question', 'announcement', 'event', 'tip'].map((filter) => (\n                <button\n                  key={filter}\n                  onClick={() => setActiveFilter(filter as any)}\n                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                    activeFilter === filter\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                  }`}\n                >\n                  {filter.charAt(0).toUpperCase() + filter.slice(1)}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Posts */}\n          <div className=\"space-y-4\">\n            {sortedPosts.map((post) => (\n              <div key={post.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"flex-shrink-0\">\n                    {post.author.avatar ? (\n                      <img\n                        src={post.author.avatar}\n                        alt={post.author.name}\n                        className=\"w-12 h-12 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <div className=\"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center\">\n                        <User className=\"w-6 h-6 text-gray-400\" />\n                      </div>\n                    )}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-start justify-between mb-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <h3 className={`font-semibold ${getRoleColor(post.author.role)}`}>\n                          {post.author.name}\n                        </h3>\n                        {post.author.verified && (\n                          <div className=\"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center\">\n                            <svg className=\"w-2.5 h-2.5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                              <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                            </svg>\n                          </div>\n                        )}\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPostTypeColor(post.type)}`}>\n                          {post.type}\n                        </span>\n                        {post.isPinned && (\n                          <Pin className=\"w-4 h-4 text-orange-500\" />\n                        )}\n                      </div>\n                      <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                        <Clock className=\"w-4 h-4\" />\n                        <span>{formatTimeAgo(post.timestamp)}</span>\n                        <button className=\"text-gray-400 hover:text-gray-600\">\n                          <MoreHorizontal className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </div>\n\n                    <p className=\"text-gray-900 mb-3\">{post.content}</p>\n\n                    {post.images && post.images.length > 0 && (\n                      <div className=\"grid grid-cols-2 gap-2 mb-3\">\n                        {post.images.map((image, index) => (\n                          <img\n                            key={index}\n                            src={image}\n                            alt={`Post image ${index + 1}`}\n                            className=\"rounded-lg object-cover h-32 w-full\"\n                          />\n                        ))}\n                      </div>\n                    )}\n\n                    <div className=\"flex flex-wrap gap-2 mb-3\">\n                      {post.tags.map((tag) => (\n                        <span\n                          key={tag}\n                          className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600\"\n                        >\n                          #{tag}\n                        </span>\n                      ))}\n                    </div>\n\n                    <div className=\"flex items-center justify-between pt-3 border-t border-gray-200\">\n                      <div className=\"flex items-center space-x-6\">\n                        <button className={`flex items-center space-x-1 text-sm transition-colors ${\n                          post.isLiked ? 'text-red-600' : 'text-gray-500 hover:text-red-600'\n                        }`}>\n                          <Heart className={`w-4 h-4 ${post.isLiked ? 'fill-current' : ''}`} />\n                          <span>{post.likes}</span>\n                        </button>\n                        <button className=\"flex items-center space-x-1 text-sm text-gray-500 hover:text-blue-600 transition-colors\">\n                          <MessageCircle className=\"w-4 h-4\" />\n                          <span>{post.comments}</span>\n                        </button>\n                        <div className=\"flex items-center space-x-1 text-sm text-gray-500\">\n                          <Eye className=\"w-4 h-4\" />\n                          <span>{post.views}</span>\n                        </div>\n                      </div>\n                      <button className=\"text-gray-500 hover:text-gray-700 transition-colors\">\n                        <Share2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {sortedPosts.length === 0 && (\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n              <MessageSquare className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No posts found</h3>\n              <p className=\"text-gray-600 mb-6\">\n                {searchTerm || activeFilter !== 'all'\n                  ? 'Try adjusting your search or filters.'\n                  : 'Be the first to start a conversation in the community!'\n                }\n              </p>\n              <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\">\n                Create First Post\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Top Members */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Top Members</h3>\n            <div className=\"space-y-4\">\n              {topMembers.map((member) => (\n                <div key={member.id} className=\"flex items-center space-x-3\">\n                  <div className=\"relative\">\n                    {member.avatar ? (\n                      <img\n                        src={member.avatar}\n                        alt={member.name}\n                        className=\"w-10 h-10 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <div className=\"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\">\n                        <User className=\"w-5 h-5 text-gray-400\" />\n                      </div>\n                    )}\n                    {member.isOnline && (\n                      <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\n                    )}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-1\">\n                      <p className={`text-sm font-medium ${getRoleColor(member.role)} truncate`}>\n                        {member.name}\n                      </p>\n                      {member.verified && (\n                        <div className=\"w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center\">\n                          <svg className=\"w-2 h-2 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                            <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                          </svg>\n                        </div>\n                      )}\n                    </div>\n                    <div className=\"flex items-center space-x-2 text-xs text-gray-500\">\n                      <span>{member.posts} posts</span>\n                      <span>•</span>\n                      <div className=\"flex items-center\">\n                        <Star className=\"w-3 h-3 text-yellow-400 mr-1\" />\n                        <span>{member.reputation}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Community Guidelines */}\n          <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Community Guidelines</h3>\n            <div className=\"space-y-3 text-sm text-gray-600\">\n              <div className=\"flex items-start space-x-2\">\n                <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0\"></div>\n                <p>Be respectful and kind to all community members</p>\n              </div>\n              <div className=\"flex items-start space-x-2\">\n                <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0\"></div>\n                <p>Keep discussions relevant to the community</p>\n              </div>\n              <div className=\"flex items-start space-x-2\">\n                <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0\"></div>\n                <p>No spam, self-promotion, or commercial posts</p>\n              </div>\n              <div className=\"flex items-start space-x-2\">\n                <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0\"></div>\n                <p>Report inappropriate content to moderators</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA8De,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwE;IACvH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAExE,kBAAkB;IAClB,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxC;YACE,IAAI;YACJ,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,MAAM;gBACN,UAAU;YACZ;YACA,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAe;gBAAU;aAAO;QACzC;QACA;YACE,IAAI;YACJ,QAAQ;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;YACZ;YACA,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAgB;aAAe;QACxC;QACA;YACE,IAAI;YACJ,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,MAAM;gBACN,UAAU;YACZ;YACA,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAW;gBAAa;aAAe;QAChD;QACA;YACE,IAAI;YACJ,QAAQ;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;YACZ;YACA,SAAS;YACT,MAAM;YACN,UAAU;YACV,WAAW;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAW;gBAAmB;aAAc;QACrD;QACA;YACE,IAAI;YACJ,QAAQ;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;YACZ;YACA,SAAS;YACT,QAAQ;gBAAC;gBAA4B;aAA2B;YAChE,MAAM;YACN,UAAU;YACV,WAAW;YACX,OAAO;YACP,UAAU;YACV,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;gBAAC;gBAAe;gBAAa;aAAM;QAC3C;KACD;IAED,oBAAoB;IACpB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;QAC/C;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;YACP,YAAY;YACZ,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;YACP,YAAY;YACZ,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,YAAY;YACZ,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,UAAU;YACV,OAAO;YACP,YAAY;YACZ,UAAU;YACV,UAAU;QACZ;KACD;IAED,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,iBAAiB,SAAS,KAAK,IAAI,KAAK;QAC9D,MAAM,gBAAgB,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,KAAK,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC5F,OAAO,iBAAiB;IAC1B;IAEA,aAAa;IACb,MAAM,cAAc;WAAI;KAAc,CAAC,IAAI,CAAC,CAAC,GAAG;QAC9C,iCAAiC;QACjC,IAAI,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC;QACvC,IAAI,CAAC,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,OAAO;QAEtC,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,AAAC,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAI,CAAC,EAAE,KAAK,GAAG,EAAE,QAAQ;YACvD;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,cAAc,GAAG,OAAO;QAC5B,IAAI,cAAc,IAAI,OAAO,AAAC,GAAc,OAAZ,aAAY;QAC5C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,IAAI,aAAa,GAAG,OAAO,AAAC,GAAa,OAAX,YAAW;QACzC,OAAO,KAAK,kBAAkB;IAChC;IAEA,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,eAAe;IACrB,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;IAE/D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;0DAId,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAI7B,6LAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAO;4CAAc;4CAAY;4CAAgB;4CAAS;yCAAM,CAAC,GAAG,CAAC,CAAC,uBACtE,6LAAC;gDAEC,SAAS,IAAM,gBAAgB;gDAC/B,WAAW,AAAC,gEAIX,OAHC,iBAAiB,SACb,8BACA;0DAGL,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;+CAR1C;;;;;;;;;;;;;;;;0CAeb,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;wCAAkB,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,KAAK,MAAM,CAAC,MAAM,iBACjB,6LAAC;wDACC,KAAK,KAAK,MAAM,CAAC,MAAM;wDACvB,KAAK,KAAK,MAAM,CAAC,IAAI;wDACrB,WAAU;;;;;6EAGZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAW,AAAC,iBAA+C,OAA/B,aAAa,KAAK,MAAM,CAAC,IAAI;sFAC1D,KAAK,MAAM,CAAC,IAAI;;;;;;wEAElB,KAAK,MAAM,CAAC,QAAQ,kBACnB,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;gFAAyB,MAAK;gFAAe,SAAQ;0FAClE,cAAA,6LAAC;oFAAK,UAAS;oFAAU,GAAE;oFAAqH,UAAS;;;;;;;;;;;;;;;;sFAI/J,6LAAC;4EAAK,WAAW,AAAC,2EAAsG,OAA5B,iBAAiB,KAAK,IAAI;sFACnH,KAAK,IAAI;;;;;;wEAEX,KAAK,QAAQ,kBACZ,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;8EAGnB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;sFAAM,cAAc,KAAK,SAAS;;;;;;sFACnC,6LAAC;4EAAO,WAAU;sFAChB,cAAA,6LAAC,mNAAA,CAAA,iBAAc;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sEAKhC,6LAAC;4DAAE,WAAU;sEAAsB,KAAK,OAAO;;;;;;wDAE9C,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACnC,6LAAC;4DAAI,WAAU;sEACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oEAEC,KAAK;oEACL,KAAK,AAAC,cAAuB,OAAV,QAAQ;oEAC3B,WAAU;mEAHL;;;;;;;;;;sEASb,6LAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,6LAAC;oEAEC,WAAU;;wEACX;wEACG;;mEAHG;;;;;;;;;;sEAQX,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAO,WAAW,AAAC,yDAEnB,OADC,KAAK,OAAO,GAAG,iBAAiB;;8FAEhC,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAW,AAAC,WAA6C,OAAnC,KAAK,OAAO,GAAG,iBAAiB;;;;;;8FAC7D,6LAAC;8FAAM,KAAK,KAAK;;;;;;;;;;;;sFAEnB,6LAAC;4EAAO,WAAU;;8FAChB,6LAAC,2NAAA,CAAA,gBAAa;oFAAC,WAAU;;;;;;8FACzB,6LAAC;8FAAM,KAAK,QAAQ;;;;;;;;;;;;sFAEtB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;8FACf,6LAAC;8FAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;8EAGrB,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAzFlB,KAAK,EAAE;;;;;;;;;;4BAkGpB,YAAY,MAAM,KAAK,mBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDACV,cAAc,iBAAiB,QAC5B,0CACA;;;;;;kDAGN,6LAAC;wCAAO,WAAU;kDAA8F;;;;;;;;;;;;;;;;;;kCAQtH,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,uBACf,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,MAAM,iBACZ,6LAAC;gEACC,KAAK,OAAO,MAAM;gEAClB,KAAK,OAAO,IAAI;gEAChB,WAAU;;;;;qFAGZ,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;4DAGnB,OAAO,QAAQ,kBACd,6LAAC;gEAAI,WAAU;;;;;;;;;;;;kEAGnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAW,AAAC,uBAAgD,OAA1B,aAAa,OAAO,IAAI,GAAE;kFAC5D,OAAO,IAAI;;;;;;oEAEb,OAAO,QAAQ,kBACd,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAAqB,MAAK;4EAAe,SAAQ;sFAC9D,cAAA,6LAAC;gFAAK,UAAS;gFAAU,GAAE;gFAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;;0EAKjK,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAM,OAAO,KAAK;4EAAC;;;;;;;kFACpB,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAM,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;+CAnCtB,OAAO,EAAE;;;;;;;;;;;;;;;;0CA6CzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAE;;;;;;;;;;;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAE;;;;;;;;;;;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAE;;;;;;;;;;;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GAhgBwB;KAAA", "debugId": null}}, {"offset": {"line": 14532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/EventsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Calendar,\n  Clock,\n  MapPin,\n  Users,\n  Plus,\n  Search,\n  Filter,\n  Eye,\n  Share2,\n  Heart,\n  MessageSquare,\n  User,\n  Star,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  CalendarDays,\n  Ticket,\n  Gift,\n  Music,\n  Coffee,\n  Home,\n  Building2,\n  PartyPopper\n} from 'lucide-react';\n\ninterface CommunityEvent {\n  id: string;\n  title: string;\n  description: string;\n  organizer: {\n    name: string;\n    avatar?: string;\n    role: 'homeowner' | 'renter' | 'buyer' | 'manager' | 'member';\n  };\n  date: string;\n  time: string;\n  location: string;\n  category: 'social' | 'educational' | 'maintenance' | 'meeting' | 'celebration';\n  attendees: number;\n  maxAttendees?: number;\n  isAttending: boolean;\n  isFree: boolean;\n  price?: number;\n  images?: string[];\n  tags: string[];\n  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';\n  rsvpDeadline?: string;\n}\n\nexport default function EventsPanel() {\n  const [activeFilter, setActiveFilter] = useState<'all' | 'upcoming' | 'ongoing' | 'completed'>('upcoming');\n  const [categoryFilter, setCategoryFilter] = useState<'all' | 'social' | 'educational' | 'maintenance' | 'meeting' | 'celebration'>('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n\n  // Mock events data\n  const [events] = useState<CommunityEvent[]>([\n    {\n      id: '1',\n      title: 'Monthly Community Meeting',\n      description: 'Join us for our monthly community meeting to discuss upcoming projects, budget updates, and community concerns. Light refreshments will be provided.',\n      organizer: {\n        name: 'Community Manager',\n        avatar: '/api/placeholder/150/150',\n        role: 'manager'\n      },\n      date: '2024-02-03T00:00:00Z',\n      time: '14:00',\n      location: 'Community Center - Main Hall',\n      category: 'meeting',\n      attendees: 23,\n      maxAttendees: 50,\n      isAttending: true,\n      isFree: true,\n      images: ['/api/placeholder/400/300'],\n      tags: ['meeting', 'community', 'monthly'],\n      status: 'upcoming',\n      rsvpDeadline: '2024-02-01T23:59:59Z'\n    },\n    {\n      id: '2',\n      title: 'Winter Block Party',\n      description: 'Come celebrate winter with hot cocoa, live music, and fun activities for the whole family! Bring your friends and neighbors for a cozy evening.',\n      organizer: {\n        name: 'Sarah Johnson',\n        avatar: '/api/placeholder/150/150',\n        role: 'homeowner'\n      },\n      date: '2024-02-10T00:00:00Z',\n      time: '17:00',\n      location: 'Central Park Area',\n      category: 'celebration',\n      attendees: 45,\n      maxAttendees: 100,\n      isAttending: false,\n      isFree: true,\n      images: ['/api/placeholder/400/300', '/api/placeholder/400/300'],\n      tags: ['party', 'winter', 'family', 'music'],\n      status: 'upcoming'\n    },\n    {\n      id: '3',\n      title: 'Home Maintenance Workshop',\n      description: 'Learn essential home maintenance skills from local experts. Topics include plumbing basics, electrical safety, and seasonal maintenance tips.',\n      organizer: {\n        name: 'Mike Chen',\n        role: 'homeowner'\n      },\n      date: '2024-02-15T00:00:00Z',\n      time: '10:00',\n      location: 'Community Workshop Room',\n      category: 'educational',\n      attendees: 12,\n      maxAttendees: 20,\n      isAttending: true,\n      isFree: false,\n      price: 15,\n      tags: ['workshop', 'maintenance', 'education'],\n      status: 'upcoming',\n      rsvpDeadline: '2024-02-13T23:59:59Z'\n    },\n    {\n      id: '4',\n      title: 'Coffee & Chat Morning',\n      description: 'Casual morning meetup for neighbors to connect over coffee and pastries. Great opportunity to meet new people in the community.',\n      organizer: {\n        name: 'Emily Rodriguez',\n        role: 'renter'\n      },\n      date: '2024-01-28T00:00:00Z',\n      time: '09:00',\n      location: 'Community Garden Pavilion',\n      category: 'social',\n      attendees: 18,\n      isAttending: false,\n      isFree: true,\n      tags: ['coffee', 'social', 'morning'],\n      status: 'completed'\n    },\n    {\n      id: '5',\n      title: 'Spring Garden Planning',\n      description: 'Plan and prepare for the community garden spring planting. We\\'ll discuss plot assignments, plant selections, and maintenance schedules.',\n      organizer: {\n        name: 'David Kim',\n        role: 'homeowner'\n      },\n      date: '2024-02-20T00:00:00Z',\n      time: '15:30',\n      location: 'Community Garden',\n      category: 'meeting',\n      attendees: 8,\n      maxAttendees: 25,\n      isAttending: false,\n      isFree: true,\n      tags: ['garden', 'planning', 'spring'],\n      status: 'upcoming'\n    }\n  ]);\n\n  const filteredEvents = events.filter(event => {\n    const matchesStatus = activeFilter === 'all' || event.status === activeFilter;\n    const matchesCategory = categoryFilter === 'all' || event.category === categoryFilter;\n    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         event.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    return matchesStatus && matchesCategory && matchesSearch;\n  });\n\n  // Sort events by date\n  const sortedEvents = [...filteredEvents].sort((a, b) => {\n    return new Date(a.date).getTime() - new Date(b.date).getTime();\n  });\n\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'social':\n        return 'bg-blue-100 text-blue-800';\n      case 'educational':\n        return 'bg-purple-100 text-purple-800';\n      case 'maintenance':\n        return 'bg-orange-100 text-orange-800';\n      case 'meeting':\n        return 'bg-gray-100 text-gray-800';\n      case 'celebration':\n        return 'bg-pink-100 text-pink-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'social':\n        return <Coffee className=\"w-4 h-4\" />;\n      case 'educational':\n        return <Star className=\"w-4 h-4\" />;\n      case 'maintenance':\n        return <Building2 className=\"w-4 h-4\" />;\n      case 'meeting':\n        return <Users className=\"w-4 h-4\" />;\n      case 'celebration':\n        return <PartyPopper className=\"w-4 h-4\" />;\n      default:\n        return <Calendar className=\"w-4 h-4\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'upcoming':\n        return 'bg-green-100 text-green-800';\n      case 'ongoing':\n        return 'bg-blue-100 text-blue-800';\n      case 'completed':\n        return 'bg-gray-100 text-gray-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      weekday: 'long',\n      month: 'long',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  };\n\n  const formatTime = (time: string) => {\n    const [hours, minutes] = time.split(':');\n    const hour = parseInt(hours);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n  };\n\n  const totalEvents = events.length;\n  const upcomingEvents = events.filter(e => e.status === 'upcoming').length;\n  const myEvents = events.filter(e => e.isAttending).length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Community Events</h2>\n        <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Create Event\n        </button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Calendar className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Events</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalEvents}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CalendarDays className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Upcoming</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{upcomingEvents}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Ticket className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">My Events</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{myEvents}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4\">\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search events...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          \n          <select\n            value={categoryFilter}\n            onChange={(e) => setCategoryFilter(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Categories</option>\n            <option value=\"social\">Social</option>\n            <option value=\"educational\">Educational</option>\n            <option value=\"maintenance\">Maintenance</option>\n            <option value=\"meeting\">Meeting</option>\n            <option value=\"celebration\">Celebration</option>\n          </select>\n        </div>\n\n        <div className=\"flex flex-wrap gap-2\">\n          {['all', 'upcoming', 'ongoing', 'completed'].map((filter) => (\n            <button\n              key={filter}\n              onClick={() => setActiveFilter(filter as any)}\n              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${\n                activeFilter === filter\n                  ? 'bg-blue-100 text-blue-700'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n              }`}\n            >\n              {filter.charAt(0).toUpperCase() + filter.slice(1)}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Events Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {sortedEvents.map((event) => (\n          <div key={event.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n            {event.images && event.images.length > 0 && (\n              <div className=\"relative h-48\">\n                <img\n                  src={event.images[0]}\n                  alt={event.title}\n                  className=\"w-full h-full object-cover\"\n                />\n                <div className=\"absolute top-4 left-4\">\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>\n                    {event.status}\n                  </span>\n                </div>\n                <div className=\"absolute top-4 right-4\">\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(event.category)}`}>\n                    {getCategoryIcon(event.category)}\n                    <span className=\"ml-1\">{event.category}</span>\n                  </span>\n                </div>\n              </div>\n            )}\n\n            <div className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-3\">\n                <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-2\">{event.title}</h3>\n                {event.isAttending && (\n                  <CheckCircle className=\"w-5 h-5 text-green-500 flex-shrink-0 ml-2\" />\n                )}\n              </div>\n\n              <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">{event.description}</p>\n\n              <div className=\"space-y-2 mb-4\">\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Calendar className=\"w-4 h-4 mr-2\" />\n                  <span>{formatDate(event.date)}</span>\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Clock className=\"w-4 h-4 mr-2\" />\n                  <span>{formatTime(event.time)}</span>\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <MapPin className=\"w-4 h-4 mr-2\" />\n                  <span>{event.location}</span>\n                </div>\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Users className=\"w-4 h-4 mr-2\" />\n                  <span>\n                    {event.attendees} attending\n                    {event.maxAttendees && ` / ${event.maxAttendees} max`}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-2\">\n                  {event.organizer.avatar ? (\n                    <img\n                      src={event.organizer.avatar}\n                      alt={event.organizer.name}\n                      className=\"w-6 h-6 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <div className=\"w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center\">\n                      <User className=\"w-3 h-3 text-gray-400\" />\n                    </div>\n                  )}\n                  <span className=\"text-sm text-gray-600\">by {event.organizer.name}</span>\n                </div>\n                <div className=\"text-sm font-medium\">\n                  {event.isFree ? (\n                    <span className=\"text-green-600\">Free</span>\n                  ) : (\n                    <span className=\"text-gray-900\">${event.price}</span>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"flex flex-wrap gap-1 mb-4\">\n                {event.tags.slice(0, 3).map((tag) => (\n                  <span\n                    key={tag}\n                    className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600\"\n                  >\n                    #{tag}\n                  </span>\n                ))}\n                {event.tags.length > 3 && (\n                  <span className=\"text-xs text-gray-500\">+{event.tags.length - 3} more</span>\n                )}\n              </div>\n\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-200\">\n                <div className=\"flex space-x-2\">\n                  <button className=\"text-gray-600 hover:text-gray-700 p-1 rounded\">\n                    <Eye className=\"w-4 h-4\" />\n                  </button>\n                  <button className=\"text-gray-600 hover:text-gray-700 p-1 rounded\">\n                    <Share2 className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                {event.status === 'upcoming' && (\n                  <button className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                    event.isAttending\n                      ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                      : 'bg-blue-600 text-white hover:bg-blue-700'\n                  }`}>\n                    {event.isAttending ? 'Cancel RSVP' : 'RSVP'}\n                  </button>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {sortedEvents.length === 0 && (\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center\">\n          <Calendar className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No events found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {searchTerm || categoryFilter !== 'all' || activeFilter !== 'upcoming'\n              ? 'Try adjusting your search or filters.'\n              : 'No upcoming events scheduled. Be the first to create one!'\n            }\n          </p>\n          <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors\">\n            Create Event\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAsDe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgD;IAC/F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgF;IACnI,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,mBAAmB;IACnB,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAC1C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;gBACT,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;YACA,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,cAAc;YACd,aAAa;YACb,QAAQ;YACR,QAAQ;gBAAC;aAA2B;YACpC,MAAM;gBAAC;gBAAW;gBAAa;aAAU;YACzC,QAAQ;YACR,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;gBACT,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;YACA,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,cAAc;YACd,aAAa;YACb,QAAQ;YACR,QAAQ;gBAAC;gBAA4B;aAA2B;YAChE,MAAM;gBAAC;gBAAS;gBAAU;gBAAU;aAAQ;YAC5C,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;YACA,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,cAAc;YACd,aAAa;YACb,QAAQ;YACR,OAAO;YACP,MAAM;gBAAC;gBAAY;gBAAe;aAAY;YAC9C,QAAQ;YACR,cAAc;QAChB;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;YACA,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,aAAa;YACb,QAAQ;YACR,MAAM;gBAAC;gBAAU;gBAAU;aAAU;YACrC,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;YACA,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,cAAc;YACd,aAAa;YACb,QAAQ;YACR,MAAM;gBAAC;gBAAU;gBAAY;aAAS;YACtC,QAAQ;QACV;KACD;IAED,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,iBAAiB,SAAS,MAAM,MAAM,KAAK;QACjE,MAAM,kBAAkB,mBAAmB,SAAS,MAAM,QAAQ,KAAK;QACvE,MAAM,gBAAgB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC7F,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,eAAe;WAAI;KAAe,CAAC,IAAI,CAAC,CAAC,GAAG;QAChD,OAAO,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAC9D;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,SAAS;YACT,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;QACpC,MAAM,OAAO,SAAS;QACtB,MAAM,OAAO,QAAQ,KAAK,OAAO;QACjC,MAAM,cAAc,OAAO,MAAM;QACjC,OAAO,AAAC,GAAiB,OAAf,aAAY,KAAc,OAAX,SAAQ,KAAQ,OAAL;IACtC;IAEA,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;IACzE,MAAM,WAAW,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAc;;;;;;;;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAO;4BAAY;4BAAW;yBAAY,CAAC,GAAG,CAAC,CAAC,uBAChD,6LAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,AAAC,gEAIX,OAHC,iBAAiB,SACb,8BACA;0CAGL,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;+BAR1C;;;;;;;;;;;;;;;;0BAeb,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC;wBAAmB,WAAU;;4BAC3B,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,MAAM,GAAG,mBACrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,MAAM,MAAM,CAAC,EAAE;wCACpB,KAAK,MAAM,KAAK;wCAChB,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAW,AAAC,2EAAuG,OAA7B,eAAe,MAAM,MAAM;sDACpH,MAAM,MAAM;;;;;;;;;;;kDAGjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAW,AAAC,2EAA2G,OAAjC,iBAAiB,MAAM,QAAQ;;gDACxH,gBAAgB,MAAM,QAAQ;8DAC/B,6LAAC;oDAAK,WAAU;8DAAQ,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoD,MAAM,KAAK;;;;;;4CAC5E,MAAM,WAAW,kBAChB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAI3B,6LAAC;wCAAE,WAAU;kDAA2C,MAAM,WAAW;;;;;;kDAEzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAM,WAAW,MAAM,IAAI;;;;;;;;;;;;0DAE9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAM,WAAW,MAAM,IAAI;;;;;;;;;;;;0DAE9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAM,MAAM,QAAQ;;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;4DACE,MAAM,SAAS;4DAAC;4DAChB,MAAM,YAAY,IAAI,AAAC,MAAwB,OAAnB,MAAM,YAAY,EAAC;;;;;;;;;;;;;;;;;;;kDAKtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,MAAM,SAAS,CAAC,MAAM,iBACrB,6LAAC;wDACC,KAAK,MAAM,SAAS,CAAC,MAAM;wDAC3B,KAAK,MAAM,SAAS,CAAC,IAAI;wDACzB,WAAU;;;;;6EAGZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAGpB,6LAAC;wDAAK,WAAU;;4DAAwB;4DAAI,MAAM,SAAS,CAAC,IAAI;;;;;;;;;;;;;0DAElE,6LAAC;gDAAI,WAAU;0DACZ,MAAM,MAAM,iBACX,6LAAC;oDAAK,WAAU;8DAAiB;;;;;yEAEjC,6LAAC;oDAAK,WAAU;;wDAAgB;wDAAE,MAAM,KAAK;;;;;;;;;;;;;;;;;;kDAKnD,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC3B,6LAAC;oDAEC,WAAU;;wDACX;wDACG;;mDAHG;;;;;4CAMR,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC;gDAAK,WAAU;;oDAAwB;oDAAE,MAAM,IAAI,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;kDAIpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAGrB,MAAM,MAAM,KAAK,4BAChB,6LAAC;gDAAO,WAAW,AAAC,8DAInB,OAHC,MAAM,WAAW,GACb,gDACA;0DAEH,MAAM,WAAW,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;uBA3GrC,MAAM,EAAE;;;;;;;;;;YAoHrB,aAAa,MAAM,KAAK,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,cAAc,mBAAmB,SAAS,iBAAiB,aACxD,0CACA;;;;;;kCAGN,6LAAC;wBAAO,WAAU;kCAA8F;;;;;;;;;;;;;;;;;;AAO1H;GA1awB;KAAA", "debugId": null}}, {"offset": {"line": 15584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/homeowner/HomeownerDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Building2, TrendingUp, Coins, FileText } from 'lucide-react';\nimport { useAccount } from 'wagmi';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useNFTOperations, useContractRead } from '@/hooks/useWeb3';\n\nexport default function HomeownerDashboard() {\n  const { user } = useAuth();\n  const { mintPropertyNFT } = useNFTOperations();\n  const { address } = useAccount();\n  const [tokenizedProperties, setTokenizedProperties] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  const { data: balance } = useContractRead('NFTi', 'balanceOf', address ? [address] : []);\n\n  useEffect(() => {\n    if (balance && address) {\n      const fetchTokens = async () => {\n        setLoading(true);\n        const tokens = [];\n        for (let i = 0; i < Number(balance); i++) {\n          const tokenId = await useContractRead('NFTi', 'tokenOfOwnerByIndex', [address, i]);\n          tokens.push({ id: tokenId, status: 'Active' });\n        }\n        setTokenizedProperties(tokens);\n        setLoading(false);\n      };\n      fetchTokens();\n    }\n  }, [balance, address]);\n\n  const handleTokenize = async () => {\n    try {\n      setLoading(true);\n      const tokenId = Math.floor(Math.random() * 1000); // Dummy tokenId\n      const tokenURI = 'ipfs://dummy-uri'; // Dummy URI\n      await mintPropertyNFT(tokenId, tokenURI);\n      alert('Property tokenized successfully!');\n    } catch (error) {\n      console.error(error);\n      alert('Failed to tokenize property.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Homeowner Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Building2 className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">My Properties</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Performance</h3>\n              <p className=\"text-2xl font-bold\">N/A</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Coins className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Tokens</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <FileText className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Reports</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">My Tokenized Properties</h3>\n        {loading ? (\n          <p>Loading...</p>\n        ) : tokenizedProperties.length > 0 ? (\n          <div className=\"space-y-4\">\n            {tokenizedProperties.map((prop, index) => (\n              <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"font-medium\">Property #{index + 1}</h4>\n                <p className=\"text-sm text-gray-600\">Token ID: {prop.id} | Status: {prop.status}</p>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <p>No tokenized properties yet.</p>\n        )}\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button \n            onClick={handleTokenize}\n            disabled={loading}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? 'Tokenizing...' : 'Tokenize Property'}\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            List on Marketplace\n          </button>\n          <button className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n            Redeem Tokens\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,aAAa,UAAU;QAAC;KAAQ,GAAG,EAAE;IAEvF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,WAAW,SAAS;gBACtB,MAAM;gEAAc;wBAClB,WAAW;wBACX,MAAM,SAAS,EAAE;wBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,UAAU,IAAK;4BACxC,MAAM,UAAU,MAAM,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,uBAAuB;gCAAC;gCAAS;6BAAE;4BACjF,OAAO,IAAI,CAAC;gCAAE,IAAI;gCAAS,QAAQ;4BAAS;wBAC9C;wBACA,uBAAuB;wBACvB,WAAW;oBACb;;gBACA;YACF;QACF;uCAAG;QAAC;QAAS;KAAQ;IAErB,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,gBAAgB;YAClE,MAAM,WAAW,oBAAoB,YAAY;YACjD,MAAM,gBAAgB,SAAS;YAC/B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;YACd,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;oBAC1C,wBACC,6LAAC;kCAAE;;;;;+BACD,oBAAoB,MAAM,GAAG,kBAC/B,6LAAC;wBAAI,WAAU;kCACZ,oBAAoB,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC;wCAAG,WAAU;;4CAAc;4CAAW,QAAQ;;;;;;;kDAC/C,6LAAC;wCAAE,WAAU;;4CAAwB;4CAAW,KAAK,EAAE;4CAAC;4CAAY,KAAK,MAAM;;;;;;;;+BAFvE;;;;;;;;;6CAOd,6LAAC;kCAAE;;;;;;;;;;;;0BAIP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,kBAAkB;;;;;;0CAE/B,6LAAC;gCAAO,WAAU;0CAAkE;;;;;;0CAGpF,6LAAC;gCAAO,WAAU;0CAAoE;;;;;;;;;;;;;;;;;;;;;;;;AAOhG;GA/HwB;;QACL,kIAAA,CAAA,UAAO;QACI,0HAAA,CAAA,mBAAgB;QACxB,8JAAA,CAAA,aAAU;QAIJ,0HAAA,CAAA,kBAAe;;;KAPnB", "debugId": null}}, {"offset": {"line": 16039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/renter/RenterDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Home, CreditCard, Star, MessageSquare } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useMLifeToken } from '@/hooks/useWeb3';\n\nexport default function RenterDashboard() {\n  const { user } = useAuth();\n  const { transfer, claimRewards } = useMLifeToken(); // Assuming claimRewards is available or implement similarly\n  const [loading, setLoading] = useState(false);\n\n  const handlePayRent = async () => {\n    setLoading(true);\n    try {\n      await transfer('recipientAddress', 100); // Dummy amount and address\n      alert('Rent paid successfully!');\n    } catch (error) {\n      alert('Failed to pay rent.');\n    }\n    setLoading(false);\n  };\n\n  const handleClaimRewards = async () => {\n    setLoading(true);\n    try {\n      await claimRewards(); // Assuming this function exists\n      alert('Rewards claimed!');\n    } catch (error) {\n      alert('Failed to claim rewards.');\n    }\n    setLoading(false);\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Renter Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Home className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">My Rentals</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <CreditCard className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Payments Due</h3>\n              <p className=\"text-2xl font-bold\">$0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Star className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Ratings</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <MessageSquare className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Messages</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Pending Payments</h3>\n        <p>Due: $500 (Dummy data)</p>\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Available Rewards</h3>\n        <p>100 MLife (Dummy data)</p>\n      </div>\n\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button \n            onClick={handlePayRent}\n            disabled={loading}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? 'Processing...' : 'Pay Rent'}\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            Submit Maintenance Request\n          </button>\n          <button \n            onClick={handleClaimRewards}\n            disabled={loading}\n            className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50\"\n          >\n            {loading ? 'Claiming...' : 'Claim Rewards'}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,KAAK,4DAA4D;IAChH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,oBAAoB,MAAM,2BAA2B;YACpE,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR;QACA,WAAW;IACb;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,IAAI;YAC<PERSON>,MAAM,gBAAgB,gCAAgC;YACtD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR;QACA,WAAW;IACb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,sMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;kCAAE;;;;;;;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;kCAAE;;;;;;;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,kBAAkB;;;;;;0CAE/B,6LAAC;gCAAO,WAAU;0CAAkE;;;;;;0CAGpF,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC;GA9GwB;;QACL,kIAAA,CAAA,UAAO;QACW,0HAAA,CAAA,gBAAa;;;KAF1B", "debugId": null}}, {"offset": {"line": 16444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/buyer/BuyerDashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport { ShoppingCart, Heart, FileCheck, DollarSign } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function BuyerDashboard() {\n  const { user } = useAuth();\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Buyer Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <ShoppingCart className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">My Purchases</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Heart className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Saved Properties</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <FileCheck className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Pending Offers</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Total Spent</h3>\n              <p className=\"text-2xl font-bold\">$0</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\">\n            Browse Marketplace\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            Make Offer\n          </button>\n          <button className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n            Track Purchases\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAEA;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAgE;;;;;;0CAGlF,6LAAC;gCAAO,WAAU;0CAAkE;;;;;;0CAGpF,6LAAC;gCAAO,WAAU;0CAAoE;;;;;;;;;;;;;;;;;;;;;;;;AAOhG;GApEwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 16770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/portfolio-manager/PortfolioManagerDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Briefcase, BarChart2, Wallet, AlertCircle, TrendingUp, TrendingDown, DollarSign, Building2, Plus, Eye, Edit, Trash2, MapPin, Users, Coins } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { usePropertyRegistry } from '@/hooks/useWeb3';\nimport { Property } from '@/types';\n\ninterface PortfolioStats {\n  totalValue: number;\n  totalInvestment: number;\n  totalReturn: number;\n  monthlyIncome: number;\n  averageROI: number;\n  propertiesCount: number;\n  occupancyRate: number;\n}\n\nexport default function PortfolioManagerDashboard() {\n  const { user } = useAuth();\n  const { registerProperty, tokenizeProperty } = usePropertyRegistry();\n  const [properties, setProperties] = useState<Property[]>([]);\n  const [portfolioStats, setPortfolioStats] = useState<PortfolioStats>({\n    totalValue: 0,\n    totalInvestment: 0,\n    totalReturn: 0,\n    monthlyIncome: 0,\n    averageROI: 0,\n    propertiesCount: 0,\n    occupancyRate: 0,\n  });\n  const [filterType, setFilterType] = useState<'all' | 'residential' | 'commercial' | 'industrial'>('all');\n  const [sortBy, setSortBy] = useState<'value' | 'roi' | 'income' | 'date'>('value');\n\n  // 模拟获取数据或集成真实数据\n  useEffect(() => {\n    // 这里应集成真实数据源，如从合约获取\n    const mockProperties = [\n      // 类似 portfolio/page.tsx 中的 mock 数据\n      {\n        id: '1',\n        title: 'Luxury Downtown Apartment',\n        location: 'Manhattan, NY',\n        type: 'residential',\n        value: 850000,\n        purchasePrice: 720000,\n        purchaseDate: '2023-03-15',\n        monthlyIncome: 6500,\n        occupancyRate: 100,\n        roi: 15.2,\n        status: 'active',\n        image: '/api/placeholder/300/200',\n        tenants: 1,\n        maxTenants: 1,\n      },\n      // 添加更多...\n    ];\n    setProperties(mockProperties);\n\n    // 计算统计\n    const stats = mockProperties.reduce((acc, prop) => ({\n      totalValue: acc.totalValue + prop.value,\n      totalInvestment: acc.totalInvestment + prop.purchasePrice,\n      totalReturn: acc.totalReturn + (prop.value - prop.purchasePrice),\n      monthlyIncome: acc.monthlyIncome + prop.monthlyIncome,\n      averageROI: (acc.averageROI + prop.roi) / (acc.propertiesCount + 1),\n      propertiesCount: acc.propertiesCount + 1,\n      occupancyRate: (acc.occupancyRate + prop.occupancyRate) / (acc.propertiesCount + 1),\n    }), { totalValue: 0, totalInvestment: 0, totalReturn: 0, monthlyIncome: 0, averageROI: 0, propertiesCount: 0, occupancyRate: 0 });\n    setPortfolioStats(stats);\n  }, []);\n\n  const filteredProperties = properties.filter(property => filterType === 'all' || property.type === filterType);\n\n  const sortedProperties = [...filteredProperties].sort((a, b) => {\n    switch (sortBy) {\n      case 'value': return b.value - a.value;\n      case 'roi': return b.roi - a.roi;\n      case 'income': return b.monthlyIncome - a.monthlyIncome;\n      case 'date': return new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime();\n      default: return 0;\n    }\n  });\n\n  const handleUpdateAsset = async (propertyId: string) => {\n    // 实现更新逻辑，使用 registerProperty 或 tokenizeProperty\n    try {\n      await tokenizeProperty(Number(propertyId), 'mockURI');\n      alert('Asset updated successfully');\n    } catch (error) {\n      console.error('Update failed', error);\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Portfolio Manager Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Portfolio Value</p>\n              <p className=\"text-2xl font-bold text-gray-900\">${portfolioStats.totalValue.toLocaleString()}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-6 h-6 text-blue-600\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n            <span className=\"text-sm text-green-600 font-medium\">+12.5%</span>\n            <span className=\"text-sm text-gray-500 ml-1\">vs last month</span>\n          </div>\n        </div>\n        {/* 类似添加其他统计卡片，使用 portfolioStats */}\n      </div>\n      {/* 添加属性列表、过滤、排序和更新按钮，类似 portfolio/page.tsx */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Properties</h3>\n        {/* 过滤和排序选择 */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {sortedProperties.map((property) => (\n            <div key={property.id} className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\">\n              {/* 属性详情 */}\n              <button onClick={() => handleUpdateAsset(property.id)} className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n                Update Asset\n              </button>\n            </div>\n          ))}\n        </div>\n      </div>\n      {/* 添加监控部分，如警报列表 */}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAEA;AACA;;;;;;;AAae,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,YAAY;QACZ,iBAAiB;QACjB,aAAa;QACb,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;IACjB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuD;IAClG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1E,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR,oBAAoB;YACpB,MAAM,iBAAiB;gBACrB,mCAAmC;gBACnC;oBACE,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,OAAO;oBACP,eAAe;oBACf,cAAc;oBACd,eAAe;oBACf,eAAe;oBACf,KAAK;oBACL,QAAQ;oBACR,OAAO;oBACP,SAAS;oBACT,YAAY;gBACd;aAED;YACD,cAAc;YAEd,OAAO;YACP,MAAM,QAAQ,eAAe,MAAM;6DAAC,CAAC,KAAK,OAAS,CAAC;wBAClD,YAAY,IAAI,UAAU,GAAG,KAAK,KAAK;wBACvC,iBAAiB,IAAI,eAAe,GAAG,KAAK,aAAa;wBACzD,aAAa,IAAI,WAAW,GAAG,CAAC,KAAK,KAAK,GAAG,KAAK,aAAa;wBAC/D,eAAe,IAAI,aAAa,GAAG,KAAK,aAAa;wBACrD,YAAY,CAAC,IAAI,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,eAAe,GAAG,CAAC;wBAClE,iBAAiB,IAAI,eAAe,GAAG;wBACvC,eAAe,CAAC,IAAI,aAAa,GAAG,KAAK,aAAa,IAAI,CAAC,IAAI,eAAe,GAAG,CAAC;oBACpF,CAAC;4DAAG;gBAAE,YAAY;gBAAG,iBAAiB;gBAAG,aAAa;gBAAG,eAAe;gBAAG,YAAY;gBAAG,iBAAiB;gBAAG,eAAe;YAAE;YAC/H,kBAAkB;QACpB;8CAAG,EAAE;IAEL,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,WAAY,eAAe,SAAS,SAAS,IAAI,KAAK;IAEnG,MAAM,mBAAmB;WAAI;KAAmB,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,OAAQ;YACN,KAAK;gBAAS,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YACtC,KAAK;gBAAO,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG;YAChC,KAAK;gBAAU,OAAO,EAAE,aAAa,GAAG,EAAE,aAAa;YACvD,KAAK;gBAAQ,OAAO,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO;YACzF;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,gDAAgD;QAChD,IAAI;YACF,MAAM,iBAAiB,OAAO,aAAa;YAC3C,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,eAAe,UAAU,CAAC,cAAc;;;;;;;;;;;;;8CAE5F,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,6LAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAE3C,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;gCAAsB,WAAU;0CAE/B,cAAA,6LAAC;oCAAO,SAAS,IAAM,kBAAkB,SAAS,EAAE;oCAAG,WAAU;8CAAoE;;;;;;+BAF7H,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AAYjC;GApHwB;;QACL,kIAAA,CAAA,UAAO;QACuB,0HAAA,CAAA,sBAAmB;;;KAF5C", "debugId": null}}, {"offset": {"line": 17048, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/community-member/CommunityMemberDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Users, Gift, MessageSquare, BarChart2 } from 'lucide-react';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useMLifeToken } from '@/hooks/useWeb3';\n\nexport default function CommunityMemberDashboard() {\n  const { user } = useAuth();\n  const { balanceOf, claimRewards } = useMLifeToken();\n  const [earnedRewards, setEarnedRewards] = useState(0);\n\n  useEffect(() => {\n    const fetchRewards = async () => {\n      if (user?.walletAddress) {\n        const balance = await balanceOf(user.walletAddress);\n        setEarnedRewards(Number(balance));\n      }\n    };\n    fetchRewards();\n  }, [user, balanceOf]);\n\n  const handleClaimRewards = async () => {\n    try {\n      await claimRewards(); // 假设 claimRewards 函数存在于钩子中\n      alert('Rewards claimed successfully');\n      // 更新余额\n      if (user?.walletAddress) {\n        const balance = await balanceOf(user.walletAddress);\n        setEarnedRewards(Number(balance));\n      }\n    } catch (error) {\n      console.error('Claim failed', error);\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <h2 className=\"text-2xl font-bold mb-6\">Community Member Dashboard</h2>\n      <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Users className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Referrals</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Gift className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Earned Rewards</h3>\n              <p className=\"text-2xl font-bold\">{earnedRewards} MLife</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <MessageSquare className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Discussions</h3>\n              <p className=\"text-2xl font-bold\">0</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-200\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <BarChart2 className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900\">Community Stats</h3>\n              <p className=\"text-2xl font-bold\">N/A</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold mb-4\">Quick Actions</h3>\n        <div className=\"flex space-x-4\">\n          <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\">\n            Refer a Friend\n          </button>\n          <button onClick={handleClaimRewards} className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700\">\n            Claim Rewards\n          </button>\n          <button className=\"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700\">\n            Join Discussion\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;AAEe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,MAAM;mEAAe;oBACnB,IAAI,iBAAA,2BAAA,KAAM,aAAa,EAAE;wBACvB,MAAM,UAAU,MAAM,UAAU,KAAK,aAAa;wBAClD,iBAAiB,OAAO;oBAC1B;gBACF;;YACA;QACF;6CAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,gBAAgB,2BAA2B;YACjD,MAAM;YACN,OAAO;YACP,IAAI,iBAAA,2BAAA,KAAM,aAAa,EAAE;gBACvB,MAAM,UAAU,MAAM,UAAU,KAAK,aAAa;gBAClD,iBAAiB,OAAO;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;;gDAAsB;gDAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAIvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mOAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAAgE;;;;;;0CAGlF,6LAAC;gCAAO,SAAS;gCAAoB,WAAU;0CAAkE;;;;;;0CAGjH,6LAAC;gCAAO,WAAU;0CAAoE;;;;;;;;;;;;;;;;;;;;;;;;AAOhG;GA9FwB;;QACL,kIAAA,CAAA,UAAO;QACY,0HAAA,CAAA,gBAAa;;;KAF3B", "debugId": null}}]}