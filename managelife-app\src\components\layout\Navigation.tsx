'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Building2, Menu, X, User, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface NavigationProps {
  showAuthButtons?: boolean;
  transparent?: boolean;
  className?: string;
}

export default function Navigation({ 
  showAuthButtons = true, 
  transparent = false,
  className = ""
}: NavigationProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const navLinks = [
    { href: '/marketplace', label: 'Marketplace' },
    { href: '/tokenize', label: 'Tokenize Property' },
    { href: '/about', label: 'Team' },
    { href: '/community', label: 'Community' },
    { href: '/docs', label: 'Docs' },
    { href: '/blog', label: 'Blog' },
  ];

  const externalLinks = [
    { href: 'https://managelife.io', label: 'Solutions' },
    { href: 'https://managelife.io', label: 'Official Site' },
  ];

  const baseClasses = transparent 
    ? "absolute top-0 left-0 right-0 z-50 bg-transparent" 
    : "bg-white shadow-sm border-b border-gray-200";

  return (
    <header className={`${baseClasses} ${className}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <img
              src="https://managelife.io/logo/ML-Logo.svg"
              alt="ManageLife"
              className="h-8 w-auto"
            />
            <span className="text-xl font-bold gradient-text">ManageLife</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`${
                  transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                } transition-colors`}
              >
                {link.label}
              </Link>
            ))}
            {externalLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                className={`${
                  transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                } transition-colors`}
              >
                {link.label}
              </a>
            ))}
          </nav>

          {/* Auth Buttons / User Menu */}
          <div className="flex items-center space-x-4">
            {showAuthButtons && (
              <>
                {user ? (
                  <div className="flex items-center space-x-4">
                    <Link
                      href="/dashboard"
                      className={`flex items-center space-x-2 ${
                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                      } transition-colors`}
                    >
                      <User className="w-4 h-4" />
                      <span className="hidden sm:inline">Dashboard</span>
                    </Link>
                    <button
                      onClick={handleLogout}
                      className={`flex items-center space-x-2 ${
                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                      } transition-colors`}
                    >
                      <LogOut className="w-4 h-4" />
                      <span className="hidden sm:inline">Logout</span>
                    </button>
                  </div>
                ) : (
                  <>
                    <Link
                      href="/auth/login"
                      className={`${
                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                      } transition-colors`}
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/register"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 web3-glow"
                    >
                      Get Started
                    </Link>
                  </>
                )}
              </>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className={`md:hidden ${
                transparent ? 'text-white' : 'text-gray-600'
              }`}
            >
              {mobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className={`md:hidden py-4 border-t ${
            transparent ? 'border-white/20 bg-black/20 backdrop-blur-sm' : 'border-gray-200'
          }`}>
            <div className="flex flex-col space-y-4">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`${
                    transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                  } transition-colors`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {link.label}
                </Link>
              ))}
              {externalLinks.map((link) => (
                <a
                  key={link.href}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${
                    transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                  } transition-colors`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {link.label}
                </a>
              ))}
              
              {showAuthButtons && (
                <div className="pt-4 border-t border-gray-200 space-y-4">
                  {user ? (
                    <>
                      <Link
                        href="/dashboard"
                        className={`flex items-center space-x-2 ${
                          transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                        } transition-colors`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <User className="w-4 h-4" />
                        <span>Dashboard</span>
                      </Link>
                      <button
                        onClick={() => {
                          handleLogout();
                          setMobileMenuOpen(false);
                        }}
                        className={`flex items-center space-x-2 ${
                          transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                        } transition-colors`}
                      >
                        <LogOut className="w-4 h-4" />
                        <span>Logout</span>
                      </button>
                    </>
                  ) : (
                    <>
                      <Link
                        href="/auth/login"
                        className={`block ${
                          transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                        } transition-colors`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Sign In
                      </Link>
                      <Link
                        href="/auth/register"
                        className="block bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-center hover:shadow-lg transition-all duration-300 web3-glow"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Get Started
                      </Link>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
