{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/dashboard/ReportsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  FileBarChart,\n  Download,\n  Calendar,\n  DollarSign,\n  TrendingUp,\n  TrendingDown,\n  Building2,\n  Users,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>hart<PERSON>,\n  <PERSON><PERSON>,\n  RefreshCw,\n  Eye,\n  Share2,\n  FileText,\n  Calculator,\n  Target,\n  AlertCircle\n} from 'lucide-react';\n\ninterface ReportData {\n  id: string;\n  title: string;\n  type: 'financial' | 'occupancy' | 'maintenance' | 'performance';\n  period: string;\n  generatedDate: string;\n  status: 'ready' | 'generating' | 'error';\n  summary: {\n    totalRevenue?: number;\n    totalExpenses?: number;\n    netIncome?: number;\n    occupancyRate?: number;\n    averageRent?: number;\n    maintenanceCosts?: number;\n    roi?: number;\n  };\n  charts?: {\n    type: 'line' | 'bar' | 'pie';\n    data: any[];\n  }[];\n}\n\nexport default function ReportsPanel() {\n  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month');\n  const [selectedType, setSelectedType] = useState<'all' | 'financial' | 'occupancy' | 'maintenance' | 'performance'>('all');\n  const [activeTab, setActiveTab] = useState<'overview' | 'financial' | 'occupancy' | 'maintenance'>('overview');\n\n  // Mock reports data\n  const [reports] = useState<ReportData[]>([\n    {\n      id: '1',\n      title: 'Monthly Financial Report',\n      type: 'financial',\n      period: 'January 2024',\n      generatedDate: '2024-01-31T23:59:59Z',\n      status: 'ready',\n      summary: {\n        totalRevenue: 12600,\n        totalExpenses: 3200,\n        netIncome: 9400,\n        roi: 7.5\n      }\n    },\n    {\n      id: '2',\n      title: 'Occupancy Analysis',\n      type: 'occupancy',\n      period: 'Q4 2023',\n      generatedDate: '2024-01-15T10:30:00Z',\n      status: 'ready',\n      summary: {\n        occupancyRate: 92,\n        averageRent: 3150\n      }\n    },\n    {\n      id: '3',\n      title: 'Maintenance Cost Report',\n      type: 'maintenance',\n      period: 'December 2023',\n      generatedDate: '2024-01-05T14:20:00Z',\n      status: 'ready',\n      summary: {\n        maintenanceCosts: 1850,\n        totalExpenses: 3200\n      }\n    },\n    {\n      id: '4',\n      title: 'Portfolio Performance',\n      type: 'performance',\n      period: '2023 Annual',\n      generatedDate: '2024-01-01T00:00:00Z',\n      status: 'generating',\n      summary: {\n        totalRevenue: 145000,\n        netIncome: 98000,\n        roi: 8.2\n      }\n    }\n  ]);\n\n  // Mock current period data\n  const currentPeriodData = {\n    totalRevenue: 12600,\n    totalExpenses: 3200,\n    netIncome: 9400,\n    occupancyRate: 92,\n    averageRent: 3150,\n    maintenanceCosts: 1850,\n    roi: 7.5,\n    properties: 4,\n    tenants: 4,\n    vacantUnits: 0,\n    maintenanceRequests: 8\n  };\n\n  const filteredReports = reports.filter(report => {\n    return selectedType === 'all' || report.type === selectedType;\n  });\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ready':\n        return 'bg-green-100 text-green-800';\n      case 'generating':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'error':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'financial':\n        return <DollarSign className=\"w-5 h-5 text-green-600\" />;\n      case 'occupancy':\n        return <Users className=\"w-5 h-5 text-blue-600\" />;\n      case 'maintenance':\n        return <Building2 className=\"w-5 h-5 text-orange-600\" />;\n      case 'performance':\n        return <TrendingUp className=\"w-5 h-5 text-purple-600\" />;\n      default:\n        return <FileBarChart className=\"w-5 h-5 text-gray-600\" />;\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return `$${amount.toLocaleString()}`;\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    });\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Reports & Analytics</h2>\n        <div className=\"flex items-center space-x-3\">\n          <select\n            value={selectedPeriod}\n            onChange={(e) => setSelectedPeriod(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </select>\n          <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\">\n            <RefreshCw className=\"w-4 h-4 mr-2\" />\n            Generate Report\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {[\n              { id: 'overview', label: 'Overview', icon: BarChart3 },\n              { id: 'financial', label: 'Financial', icon: DollarSign },\n              { id: 'occupancy', label: 'Occupancy', icon: Users },\n              { id: 'maintenance', label: 'Maintenance', icon: Building2 },\n            ].map((tab) => {\n              const IconComponent = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <IconComponent className=\"w-4 h-4\" />\n                  <span>{tab.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Overview Tab */}\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Key Metrics */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                <div className=\"bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 border border-green-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-green-600\">Total Revenue</p>\n                      <p className=\"text-2xl font-bold text-green-900\">{formatCurrency(currentPeriodData.totalRevenue)}</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-green-200 rounded-lg flex items-center justify-center\">\n                      <TrendingUp className=\"w-6 h-6 text-green-600\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center text-sm\">\n                    <TrendingUp className=\"w-4 h-4 text-green-500 mr-1\" />\n                    <span className=\"text-green-600 font-medium\">+12.5%</span>\n                    <span className=\"text-green-600 ml-1\">vs last month</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-blue-600\">Net Income</p>\n                      <p className=\"text-2xl font-bold text-blue-900\">{formatCurrency(currentPeriodData.netIncome)}</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-blue-200 rounded-lg flex items-center justify-center\">\n                      <DollarSign className=\"w-6 h-6 text-blue-600\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center text-sm\">\n                    <TrendingUp className=\"w-4 h-4 text-blue-500 mr-1\" />\n                    <span className=\"text-blue-600 font-medium\">+8.3%</span>\n                    <span className=\"text-blue-600 ml-1\">vs last month</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-purple-600\">Occupancy Rate</p>\n                      <p className=\"text-2xl font-bold text-purple-900\">{currentPeriodData.occupancyRate}%</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-purple-200 rounded-lg flex items-center justify-center\">\n                      <Users className=\"w-6 h-6 text-purple-600\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center text-sm\">\n                    <TrendingUp className=\"w-4 h-4 text-purple-500 mr-1\" />\n                    <span className=\"text-purple-600 font-medium\">+2.1%</span>\n                    <span className=\"text-purple-600 ml-1\">vs last month</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-6 border border-orange-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-orange-600\">ROI</p>\n                      <p className=\"text-2xl font-bold text-orange-900\">{currentPeriodData.roi}%</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-orange-200 rounded-lg flex items-center justify-center\">\n                      <Target className=\"w-6 h-6 text-orange-600\" />\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center text-sm\">\n                    <TrendingUp className=\"w-4 h-4 text-orange-500 mr-1\" />\n                    <span className=\"text-orange-600 font-medium\">+0.5%</span>\n                    <span className=\"text-orange-600 ml-1\">vs last month</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Quick Stats */}\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center\">\n                    <Building2 className=\"w-8 h-8 text-blue-600 mr-3\" />\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Properties</p>\n                      <p className=\"text-xl font-bold text-gray-900\">{currentPeriodData.properties}</p>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center\">\n                    <Users className=\"w-8 h-8 text-green-600 mr-3\" />\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Active Tenants</p>\n                      <p className=\"text-xl font-bold text-gray-900\">{currentPeriodData.tenants}</p>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center\">\n                    <AlertCircle className=\"w-8 h-8 text-orange-600 mr-3\" />\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Vacant Units</p>\n                      <p className=\"text-xl font-bold text-gray-900\">{currentPeriodData.vacantUnits}</p>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-white border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center\">\n                    <FileText className=\"w-8 h-8 text-purple-600 mr-3\" />\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Maintenance</p>\n                      <p className=\"text-xl font-bold text-gray-900\">{currentPeriodData.maintenanceRequests}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Financial Tab */}\n          {activeTab === 'financial' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Revenue Breakdown</h3>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Rental Income</span>\n                      <span className=\"font-semibold\">{formatCurrency(11200)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Late Fees</span>\n                      <span className=\"font-semibold\">{formatCurrency(150)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Other Income</span>\n                      <span className=\"font-semibold\">{formatCurrency(1250)}</span>\n                    </div>\n                    <div className=\"border-t pt-3 flex justify-between font-bold\">\n                      <span>Total Revenue</span>\n                      <span>{formatCurrency(currentPeriodData.totalRevenue)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Expense Breakdown</h3>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Maintenance</span>\n                      <span className=\"font-semibold\">{formatCurrency(1850)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Property Tax</span>\n                      <span className=\"font-semibold\">{formatCurrency(800)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Insurance</span>\n                      <span className=\"font-semibold\">{formatCurrency(350)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Management</span>\n                      <span className=\"font-semibold\">{formatCurrency(200)}</span>\n                    </div>\n                    <div className=\"border-t pt-3 flex justify-between font-bold\">\n                      <span>Total Expenses</span>\n                      <span>{formatCurrency(currentPeriodData.totalExpenses)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Profitability</h3>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Gross Income</span>\n                      <span className=\"font-semibold\">{formatCurrency(currentPeriodData.totalRevenue)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Total Expenses</span>\n                      <span className=\"font-semibold text-red-600\">-{formatCurrency(currentPeriodData.totalExpenses)}</span>\n                    </div>\n                    <div className=\"border-t pt-3 flex justify-between font-bold text-green-600\">\n                      <span>Net Income</span>\n                      <span>{formatCurrency(currentPeriodData.netIncome)}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Profit Margin</span>\n                      <span className=\"font-semibold\">{((currentPeriodData.netIncome / currentPeriodData.totalRevenue) * 100).toFixed(1)}%</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Other tabs would have similar content */}\n          {activeTab === 'occupancy' && (\n            <div className=\"text-center py-12\">\n              <PieChart className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Occupancy Analytics</h3>\n              <p className=\"text-gray-600\">Detailed occupancy reports and trends coming soon.</p>\n            </div>\n          )}\n\n          {activeTab === 'maintenance' && (\n            <div className=\"text-center py-12\">\n              <Building2 className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Maintenance Reports</h3>\n              <p className=\"text-gray-600\">Maintenance cost analysis and trends coming soon.</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Generated Reports */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Generated Reports</h3>\n          <select\n            value={selectedType}\n            onChange={(e) => setSelectedType(e.target.value as any)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Types</option>\n            <option value=\"financial\">Financial</option>\n            <option value=\"occupancy\">Occupancy</option>\n            <option value=\"maintenance\">Maintenance</option>\n            <option value=\"performance\">Performance</option>\n          </select>\n        </div>\n\n        <div className=\"space-y-4\">\n          {filteredReports.map((report) => (\n            <div key={report.id} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"mt-1\">\n                    {getTypeIcon(report.type)}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{report.title}</h4>\n                    <p className=\"text-sm text-gray-600\">{report.period}</p>\n                    <p className=\"text-xs text-gray-500\">Generated on {formatDate(report.generatedDate)}</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-3\">\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>\n                    {report.status === 'generating' && <RefreshCw className=\"w-3 h-3 mr-1 animate-spin\" />}\n                    {report.status}\n                  </span>\n                  {report.status === 'ready' && (\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-gray-600 hover:text-gray-700 p-1 rounded\">\n                        <Eye className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"text-gray-600 hover:text-gray-700 p-1 rounded\">\n                        <Download className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"text-gray-600 hover:text-gray-700 p-1 rounded\">\n                        <Share2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n              \n              {report.summary && report.status === 'ready' && (\n                <div className=\"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200\">\n                  {report.summary.totalRevenue && (\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Revenue</p>\n                      <p className=\"font-semibold\">{formatCurrency(report.summary.totalRevenue)}</p>\n                    </div>\n                  )}\n                  {report.summary.netIncome && (\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Net Income</p>\n                      <p className=\"font-semibold\">{formatCurrency(report.summary.netIncome)}</p>\n                    </div>\n                  )}\n                  {report.summary.occupancyRate && (\n                    <div>\n                      <p className=\"text-xs text-gray-500\">Occupancy</p>\n                      <p className=\"font-semibold\">{report.summary.occupancyRate}%</p>\n                    </div>\n                  )}\n                  {report.summary.roi && (\n                    <div>\n                      <p className=\"text-xs text-gray-500\">ROI</p>\n                      <p className=\"font-semibold\">{report.summary.roi}%</p>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA8Ce,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqE;IACpH,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IAEnG,oBAAoB;IACpB,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACvC;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,QAAQ;YACR,eAAe;YACf,QAAQ;YACR,SAAS;gBACP,cAAc;gBACd,eAAe;gBACf,WAAW;gBACX,KAAK;YACP;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,QAAQ;YACR,eAAe;YACf,QAAQ;YACR,SAAS;gBACP,eAAe;gBACf,aAAa;YACf;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,QAAQ;YACR,eAAe;YACf,QAAQ;YACR,SAAS;gBACP,kBAAkB;gBAClB,eAAe;YACjB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,QAAQ;YACR,eAAe;YACf,QAAQ;YACR,SAAS;gBACP,cAAc;gBACd,WAAW;gBACX,KAAK;YACP;QACF;KACD;IAED,2BAA2B;IAC3B,MAAM,oBAAoB;QACxB,cAAc;QACd,eAAe;QACf,WAAW;QACX,eAAe;QACf,aAAa;QACb,kBAAkB;QAClB,KAAK;QACL,YAAY;QACZ,SAAS;QACT,aAAa;QACb,qBAAqB;IACvB;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,OAAO,iBAAiB,SAAS,OAAO,IAAI,KAAK;IACnD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,6LAAC,8OAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;QACnC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,AAAC,IAA2B,OAAxB,OAAO,cAAc;IAClC;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;0CAEvB,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM,qNAAA,CAAA,YAAS;gCAAC;gCACrD;oCAAE,IAAI;oCAAa,OAAO;oCAAa,MAAM,qNAAA,CAAA,aAAU;gCAAC;gCACxD;oCAAE,IAAI;oCAAa,OAAO;oCAAa,MAAM,uMAAA,CAAA,QAAK;gCAAC;gCACnD;oCAAE,IAAI;oCAAe,OAAO;oCAAe,MAAM,mNAAA,CAAA,YAAS;gCAAC;6BAC5D,CAAC,GAAG,CAAC,CAAC;gCACL,MAAM,gBAAgB,IAAI,IAAI;gCAC9B,qBACE,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,AAAC,0FAIX,OAHC,cAAc,IAAI,EAAE,GAChB,kCACA;;sDAGN,6LAAC;4CAAc,WAAU;;;;;;sDACzB,6LAAC;sDAAM,IAAI,KAAK;;;;;;;mCATX,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAIJ,6LAAC;wBAAI,WAAU;;4BAEZ,cAAc,4BACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAqC;;;;;;kFAClD,6LAAC;wEAAE,WAAU;kFAAqC,eAAe,kBAAkB,YAAY;;;;;;;;;;;;0EAEjG,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAG1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;;;;;;0DAI1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAoC;;;;;;kFACjD,6LAAC;wEAAE,WAAU;kFAAoC,eAAe,kBAAkB,SAAS;;;;;;;;;;;;0EAE7F,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAG1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAsC;;;;;;kFACnD,6LAAC;wEAAE,WAAU;;4EAAsC,kBAAkB,aAAa;4EAAC;;;;;;;;;;;;;0EAErF,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAGrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAA8B;;;;;;0EAC9C,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;0DAI3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAsC;;;;;;kFACnD,6LAAC;wEAAE,WAAU;;4EAAsC,kBAAkB,GAAG;4EAAC;;;;;;;;;;;;;0EAE3E,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAGtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;gEAAK,WAAU;0EAA8B;;;;;;0EAC9C,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAmC,kBAAkB,UAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIlF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAmC,kBAAkB,OAAO;;;;;;;;;;;;;;;;;;;;;;;0DAI/E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAmC,kBAAkB,WAAW;;;;;;;;;;;;;;;;;;;;;;;0DAInF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAmC,kBAAkB,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAShG,cAAc,6BACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAM,eAAe,kBAAkB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;sDAK1D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAM,eAAe,kBAAkB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;sDAK3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAAiB,eAAe,kBAAkB,YAAY;;;;;;;;;;;;sEAEhF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;;wEAA6B;wEAAE,eAAe,kBAAkB,aAAa;;;;;;;;;;;;;sEAE/F,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;8EAAM,eAAe,kBAAkB,SAAS;;;;;;;;;;;;sEAEnD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;;wEAAiB,CAAC,AAAC,kBAAkB,SAAS,GAAG,kBAAkB,YAAY,GAAI,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAS9H,cAAc,6BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;4BAIhC,cAAc,+BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAc;;;;;;;;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;gCAAoB,WAAU;;kDAC7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,YAAY,OAAO,IAAI;;;;;;kEAE1B,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA+B,OAAO,KAAK;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAyB,OAAO,MAAM;;;;;;0EACnD,6LAAC;gEAAE,WAAU;;oEAAwB;oEAAc,WAAW,OAAO,aAAa;;;;;;;;;;;;;;;;;;;0DAGtF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,AAAC,2EAAwG,OAA9B,eAAe,OAAO,MAAM;;4DACrH,OAAO,MAAM,KAAK,8BAAgB,6LAAC,mNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DACvD,OAAO,MAAM;;;;;;;oDAEf,OAAO,MAAM,KAAK,yBACjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAO3B,OAAO,OAAO,IAAI,OAAO,MAAM,KAAK,yBACnC,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,OAAO,CAAC,YAAY,kBAC1B,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEAAiB,eAAe,OAAO,OAAO,CAAC,YAAY;;;;;;;;;;;;4CAG3E,OAAO,OAAO,CAAC,SAAS,kBACvB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEAAiB,eAAe,OAAO,OAAO,CAAC,SAAS;;;;;;;;;;;;4CAGxE,OAAO,OAAO,CAAC,aAAa,kBAC3B,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;;4DAAiB,OAAO,OAAO,CAAC,aAAa;4DAAC;;;;;;;;;;;;;4CAG9D,OAAO,OAAO,CAAC,GAAG,kBACjB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;;4DAAiB,OAAO,OAAO,CAAC,GAAG;4DAAC;;;;;;;;;;;;;;;;;;;;+BAxDjD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;AAmE/B;GArdwB;KAAA", "debugId": null}}]}