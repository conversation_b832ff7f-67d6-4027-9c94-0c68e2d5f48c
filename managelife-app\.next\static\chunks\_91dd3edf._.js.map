{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/settings/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\nexport default function SettingsPage() {\r\n  const { user } = useAuth();\r\n  const router = useRouter();\r\n\r\n  // Redirect to dashboard settings tab\r\n  useEffect(() => {\r\n    if (!user) {\r\n      router.push('/auth/login');\r\n    } else {\r\n      router.replace('/dashboard?tab=settings');\r\n    }\r\n  }, [user, router]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n      <div className=\"text-center\">\r\n        <div className=\"w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\r\n        <p className=\"text-gray-600\">Redirecting to Settings...</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,OAAO,CAAC;YACjB;QACF;iCAAG;QAAC;QAAM;KAAO;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;;AAIrC;GArBwB;;QACL,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}