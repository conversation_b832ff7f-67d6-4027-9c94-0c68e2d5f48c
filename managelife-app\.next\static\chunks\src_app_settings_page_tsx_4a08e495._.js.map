{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface SettingsData {\n  // Profile Settings\n  displayName: string;\n  bio: string;\n  location: string;\n  website: string;\n  \n  // Privacy Settings\n  profileVisibility: 'public' | 'private' | 'friends';\n  showEmail: boolean;\n  showWallet: boolean;\n  showActivity: boolean;\n  \n  // Notification Settings\n  emailNotifications: boolean;\n  pushNotifications: boolean;\n  rewardNotifications: boolean;\n  communityNotifications: boolean;\n  marketingEmails: boolean;\n  \n  // App Preferences\n  language: string;\n  currency: string;\n  timezone: string;\n  theme: 'light' | 'dark' | 'auto';\n  \n  // Security Settings\n  twoFactorEnabled: boolean;\n  loginAlerts: boolean;\n  sessionTimeout: number;\n}\n\nexport default function SettingsPage() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState<'profile' | 'privacy' | 'notifications' | 'preferences' | 'security' | 'data'>('profile');\n  const [isSaving, setIsSaving] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);\n\n  const [settings, setSettings] = useState<SettingsData>({\n    displayName: '',\n    bio: '',\n    location: '',\n    website: '',\n    profileVisibility: 'public',\n    showEmail: false,\n    showWallet: false,\n    showActivity: true,\n    emailNotifications: true,\n    pushNotifications: true,\n    rewardNotifications: true,\n    communityNotifications: false,\n    marketingEmails: false,\n    language: 'en',\n    currency: 'usd',\n    timezone: 'UTC',\n    theme: 'light',\n    twoFactorEnabled: false,\n    loginAlerts: true,\n    sessionTimeout: 30,\n  });\n\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n\n  // Redirect to login if not authenticated\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login');\n    }\n  }, [user, loading, router]);\n\n  // Load settings from API\n  const loadSettings = async () => {\n    if (!user) return;\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('/api/settings', {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSettings(data.settings);\n      }\n    } catch (error) {\n      console.error('Failed to load settings:', error);\n      // Fallback to user data\n      setSettings(prev => ({\n        ...prev,\n        displayName: user.name || '',\n        bio: user.bio || '',\n        location: user.location || '',\n        website: user.website || '',\n      }));\n    }\n  };\n\n  // Initialize settings when user loads\n  useEffect(() => {\n    loadSettings();\n  }, [user]);\n\n  const handleSettingChange = (key: keyof SettingsData, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value,\n    }));\n  };\n\n  const handlePasswordChange = (key: keyof typeof passwordData, value: string) => {\n    setPasswordData(prev => ({\n      ...prev,\n      [key]: value,\n    }));\n  };\n\n  const handleSaveSettings = async () => {\n    setIsSaving(true);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('/api/settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify(settings),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to save settings');\n      }\n\n      setSaveMessage({ type: 'success', text: 'Settings saved successfully!' });\n      setTimeout(() => setSaveMessage(null), 3000);\n    } catch (error) {\n      setSaveMessage({ type: 'error', text: 'Failed to save settings. Please try again.' });\n      setTimeout(() => setSaveMessage(null), 3000);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePasswordUpdate = async () => {\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setSaveMessage({ type: 'error', text: 'New passwords do not match.' });\n      setTimeout(() => setSaveMessage(null), 3000);\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('/api/settings', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify({\n          action: 'change_password',\n          currentPassword: passwordData.currentPassword,\n          newPassword: passwordData.newPassword,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to update password');\n      }\n\n      setSaveMessage({ type: 'success', text: 'Password updated successfully!' });\n      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });\n      setTimeout(() => setSaveMessage(null), 3000);\n    } catch (error: any) {\n      setSaveMessage({ type: 'error', text: error.message || 'Failed to update password. Please try again.' });\n      setTimeout(() => setSaveMessage(null), 3000);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleExportData = () => {\n    // Simulate data export\n    const data = {\n      user: user,\n      settings: settings,\n      exportDate: new Date().toISOString(),\n    };\n    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `managelife-data-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleDeleteAccount = async () => {\n    if (!showDeleteConfirm) {\n      setShowDeleteConfirm(true);\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('/api/settings', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify({\n          action: 'delete_account',\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to delete account');\n      }\n\n      setSaveMessage({ type: 'success', text: 'Account deletion initiated. You will receive a confirmation email.' });\n      setShowDeleteConfirm(false);\n\n      // Redirect to home page after a delay\n      setTimeout(() => {\n        localStorage.removeItem('token');\n        router.push('/');\n      }, 3000);\n    } catch (error: any) {\n      setSaveMessage({ type: 'error', text: error.message || 'Failed to delete account. Please contact support.' });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading settings...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  const tabs = [\n    { id: 'profile', label: 'Profile', icon: User },\n    { id: 'privacy', label: 'Privacy', icon: Shield },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'preferences', label: 'Preferences', icon: Globe },\n    { id: 'security', label: 'Security', icon: Lock },\n    { id: 'data', label: 'Data & Privacy', icon: Database },\n  ] as const;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-4\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Settings</h1>\n            <button\n              onClick={() => router.push('/dashboard')}\n              className=\"text-gray-600 hover:text-blue-600 transition-colors\"\n            >\n              Back to Dashboard\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Save Message */}\n          {saveMessage && (\n            <div className={`mb-6 p-4 rounded-lg flex items-center ${\n              saveMessage.type === 'success' \n                ? 'bg-green-50 text-green-800 border border-green-200' \n                : 'bg-red-50 text-red-800 border border-red-200'\n            }`}>\n              {saveMessage.type === 'success' ? (\n                <Check className=\"w-5 h-5 mr-2\" />\n              ) : (\n                <AlertTriangle className=\"w-5 h-5 mr-2\" />\n              )}\n              {saveMessage.text}\n            </div>\n          )}\n\n          <div className=\"grid lg:grid-cols-4 gap-8\">\n            {/* Sidebar Navigation */}\n            <div className=\"lg:col-span-1\">\n              <nav className=\"space-y-1\">\n                {tabs.map((tab) => {\n                  const Icon = tab.icon;\n                  return (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id as any)}\n                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                        activeTab === tab.id\n                          ? 'bg-blue-100 text-blue-700'\n                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                      }`}\n                    >\n                      <Icon className=\"w-5 h-5 mr-3\" />\n                      {tab.label}\n                    </button>\n                  );\n                })}\n              </nav>\n            </div>\n\n            {/* Main Content */}\n            <div className=\"lg:col-span-3\">\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200\">\n                {/* Profile Settings */}\n                {activeTab === 'profile' && (\n                  <div className=\"p-6\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Profile Settings</h2>\n                    \n                    <div className=\"space-y-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Display Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={settings.displayName}\n                          onChange={(e) => handleSettingChange('displayName', e.target.value)}\n                          className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          placeholder=\"Your display name\"\n                        />\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Bio\n                        </label>\n                        <textarea\n                          value={settings.bio}\n                          onChange={(e) => handleSettingChange('bio', e.target.value)}\n                          rows={4}\n                          className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          placeholder=\"Tell us about yourself...\"\n                        />\n                      </div>\n\n                      <div className=\"grid md:grid-cols-2 gap-6\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Location\n                          </label>\n                          <input\n                            type=\"text\"\n                            value={settings.location}\n                            onChange={(e) => handleSettingChange('location', e.target.value)}\n                            className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            placeholder=\"City, Country\"\n                          />\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Website\n                          </label>\n                          <input\n                            type=\"url\"\n                            value={settings.website}\n                            onChange={(e) => handleSettingChange('website', e.target.value)}\n                            className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                            placeholder=\"https://yourwebsite.com\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Privacy Settings */}\n                {activeTab === 'privacy' && (\n                  <div className=\"p-6\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Privacy Settings</h2>\n                    \n                    <div className=\"space-y-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                          Profile Visibility\n                        </label>\n                        <select\n                          value={settings.profileVisibility}\n                          onChange={(e) => handleSettingChange('profileVisibility', e.target.value)}\n                          className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        >\n                          <option value=\"public\">Public - Anyone can see your profile</option>\n                          <option value=\"private\">Private - Only you can see your profile</option>\n                          <option value=\"friends\">Friends - Only your connections can see</option>\n                        </select>\n                      </div>\n\n                      <div className=\"space-y-4\">\n                        <h3 className=\"text-lg font-medium text-gray-900\">Show in Profile</h3>\n                        \n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Email Address</h4>\n                            <p className=\"text-sm text-gray-600\">Display your email on your public profile</p>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={settings.showEmail}\n                              onChange={(e) => handleSettingChange('showEmail', e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Wallet Address</h4>\n                            <p className=\"text-sm text-gray-600\">Show your connected wallet address</p>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={settings.showWallet}\n                              onChange={(e) => handleSettingChange('showWallet', e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Activity Status</h4>\n                            <p className=\"text-sm text-gray-600\">Show when you're active on the platform</p>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={settings.showActivity}\n                              onChange={(e) => handleSettingChange('showActivity', e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Notifications Settings */}\n                {activeTab === 'notifications' && (\n                  <div className=\"p-6\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Notification Settings</h2>\n\n                    <div className=\"space-y-6\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">Email Notifications</h4>\n                          <p className=\"text-sm text-gray-600\">Receive important updates via email</p>\n                        </div>\n                        <label className=\"relative inline-flex items-center cursor-pointer\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.emailNotifications}\n                            onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}\n                            className=\"sr-only peer\"\n                          />\n                          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        </label>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">Push Notifications</h4>\n                          <p className=\"text-sm text-gray-600\">Get instant notifications in your browser</p>\n                        </div>\n                        <label className=\"relative inline-flex items-center cursor-pointer\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.pushNotifications}\n                            onChange={(e) => handleSettingChange('pushNotifications', e.target.checked)}\n                            className=\"sr-only peer\"\n                          />\n                          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        </label>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">Reward Notifications</h4>\n                          <p className=\"text-sm text-gray-600\">Get notified when you earn $MLIFE tokens</p>\n                        </div>\n                        <label className=\"relative inline-flex items-center cursor-pointer\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.rewardNotifications}\n                            onChange={(e) => handleSettingChange('rewardNotifications', e.target.checked)}\n                            className=\"sr-only peer\"\n                          />\n                          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        </label>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">Community Notifications</h4>\n                          <p className=\"text-sm text-gray-600\">Updates about community events and discussions</p>\n                        </div>\n                        <label className=\"relative inline-flex items-center cursor-pointer\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.communityNotifications}\n                            onChange={(e) => handleSettingChange('communityNotifications', e.target.checked)}\n                            className=\"sr-only peer\"\n                          />\n                          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        </label>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">Marketing Emails</h4>\n                          <p className=\"text-sm text-gray-600\">Receive promotional content and product updates</p>\n                        </div>\n                        <label className=\"relative inline-flex items-center cursor-pointer\">\n                          <input\n                            type=\"checkbox\"\n                            checked={settings.marketingEmails}\n                            onChange={(e) => handleSettingChange('marketingEmails', e.target.checked)}\n                            className=\"sr-only peer\"\n                          />\n                          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                        </label>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Preferences Settings */}\n                {activeTab === 'preferences' && (\n                  <div className=\"p-6\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">App Preferences</h2>\n\n                    <div className=\"space-y-6\">\n                      <div className=\"grid md:grid-cols-2 gap-6\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Language\n                          </label>\n                          <select\n                            value={settings.language}\n                            onChange={(e) => handleSettingChange('language', e.target.value)}\n                            className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          >\n                            <option value=\"en\">English</option>\n                            <option value=\"es\">Español</option>\n                            <option value=\"fr\">Français</option>\n                            <option value=\"de\">Deutsch</option>\n                            <option value=\"zh\">中文</option>\n                            <option value=\"ja\">日本語</option>\n                          </select>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Currency\n                          </label>\n                          <select\n                            value={settings.currency}\n                            onChange={(e) => handleSettingChange('currency', e.target.value)}\n                            className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          >\n                            <option value=\"usd\">USD ($)</option>\n                            <option value=\"eur\">EUR (€)</option>\n                            <option value=\"gbp\">GBP (£)</option>\n                            <option value=\"eth\">ETH (Ξ)</option>\n                            <option value=\"btc\">BTC (₿)</option>\n                          </select>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Time Zone\n                          </label>\n                          <select\n                            value={settings.timezone}\n                            onChange={(e) => handleSettingChange('timezone', e.target.value)}\n                            className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          >\n                            <option value=\"UTC\">UTC</option>\n                            <option value=\"EST\">Eastern Time (EST)</option>\n                            <option value=\"PST\">Pacific Time (PST)</option>\n                            <option value=\"CET\">Central European Time (CET)</option>\n                            <option value=\"JST\">Japan Standard Time (JST)</option>\n                            <option value=\"CST\">China Standard Time (CST)</option>\n                          </select>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Theme\n                          </label>\n                          <select\n                            value={settings.theme}\n                            onChange={(e) => handleSettingChange('theme', e.target.value)}\n                            className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          >\n                            <option value=\"light\">Light</option>\n                            <option value=\"dark\">Dark</option>\n                            <option value=\"auto\">Auto (System)</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Security Settings */}\n                {activeTab === 'security' && (\n                  <div className=\"p-6\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Security Settings</h2>\n\n                    <div className=\"space-y-8\">\n                      {/* Password Change */}\n                      <div>\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Change Password</h3>\n                        <div className=\"space-y-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                              Current Password\n                            </label>\n                            <div className=\"relative\">\n                              <input\n                                type={showPassword ? 'text' : 'password'}\n                                value={passwordData.currentPassword}\n                                onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}\n                                className=\"w-full p-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                placeholder=\"Enter current password\"\n                              />\n                              <button\n                                type=\"button\"\n                                onClick={() => setShowPassword(!showPassword)}\n                                className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                              >\n                                {showPassword ? (\n                                  <EyeOff className=\"w-5 h-5 text-gray-400\" />\n                                ) : (\n                                  <Eye className=\"w-5 h-5 text-gray-400\" />\n                                )}\n                              </button>\n                            </div>\n                          </div>\n\n                          <div className=\"grid md:grid-cols-2 gap-4\">\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                New Password\n                              </label>\n                              <input\n                                type=\"password\"\n                                value={passwordData.newPassword}\n                                onChange={(e) => handlePasswordChange('newPassword', e.target.value)}\n                                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                placeholder=\"Enter new password\"\n                              />\n                            </div>\n\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Confirm New Password\n                              </label>\n                              <input\n                                type=\"password\"\n                                value={passwordData.confirmPassword}\n                                onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}\n                                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                placeholder=\"Confirm new password\"\n                              />\n                            </div>\n                          </div>\n\n                          <button\n                            onClick={handlePasswordUpdate}\n                            disabled={isSaving || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}\n                            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n                          >\n                            Update Password\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Two-Factor Authentication */}\n                      <div className=\"border-t border-gray-200 pt-6\">\n                        <div className=\"flex items-center justify-between mb-4\">\n                          <div>\n                            <h3 className=\"text-lg font-medium text-gray-900\">Two-Factor Authentication</h3>\n                            <p className=\"text-sm text-gray-600\">Add an extra layer of security to your account</p>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={settings.twoFactorEnabled}\n                              onChange={(e) => handleSettingChange('twoFactorEnabled', e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n                        {settings.twoFactorEnabled && (\n                          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                            <div className=\"flex items-center\">\n                              <Smartphone className=\"w-5 h-5 text-green-600 mr-2\" />\n                              <span className=\"text-green-800 font-medium\">Two-factor authentication is enabled</span>\n                            </div>\n                            <p className=\"text-green-700 text-sm mt-1\">\n                              Your account is protected with 2FA. You'll need your authenticator app to sign in.\n                            </p>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Login Alerts */}\n                      <div className=\"border-t border-gray-200 pt-6\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h3 className=\"text-lg font-medium text-gray-900\">Login Alerts</h3>\n                            <p className=\"text-sm text-gray-600\">Get notified of new sign-ins to your account</p>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={settings.loginAlerts}\n                              onChange={(e) => handleSettingChange('loginAlerts', e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n                      </div>\n\n                      {/* Session Timeout */}\n                      <div className=\"border-t border-gray-200 pt-6\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                            Session Timeout (minutes)\n                          </label>\n                          <select\n                            value={settings.sessionTimeout}\n                            onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}\n                            className=\"w-full max-w-xs p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          >\n                            <option value={15}>15 minutes</option>\n                            <option value={30}>30 minutes</option>\n                            <option value={60}>1 hour</option>\n                            <option value={120}>2 hours</option>\n                            <option value={480}>8 hours</option>\n                          </select>\n                          <p className=\"text-sm text-gray-600 mt-1\">\n                            You'll be automatically logged out after this period of inactivity\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Data & Privacy Settings */}\n                {activeTab === 'data' && (\n                  <div className=\"p-6\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Data & Privacy</h2>\n\n                    <div className=\"space-y-8\">\n                      {/* Data Export */}\n                      <div>\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Export Your Data</h3>\n                        <p className=\"text-gray-600 mb-4\">\n                          Download a copy of your data including profile information, transaction history, and settings.\n                        </p>\n                        <button\n                          onClick={handleExportData}\n                          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center\"\n                        >\n                          <Download className=\"w-4 h-4 mr-2\" />\n                          Export Data\n                        </button>\n                      </div>\n\n                      {/* Data Retention */}\n                      <div className=\"border-t border-gray-200 pt-6\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Data Retention</h3>\n                        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                          <h4 className=\"font-medium text-blue-900 mb-2\">How long we keep your data:</h4>\n                          <ul className=\"text-blue-800 text-sm space-y-1\">\n                            <li>• Profile data: Until you delete your account</li>\n                            <li>• Transaction history: 7 years (regulatory requirement)</li>\n                            <li>• Activity logs: 2 years</li>\n                            <li>• Support conversations: 3 years</li>\n                          </ul>\n                        </div>\n                      </div>\n\n                      {/* Account Deletion */}\n                      <div className=\"border-t border-gray-200 pt-6\">\n                        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Delete Account</h3>\n                        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-4\">\n                          <div className=\"flex items-start\">\n                            <AlertTriangle className=\"w-5 h-5 text-red-600 mr-2 mt-0.5\" />\n                            <div>\n                              <h4 className=\"font-medium text-red-900 mb-2\">Warning: This action cannot be undone</h4>\n                              <ul className=\"text-red-800 text-sm space-y-1\">\n                                <li>• All your data will be permanently deleted</li>\n                                <li>• Your $MLIFE tokens will be lost</li>\n                                <li>• Property NFTs will be transferred to a recovery wallet</li>\n                                <li>• You won't be able to recover your account</li>\n                              </ul>\n                            </div>\n                          </div>\n                        </div>\n\n                        {!showDeleteConfirm ? (\n                          <button\n                            onClick={handleDeleteAccount}\n                            className=\"bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors flex items-center\"\n                          >\n                            <Trash2 className=\"w-4 h-4 mr-2\" />\n                            Delete Account\n                          </button>\n                        ) : (\n                          <div className=\"space-y-4\">\n                            <p className=\"text-gray-900 font-medium\">\n                              Are you absolutely sure you want to delete your account?\n                            </p>\n                            <div className=\"flex space-x-3\">\n                              <button\n                                onClick={handleDeleteAccount}\n                                disabled={isSaving}\n                                className=\"bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center\"\n                              >\n                                {isSaving ? (\n                                  <>\n                                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                                    Deleting...\n                                  </>\n                                ) : (\n                                  'Yes, Delete My Account'\n                                )}\n                              </button>\n                              <button\n                                onClick={() => setShowDeleteConfirm(false)}\n                                className=\"bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-400 transition-colors\"\n                              >\n                                Cancel\n                              </button>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Save Button */}\n                {activeTab !== 'security' && activeTab !== 'data' && (\n                  <div className=\"px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-xl\">\n                    <div className=\"flex justify-end\">\n                      <button\n                        onClick={handleSaveSettings}\n                        disabled={isSaving}\n                        className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n                      >\n                        {isSaving ? (\n                          <>\n                            <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                            Saving...\n                          </>\n                        ) : (\n                          <>\n                            <Save className=\"w-4 h-4 mr-2\" />\n                            Save Changes\n                          </>\n                        )}\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAsCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,SAAwF;IAC1H,MAAM,CAAC,UAAU,YAAY,GAAG,SAAS;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,SAAS;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,SAAS;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,SAA6D;IAEnG,MAAM,CAAC,UAAU,YAAY,GAAG,SAAuB;QACrD,aAAa;QACb,KAAK;QACL,UAAU;QACV,SAAS;QACT,mBAAmB;QACnB,WAAW;QACX,YAAY;QACZ,cAAc;QACd,oBAAoB;QACpB,mBAAmB;QACnB,qBAAqB;QACrB,wBAAwB;QACxB,iBAAiB;QACjB,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,SAAS;QAC/C,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wBAAwB;YACxB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,aAAa,KAAK,IAAI,IAAI;oBAC1B,KAAK,KAAK,GAAG,IAAI;oBACjB,UAAU,KAAK,QAAQ,IAAI;oBAC3B,SAAS,KAAK,OAAO,IAAI;gBAC3B,CAAC;QACH;IACF;IAEA,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,sBAAsB,CAAC,KAAyB;QACpD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC,KAAgC;QAC5D,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,YAAY;QACZ,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,eAAe;gBAAE,MAAM;gBAAW,MAAM;YAA+B;YACvE,WAAW,IAAM,eAAe,OAAO;QACzC,EAAE,OAAO,OAAO;YACd,eAAe;gBAAE,MAAM;gBAAS,MAAM;YAA6C;YACnF,WAAW,IAAM,eAAe,OAAO;QACzC,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,aAAa,WAAW,KAAK,aAAa,eAAe,EAAE;YAC7D,eAAe;gBAAE,MAAM;gBAAS,MAAM;YAA8B;YACpE,WAAW,IAAM,eAAe,OAAO;YACvC;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,iBAAiB,aAAa,eAAe;oBAC7C,aAAa,aAAa,WAAW;gBACvC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,eAAe;gBAAE,MAAM;gBAAW,MAAM;YAAiC;YACzE,gBAAgB;gBAAE,iBAAiB;gBAAI,aAAa;gBAAI,iBAAiB;YAAG;YAC5E,WAAW,IAAM,eAAe,OAAO;QACzC,EAAE,OAAO,OAAY;YACnB,eAAe;gBAAE,MAAM;gBAAS,MAAM,MAAM,OAAO,IAAI;YAA+C;YACtG,WAAW,IAAM,eAAe,OAAO;QACzC,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,uBAAuB;QACvB,MAAM,OAAO;YACX,MAAM;YACN,UAAU;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,MAAM,MAAM;SAAG,EAAE;YAAE,MAAM;QAAmB;QAClF,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,AAAC,mBAAyD,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;QACvE,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,mBAAmB;YACtB,qBAAqB;YACrB;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;gBACV;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,eAAe;gBAAE,MAAM;gBAAW,MAAM;YAAqE;YAC7G,qBAAqB;YAErB,sCAAsC;YACtC,WAAW;gBACT,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,OAAY;YACnB,eAAe;gBAAE,MAAM;gBAAS,MAAM,MAAM,OAAO,IAAI;YAAoD;QAC7G,SAAU;YACR,YAAY;QACd;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM;QAAK;QAC9C;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM;QAAO;QAChD;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM;QAAK;QAC1D;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM;QAAM;QACvD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAK;QAChD;YAAE,IAAI;YAAQ,OAAO;YAAkB,MAAM;QAAS;KACvD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,6BACC,6LAAC;4BAAI,WAAW,AAAC,yCAIhB,OAHC,YAAY,IAAI,KAAK,YACjB,uDACA;;gCAEH,YAAY,IAAI,KAAK,0BACpB,6LAAC;oCAAM,WAAU;;;;;yDAEjB,6LAAC;oCAAc,WAAU;;;;;;gCAE1B,YAAY,IAAI;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC;4CACT,MAAM,OAAO,IAAI,IAAI;4CACrB,qBACE,6LAAC;gDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,AAAC,uFAIX,OAHC,cAAc,IAAI,EAAE,GAChB,8BACA;;kEAGN,6LAAC;wDAAK,WAAU;;;;;;oDACf,IAAI,KAAK;;+CATL,IAAI,EAAE;;;;;wCAYjB;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;4CAEZ,cAAc,2BACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6LAAC;wEACC,MAAK;wEACL,OAAO,SAAS,WAAW;wEAC3B,UAAU,CAAC,IAAM,oBAAoB,eAAe,EAAE,MAAM,CAAC,KAAK;wEAClE,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAIhB,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6LAAC;wEACC,OAAO,SAAS,GAAG;wEACnB,UAAU,CAAC,IAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;wEAC1D,MAAM;wEACN,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAIhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,6LAAC;gFACC,MAAK;gFACL,OAAO,SAAS,QAAQ;gFACxB,UAAU,CAAC,IAAM,oBAAoB,YAAY,EAAE,MAAM,CAAC,KAAK;gFAC/D,WAAU;gFACV,aAAY;;;;;;;;;;;;kFAIhB,6LAAC;;0FACC,6LAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAGhE,6LAAC;gFACC,MAAK;gFACL,OAAO,SAAS,OAAO;gFACvB,UAAU,CAAC,IAAM,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK;gFAC9D,WAAU;gFACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CASvB,cAAc,2BACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,6LAAC;wEACC,OAAO,SAAS,iBAAiB;wEACjC,UAAU,CAAC,IAAM,oBAAoB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wEACxE,WAAU;;0FAEV,6LAAC;gFAAO,OAAM;0FAAS;;;;;;0FACvB,6LAAC;gFAAO,OAAM;0FAAU;;;;;;0FACxB,6LAAC;gFAAO,OAAM;0FAAU;;;;;;;;;;;;;;;;;;0EAI5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAoC;;;;;;kFAElD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAA4B;;;;;;kGAC1C,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;0FAEvC,6LAAC;gFAAM,WAAU;;kGACf,6LAAC;wFACC,MAAK;wFACL,SAAS,SAAS,SAAS;wFAC3B,UAAU,CAAC,IAAM,oBAAoB,aAAa,EAAE,MAAM,CAAC,OAAO;wFAClE,WAAU;;;;;;kGAEZ,6LAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;kFAInB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAA4B;;;;;;kGAC1C,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;0FAEvC,6LAAC;gFAAM,WAAU;;kGACf,6LAAC;wFACC,MAAK;wFACL,SAAS,SAAS,UAAU;wFAC5B,UAAU,CAAC,IAAM,oBAAoB,cAAc,EAAE,MAAM,CAAC,OAAO;wFACnE,WAAU;;;;;;kGAEZ,6LAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;kFAInB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAA4B;;;;;;kGAC1C,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;0FAEvC,6LAAC;gFAAM,WAAU;;kGACf,6LAAC;wFACC,MAAK;wFACL,SAAS,SAAS,YAAY;wFAC9B,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,OAAO;wFACrE,WAAU;;;;;;kGAEZ,6LAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAS1B,cAAc,iCACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAA4B;;;;;;0FAC1C,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAM,WAAU;;0FACf,6LAAC;gFACC,MAAK;gFACL,SAAS,SAAS,kBAAkB;gFACpC,UAAU,CAAC,IAAM,oBAAoB,sBAAsB,EAAE,MAAM,CAAC,OAAO;gFAC3E,WAAU;;;;;;0FAEZ,6LAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;;0EAInB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAA4B;;;;;;0FAC1C,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAM,WAAU;;0FACf,6LAAC;gFACC,MAAK;gFACL,SAAS,SAAS,iBAAiB;gFACnC,UAAU,CAAC,IAAM,oBAAoB,qBAAqB,EAAE,MAAM,CAAC,OAAO;gFAC1E,WAAU;;;;;;0FAEZ,6LAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;;0EAInB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAA4B;;;;;;0FAC1C,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAM,WAAU;;0FACf,6LAAC;gFACC,MAAK;gFACL,SAAS,SAAS,mBAAmB;gFACrC,UAAU,CAAC,IAAM,oBAAoB,uBAAuB,EAAE,MAAM,CAAC,OAAO;gFAC5E,WAAU;;;;;;0FAEZ,6LAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;;0EAInB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAA4B;;;;;;0FAC1C,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAM,WAAU;;0FACf,6LAAC;gFACC,MAAK;gFACL,SAAS,SAAS,sBAAsB;gFACxC,UAAU,CAAC,IAAM,oBAAoB,0BAA0B,EAAE,MAAM,CAAC,OAAO;gFAC/E,WAAU;;;;;;0FAEZ,6LAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;;0EAInB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAA4B;;;;;;0FAC1C,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAM,WAAU;;0FACf,6LAAC;gFACC,MAAK;gFACL,SAAS,SAAS,eAAe;gFACjC,UAAU,CAAC,IAAM,oBAAoB,mBAAmB,EAAE,MAAM,CAAC,OAAO;gFACxE,WAAU;;;;;;0FAEZ,6LAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAQxB,cAAc,+BACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,6LAAC;4EACC,OAAO,SAAS,QAAQ;4EACxB,UAAU,CAAC,IAAM,oBAAoB,YAAY,EAAE,MAAM,CAAC,KAAK;4EAC/D,WAAU;;8FAEV,6LAAC;oFAAO,OAAM;8FAAK;;;;;;8FACnB,6LAAC;oFAAO,OAAM;8FAAK;;;;;;8FACnB,6LAAC;oFAAO,OAAM;8FAAK;;;;;;8FACnB,6LAAC;oFAAO,OAAM;8FAAK;;;;;;8FACnB,6LAAC;oFAAO,OAAM;8FAAK;;;;;;8FACnB,6LAAC;oFAAO,OAAM;8FAAK;;;;;;;;;;;;;;;;;;8EAIvB,6LAAC;;sFACC,6LAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,6LAAC;4EACC,OAAO,SAAS,QAAQ;4EACxB,UAAU,CAAC,IAAM,oBAAoB,YAAY,EAAE,MAAM,CAAC,KAAK;4EAC/D,WAAU;;8FAEV,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;;;;;;;;;;;;;8EAIxB,6LAAC;;sFACC,6LAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,6LAAC;4EACC,OAAO,SAAS,QAAQ;4EACxB,UAAU,CAAC,IAAM,oBAAoB,YAAY,EAAE,MAAM,CAAC,KAAK;4EAC/D,WAAU;;8FAEV,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;8FACpB,6LAAC;oFAAO,OAAM;8FAAM;;;;;;;;;;;;;;;;;;8EAIxB,6LAAC;;sFACC,6LAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,6LAAC;4EACC,OAAO,SAAS,KAAK;4EACrB,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;4EAC5D,WAAU;;8FAEV,6LAAC;oFAAO,OAAM;8FAAQ;;;;;;8FACtB,6LAAC;oFAAO,OAAM;8FAAO;;;;;;8FACrB,6LAAC;oFAAO,OAAM;8FAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAShC,cAAc,4BACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAM,WAAU;kGAA+C;;;;;;kGAGhE,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGACC,MAAM,eAAe,SAAS;gGAC9B,OAAO,aAAa,eAAe;gGACnC,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gGACvE,WAAU;gGACV,aAAY;;;;;;0GAEd,6LAAC;gGACC,MAAK;gGACL,SAAS,IAAM,gBAAgB,CAAC;gGAChC,WAAU;0GAET,6BACC,6LAAC;oGAAO,WAAU;;;;;yHAElB,6LAAC;oGAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0FAMvB,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;;0GACC,6LAAC;gGAAM,WAAU;0GAA+C;;;;;;0GAGhE,6LAAC;gGACC,MAAK;gGACL,OAAO,aAAa,WAAW;gGAC/B,UAAU,CAAC,IAAM,qBAAqB,eAAe,EAAE,MAAM,CAAC,KAAK;gGACnE,WAAU;gGACV,aAAY;;;;;;;;;;;;kGAIhB,6LAAC;;0GACC,6LAAC;gGAAM,WAAU;0GAA+C;;;;;;0GAGhE,6LAAC;gGACC,MAAK;gGACL,OAAO,aAAa,eAAe;gGACnC,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gGACvE,WAAU;gGACV,aAAY;;;;;;;;;;;;;;;;;;0FAKlB,6LAAC;gFACC,SAAS;gFACT,UAAU,YAAY,CAAC,aAAa,eAAe,IAAI,CAAC,aAAa,WAAW,IAAI,CAAC,aAAa,eAAe;gFACjH,WAAU;0FACX;;;;;;;;;;;;;;;;;;0EAOL,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAoC;;;;;;kGAClD,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;0FAEvC,6LAAC;gFAAM,WAAU;;kGACf,6LAAC;wFACC,MAAK;wFACL,SAAS,SAAS,gBAAgB;wFAClC,UAAU,CAAC,IAAM,oBAAoB,oBAAoB,EAAE,MAAM,CAAC,OAAO;wFACzE,WAAU;;;;;;kGAEZ,6LAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;oEAGlB,SAAS,gBAAgB,kBACxB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAW,WAAU;;;;;;kGACtB,6LAAC;wFAAK,WAAU;kGAA6B;;;;;;;;;;;;0FAE/C,6LAAC;gFAAE,WAAU;0FAA8B;;;;;;;;;;;;;;;;;;0EAQjD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FAAoC;;;;;;8FAClD,6LAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;sFAEvC,6LAAC;4EAAM,WAAU;;8FACf,6LAAC;oFACC,MAAK;oFACL,SAAS,SAAS,WAAW;oFAC7B,UAAU,CAAC,IAAM,oBAAoB,eAAe,EAAE,MAAM,CAAC,OAAO;oFACpE,WAAU;;;;;;8FAEZ,6LAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0EAMrB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;;sFACC,6LAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,6LAAC;4EACC,OAAO,SAAS,cAAc;4EAC9B,UAAU,CAAC,IAAM,oBAAoB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4EAC9E,WAAU;;8FAEV,6LAAC;oFAAO,OAAO;8FAAI;;;;;;8FACnB,6LAAC;oFAAO,OAAO;8FAAI;;;;;;8FACnB,6LAAC;oFAAO,OAAO;8FAAI;;;;;;8FACnB,6LAAC;oFAAO,OAAO;8FAAK;;;;;;8FACpB,6LAAC;oFAAO,OAAO;8FAAK;;;;;;;;;;;;sFAEtB,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAUnD,cAAc,wBACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,6LAAC;wEAAE,WAAU;kFAAqB;;;;;;kFAGlC,6LAAC;wEACC,SAAS;wEACT,WAAU;;0FAEV,6LAAC;gFAAS,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;0EAMzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FAAiC;;;;;;0FAC/C,6LAAC;gFAAG,WAAU;;kGACZ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;kGACJ,6LAAC;kGAAG;;;;;;;;;;;;;;;;;;;;;;;;0EAMV,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAyC;;;;;;kFACvD,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAc,WAAU;;;;;;8FACzB,6LAAC;;sGACC,6LAAC;4FAAG,WAAU;sGAAgC;;;;;;sGAC9C,6LAAC;4FAAG,WAAU;;8GACZ,6LAAC;8GAAG;;;;;;8GACJ,6LAAC;8GAAG;;;;;;8GACJ,6LAAC;8GAAG;;;;;;8GACJ,6LAAC;8GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oEAMX,CAAC,kCACA,6LAAC;wEACC,SAAS;wEACT,WAAU;;0FAEV,6LAAC;gFAAO,WAAU;;;;;;4EAAiB;;;;;;6FAIrC,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAA4B;;;;;;0FAGzC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFACC,SAAS;wFACT,UAAU;wFACV,WAAU;kGAET,yBACC;;8GACE,6LAAC;oGAAI,WAAU;;;;;;gGAA0F;;2GAI3G;;;;;;kGAGJ,6LAAC;wFACC,SAAS,IAAM,qBAAqB;wFACpC,WAAU;kGACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAYd,cAAc,cAAc,cAAc,wBACzC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,SAAS;wDACT,UAAU;wDACV,WAAU;kEAET,yBACC;;8EACE,6LAAC;oEAAI,WAAU;;;;;;gEAA0F;;yFAI3G;;8EACE,6LAAC;oEAAK,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe7D;GAv3BwB;;QACI,kIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}