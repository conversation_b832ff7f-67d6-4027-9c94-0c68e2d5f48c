{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Building2, Mail, Lock, Wallet, ArrowLeft, AlertCircle } from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function LoginPage() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [loginMethod, setLoginMethod] = useState<'email' | 'wallet'>('email');\n  const [error, setError] = useState('');\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n\n  const router = useRouter();\n  const { login, loginWithWallet } = useAuth();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value,\n    }));\n    setError(''); // Clear error when user types\n  };\n\n  const handleEmailLogin = async (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    try {\n      await login(formData.email, formData.password);\n      router.push('/dashboard');\n    } catch (error: any) {\n      setError(error.message || 'Login failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleWalletConnect = async () => {\n    setIsLoading(true);\n    setError('');\n\n    try {\n      // Check if MetaMask is installed\n      if (typeof window.ethereum !== 'undefined') {\n        // Request account access\n        const accounts = await window.ethereum.request({\n          method: 'eth_requestAccounts',\n        });\n\n        if (accounts.length > 0) {\n          const result = await loginWithWallet(accounts[0]);\n\n          if (result.isNewUser) {\n            // Redirect to profile setup for new users\n            router.push('/dashboard?welcome=true');\n          } else {\n            router.push('/dashboard');\n          }\n        }\n      } else {\n        setError('Please install MetaMask to connect your wallet');\n      }\n    } catch (error: any) {\n      console.error('Error connecting wallet:', error);\n      setError(error.message || 'Wallet connection failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full\">\n        {/* Back to Home */}\n        <Link\n          href=\"/\"\n          className=\"inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors mb-8\"\n        >\n          <ArrowLeft className=\"w-4 h-4 mr-2\" />\n          Back to Home\n        </Link>\n\n        {/* Login Card */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\n          {/* Header */}\n          <div className=\"text-center mb-8\">\n            <div className=\"flex items-center justify-center space-x-2 mb-4\">\n              <img\n                src=\"/logo/logo.png\"\n                alt=\"ManageLife\"\n                className=\"h-10 w-auto\"\n              />\n            \n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Welcome Back</h1>\n            <p className=\"text-gray-600\">Sign in to your account to continue</p>\n          </div>\n\n          {/* Login Method Toggle */}\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\">\n              <AlertCircle className=\"w-5 h-5 text-red-600 mr-3 flex-shrink-0\" />\n              <p className=\"text-red-700 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Login Method Toggle */}\n          <div className=\"flex bg-gray-100 rounded-lg p-1 mb-6\">\n            <button\n              onClick={() => {\n                setLoginMethod('email');\n                setError('');\n              }}\n              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                loginMethod === 'email'\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <Mail className=\"w-4 h-4 inline mr-2\" />\n              Email\n            </button>\n            <button\n              onClick={() => {\n                setLoginMethod('wallet');\n                setError('');\n              }}\n              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n                loginMethod === 'wallet'\n                  ? 'bg-white text-blue-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <Wallet className=\"w-4 h-4 inline mr-2\" />\n              Wallet\n            </button>\n          </div>\n\n          {/* Email Login Form */}\n          {loginMethod === 'email' && (\n            <form onSubmit={handleEmailLogin} className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email Address\n                </label>\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    required\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Enter your email\"\n                  />\n                </div>\n              </div>\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type=\"password\"\n                    required\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Enter your password\"\n                  />\n                </div>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <label className=\"flex items-center\">\n                  <input type=\"checkbox\" className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\" />\n                  <span className=\"ml-2 text-sm text-gray-600\">Remember me</span>\n                </label>\n                <Link href=\"/auth/forgot-password\" className=\"text-sm text-blue-600 hover:text-blue-700\">\n                  Forgot password?\n                </Link>\n              </div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50\"\n              >\n                {isLoading ? 'Signing In...' : 'Sign In'}\n              </button>\n            </form>\n          )}\n\n          {/* Wallet Connection */}\n          {loginMethod === 'wallet' && (\n            <div className=\"space-y-4\">\n              <div className=\"text-center py-8\">\n                <Wallet className=\"w-16 h-16 text-blue-600 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Connect Your Wallet</h3>\n                <p className=\"text-gray-600 mb-6\">\n                  Connect your MetaMask wallet to access your ManageLife account\n                </p>\n                <button\n                  onClick={handleWalletConnect}\n                  disabled={isLoading}\n                  className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50\"\n                >\n                  {isLoading ? 'Connecting...' : 'Connect MetaMask'}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Sign Up Link */}\n          <div className=\"text-center mt-6 pt-6 border-t border-gray-200\">\n            <p className=\"text-gray-600\">\n              Don't have an account?{' '}\n              <Link href=\"/auth/register\" className=\"text-blue-600 hover:text-blue-700 font-semibold\">\n                Sign up\n              </Link>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzC,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;QACD,SAAS,KAAK,8BAA8B;IAC9C;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ;YAC7C,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,aAAa;QACb,SAAS;QAET,IAAI;YACF,iCAAiC;YACjC,IAAI,OAAO,OAAO,QAAQ,KAAK,aAAa;gBAC1C,yBAAyB;gBACzB,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;oBAC7C,QAAQ;gBACV;gBAEA,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,MAAM,SAAS,MAAM,gBAAgB,QAAQ,CAAC,EAAE;oBAEhD,IAAI,OAAO,SAAS,EAAE;wBACpB,0CAA0C;wBAC1C,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;gBACF;YACF,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAKxC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;8CAId,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;wBAK9B,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,eAAe;wCACf,SAAS;oCACX;oCACA,WAAW,CAAC,kEAAkE,EAC5E,gBAAgB,UACZ,qCACA,qCACJ;;sDAEF,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;8CAG1C,8OAAC;oCACC,SAAS;wCACP,eAAe;wCACf,SAAS;oCACX;oCACA,WAAW,CAAC,kEAAkE,EAC5E,gBAAgB,WACZ,qCACA,qCACJ;;sDAEF,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;;;;;;;wBAM7C,gBAAgB,yBACf,8OAAC;4BAAK,UAAU;4BAAkB,WAAU;;8CAC1C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,MAAK;oDAAW,WAAU;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAwB,WAAU;sDAA4C;;;;;;;;;;;;8CAI3F,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,YAAY,kBAAkB;;;;;;;;;;;;wBAMpC,gBAAgB,0BACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAgB;oCACJ;kDACvB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAiB,WAAU;kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStG", "debugId": null}}]}