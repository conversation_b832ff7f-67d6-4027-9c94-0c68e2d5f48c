import { UserRole } from '@/types';

// Application constants
export const APP_NAME = 'ManageLife';
export const APP_DESCRIPTION = 'Real Estate Tokenization Platform';

// Supported chains
export const SUPPORTED_CHAINS = {
  ETHEREUM: 1,
  POLYGON: 137,
  SEPOLIA: 11155111, // Testnet
} as const;

// Contract addresses (placeholder - replace with actual deployed contracts)
export const CONTRACT_ADDRESSES = {
  NFTi: '******************************************',
  NFTr: '******************************************',
  MLIFE_TOKEN: '******************************************',
  MARKETPLACE: '******************************************',
  REWARDS: '******************************************',
} as const;

// User roles
export const USER_ROLES: UserRole[] = [
  'homeowner',
  'renter',
  'buyer',
  'portfolio-manager',
  'community-member',
];

// Role permissions
export const ROLE_PERMISSIONS = {
  homeowner: [
    'create_property',
    'tokenize_property',
    'list_property',
    'manage_leases',
    'view_analytics',
    'request_maintenance',
  ],
  renter: [
    'browse_properties',
    'create_lease',
    'pay_rent',
    'request_maintenance',
    'view_lease_history',
  ],
  buyer: [
    'browse_marketplace',
    'purchase_property',
    'make_offers',
    'view_property_details',
    'save_favorites',
  ],
  'portfolio-manager': [
    'manage_multiple_properties',
    'view_portfolio_analytics',
    'manage_tenants',
    'handle_maintenance',
    'generate_reports',
  ],
  'community-member': [
    'view_events',
    'participate_discussions',
    'earn_rewards',
    'refer_users',
    'access_resources',
  ],
} as const;

// Property types
export const PROPERTY_TYPES = [
  { value: 'house', label: 'House' },
  { value: 'apartment', label: 'Apartment' },
  { value: 'condo', label: 'Condominium' },
  { value: 'commercial', label: 'Commercial' },
] as const;

// Property statuses
export const PROPERTY_STATUSES = [
  { value: 'available', label: 'Available', color: 'green' },
  { value: 'rented', label: 'Rented', color: 'blue' },
  { value: 'sold', label: 'Sold', color: 'gray' },
  { value: 'maintenance', label: 'Under Maintenance', color: 'yellow' },
] as const;

// Currencies
export const CURRENCIES = [
  { value: 'USD', label: 'US Dollar', symbol: '$' },
  { value: 'ETH', label: 'Ethereum', symbol: 'Ξ' },
  { value: 'MLIFE', label: 'ManageLife Token', symbol: '$MLIFE' },
] as const;

// Maintenance categories
export const MAINTENANCE_CATEGORIES = [
  { value: 'plumbing', label: 'Plumbing' },
  { value: 'electrical', label: 'Electrical' },
  { value: 'hvac', label: 'HVAC' },
  { value: 'appliance', label: 'Appliance' },
  { value: 'structural', label: 'Structural' },
  { value: 'other', label: 'Other' },
] as const;

// Maintenance priorities
export const MAINTENANCE_PRIORITIES = [
  { value: 'low', label: 'Low', color: 'green' },
  { value: 'medium', label: 'Medium', color: 'yellow' },
  { value: 'high', label: 'High', color: 'orange' },
  { value: 'urgent', label: 'Urgent', color: 'red' },
] as const;

// Payment statuses
export const PAYMENT_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'yellow' },
  { value: 'paid', label: 'Paid', color: 'green' },
  { value: 'overdue', label: 'Overdue', color: 'red' },
] as const;

// Reward sources
export const REWARD_SOURCES = [
  { value: 'rent_payment', label: 'Rent Payment' },
  { value: 'referral', label: 'Referral' },
  { value: 'community_activity', label: 'Community Activity' },
  { value: 'staking', label: 'Staking' },
  { value: 'marketplace_activity', label: 'Marketplace Activity' },
] as const;

// Event types
export const EVENT_TYPES = [
  { value: 'webinar', label: 'Webinar' },
  { value: 'meetup', label: 'Meetup' },
  { value: 'workshop', label: 'Workshop' },
  { value: 'announcement', label: 'Announcement' },
] as const;

// Social media links
export const SOCIAL_LINKS = {
  TELEGRAM: 'https://t.me/managelife',
  DISCORD: 'https://discord.gg/managelife',
  TWITTER: 'https://twitter.com/managelife',
  LINKEDIN: 'https://linkedin.com/company/managelife',
  MEDIUM: 'https://medium.com/@managelife',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  PROPERTIES: '/api/properties',
  USERS: '/api/users',
  LEASES: '/api/leases',
  MARKETPLACE: '/api/marketplace',
  REWARDS: '/api/rewards',
  MAINTENANCE: '/api/maintenance',
  EVENTS: '/api/events',
  AUTH: '/api/auth',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'managelife_user_preferences',
  WALLET_CONNECTION: 'managelife_wallet_connection',
  THEME: 'managelife_theme',
  LANGUAGE: 'managelife_language',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
} as const;

// File upload
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
  MAX_FILES: 10,
} as const;

// Validation rules
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  DESCRIPTION_MAX_LENGTH: 1000,
  TITLE_MAX_LENGTH: 100,
} as const;

// Theme colors
export const THEME_COLORS = {
  PRIMARY: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    900: '#1e3a8a',
  },
  SECONDARY: {
    50: '#faf5ff',
    100: '#f3e8ff',
    500: '#8b5cf6',
    600: '#7c3aed',
    700: '#6d28d9',
    900: '#4c1d95',
  },
} as const;

// Dashboard navigation items
export const DASHBOARD_NAV_ITEMS = {
  homeowner: [
    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },
    { href: '/dashboard?tab=properties', label: 'My Properties', icon: 'Building', tab: 'properties' },
    { href: '/dashboard?tab=rentals', label: 'Rental Management', icon: 'FileText', tab: 'rentals' },
    { href: '/dashboard?tab=nfts', label: 'My NFTs', icon: 'Coins', tab: 'nfts' },
    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },
    { href: '/dashboard?tab=rewards', label: 'Rewards', icon: 'Gift', tab: 'rewards' },
    { href: '/dashboard?tab=analytics', label: 'Analytics', icon: 'BarChart3', tab: 'analytics' },
  ],
  renter: [
    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },
    { href: '/dashboard?tab=rentals', label: 'My Rental', icon: 'FileText', tab: 'rentals' },
    { href: '/dashboard?tab=payments', label: 'Payments', icon: 'CreditCard', tab: 'payments' },
    { href: '/dashboard?tab=maintenance', label: 'Maintenance', icon: 'Wrench', tab: 'maintenance' },
    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },
    { href: '/dashboard?tab=rewards', label: 'Rewards', icon: 'Gift', tab: 'rewards' },
  ],
  buyer: [
    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },
    { href: '/dashboard?tab=marketplace', label: 'Browse Properties', icon: 'Search', tab: 'marketplace' },
    { href: '/dashboard?tab=favorites', label: 'Favorites', icon: 'Heart', tab: 'favorites' },
    { href: '/dashboard?tab=offers', label: 'My Offers', icon: 'HandCoins', tab: 'offers' },
    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },
    { href: '/dashboard?tab=purchases', label: 'Purchases', icon: 'ShoppingCart', tab: 'purchases' },
  ],
  'portfolio-manager': [
    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },
    { href: '/dashboard?tab=portfolio', label: 'Portfolio', icon: 'Building2', tab: 'portfolio' },
    { href: '/dashboard?tab=tenants', label: 'Tenants', icon: 'Users', tab: 'tenants' },
    { href: '/dashboard?tab=maintenance', label: 'Maintenance', icon: 'Wrench', tab: 'maintenance' },
    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },
    { href: '/dashboard?tab=reports', label: 'Reports', icon: 'FileBarChart', tab: 'reports' },
    { href: '/dashboard?tab=analytics', label: 'Analytics', icon: 'BarChart3', tab: 'analytics' },
  ],
  'community-member': [
    { href: '/dashboard', label: 'Overview', icon: 'Home', tab: 'overview' },
    { href: '/dashboard?tab=community', label: 'Community', icon: 'Users', tab: 'community' },
    { href: '/dashboard?tab=events', label: 'Events', icon: 'Calendar', tab: 'events' },
    { href: '/dashboard?tab=notifications', label: 'Notifications', icon: 'Bell', tab: 'notifications' },
    { href: '/dashboard?tab=rewards', label: 'Rewards', icon: 'Gift', tab: 'rewards' },
    { href: '/dashboard?tab=referrals', label: 'Referrals', icon: 'UserPlus', tab: 'referrals' },
  ],
} as const;

// Utility functions for roles
export function getRoleDisplayName(role: string): string {
  switch (role) {
    case 'homeowner':
      return 'Homeowner';
    case 'renter':
      return 'Renter';
    case 'buyer':
      return 'Buyer';
    case 'portfolio-manager':
      return 'Portfolio Manager';
    case 'community-member':
      return 'Community Member';
    default:
      return role;
  }
}

export function getRoleColor(role: string): string {
  switch (role) {
    case 'homeowner':
      return 'text-blue-600 bg-blue-100';
    case 'renter':
      return 'text-green-600 bg-green-100';
    case 'buyer':
      return 'text-purple-600 bg-purple-100';
    case 'portfolio-manager':
      return 'text-orange-600 bg-orange-100';
    case 'community-member':
      return 'text-pink-600 bg-pink-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
}
